import type React from 'react';

interface H1IconProps {
    className?: string;
    letterClassName?: string;
    levelClassName?: string;
    [key: string]: any;
}

const H1Icon: React.FC<H1IconProps> = ({
    className,
    letterClassName,
    levelClassName,
    ...props
}) => (
    <svg
        width="15"
        height="15"
        viewBox="0 0 15 15"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        {...props}
    >
        <path
            className={levelClassName}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10.95 4.98927C11.2538 4.98927 11.5 5.23551 11.5 5.53927V12.6877H10.4V5.53927C10.4 5.23551 10.6462 4.98927 10.95 4.98927Z"
        />
        <path
            className={levelClassName}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12.9 12.5174C12.9 12.7659 12.6985 12.9674 12.45 12.9674H9.45C9.20147 12.9674 9 12.7659 9 12.5174C9 12.2689 9.20147 12.0674 9.45 12.0674H12.45C12.6985 12.0674 12.9 12.2689 12.9 12.5174Z"
        />
        <path
            className={levelClassName}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.3389 5.2128C11.4661 5.42627 11.3963 5.70249 11.1828 5.82976C10.5947 6.1804 10.1817 6.34855 9.89757 6.42654C9.75473 6.46574 9.64168 6.48294 9.55336 6.48806C9.50912 6.49063 9.47095 6.49017 9.43834 6.48788C9.42203 6.48674 9.40714 6.48514 9.39361 6.48324C9.38684 6.48229 9.38042 6.48127 9.37433 6.48019L9.36546 6.47854L9.36122 6.47769L9.35914 6.47726L9.3571 6.47682C9.11412 6.4246 8.95948 6.18529 9.0117 5.94231C9.0607 5.71437 9.27434 5.56417 9.50121 5.58957C9.51779 5.58861 9.56818 5.58366 9.65933 5.55864C9.84321 5.50817 10.1805 5.36767 10.7219 5.0449C10.9354 4.91762 11.2116 4.99933 11.3389 5.2128Z"
        />
        <path
            className={letterClassName}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.30469 2.50078C8.30469 2.25225 8.50616 2.05078 8.75469 2.05078H12.2547C12.5032 2.05078 12.7047 2.25225 12.7047 2.50078C12.7047 2.74931 12.5032 2.95078 12.2547 2.95078H11.0503V4.00039C11.0336 4.00013 11.0168 4 11 4C10.6368 4 10.2848 4.06052 9.95034 4.17393V2.95078H8.75469C8.50616 2.95078 8.30469 2.74931 8.30469 2.50078ZM7.31541 7.05062H5.05034V2.95078H6.25469C6.50322 2.95078 6.70469 2.74931 6.70469 2.50078C6.70469 2.25225 6.50322 2.05078 6.25469 2.05078H2.75469C2.50616 2.05078 2.30469 2.25225 2.30469 2.50078C2.30469 2.74931 2.50616 2.95078 2.75469 2.95078H3.95034V12.0508H2.75469C2.50616 12.0508 2.30469 12.2523 2.30469 12.5008C2.30469 12.7493 2.50616 12.9508 2.75469 12.9508H6.25469C6.50322 12.9508 6.70469 12.7493 6.70469 12.5008C6.70469 12.2523 6.50322 12.0508 6.25469 12.0508H5.05034V7.95062H7.08824C7.14151 7.63886 7.21802 7.33787 7.31541 7.05062Z"
        />
    </svg>
);

export default H1Icon;
