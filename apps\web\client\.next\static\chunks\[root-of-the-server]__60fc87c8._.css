/* [project]/apps/web/client/src/styles/globals.css [app-client] (css) */
html, body {
  overscroll-behavior: none;
  overflow-x: hidden;
}


/* [project]/packages/ui/src/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-50: oklch(97.1% .013 17.38);
    --color-amber-50: oklch(98.7% .022 95.277);
    --color-teal-50: oklch(98.4% .014 180.72);
    --color-blue-50: oklch(97% .014 254.604);
    --color-zinc-700: oklch(37% .013 285.805);
    --color-zinc-800: oklch(27.4% .006 286.033);
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 1400px;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -.025em;
    --tracking-widest: .1em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-xs: .125rem;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --drop-shadow-xl: 0 9px 7px #0000001a;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-lg: 16px;
    --blur-xl: 24px;
    --aspect-video: 16 / 9;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --animate-accordion-down: accordion-down .2s ease-out;
    --animate-accordion-up: accordion-up .2s ease-out;
    --animate-edit-panel-in: edit-panel-in 1s ease;
    --animate-layer-panel-in: layer-panel-in 1s ease;
    --animate-toolbar-up: toolbar-up 1.25s ease;
    --animate-shine: shine var(--duration) infinite linear;
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  *, :after, :before, ::backdrop, ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  button:not([disabled]), [role="button"]:not([disabled]) {
    cursor: pointer;
  }

  * {
    border-color: hsl(var(--border));
  }

  ::-webkit-scrollbar {
    display: none;
  }

  input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  * {
    border-color: hsl(var(--border));
    outline-color: hsl(var(--ring));
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, hsl(var(--ring)) 50%, transparent);
    }
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

@layer components;

@layer utilities {
  .\@container\/card-header {
    container: card-header / inline-size;
  }

  .pointer-events-auto {
    pointer-events: auto;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .invisible {
    visibility: hidden;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .-top-1 {
    top: calc(var(--spacing) * -1);
  }

  .-top-1\.5 {
    top: calc(var(--spacing) * -1.5);
  }

  .-top-12 {
    top: calc(var(--spacing) * -12);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1 {
    top: calc(var(--spacing) * 1);
  }

  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-3\.5 {
    top: calc(var(--spacing) * 3.5);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-10 {
    top: calc(var(--spacing) * 10);
  }

  .top-\[1px\] {
    top: 1px;
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .top-\[60\%\] {
    top: 60%;
  }

  .top-full {
    top: 100%;
  }

  .-right-1\.5 {
    right: calc(var(--spacing) * -1.5);
  }

  .-right-10 {
    right: calc(var(--spacing) * -10);
  }

  .-right-12 {
    right: calc(var(--spacing) * -12);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-0\.5 {
    right: calc(var(--spacing) * .5);
  }

  .right-1 {
    right: calc(var(--spacing) * 1);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-9 {
    right: calc(var(--spacing) * 9);
  }

  .right-24 {
    right: calc(var(--spacing) * 24);
  }

  .right-\[1px\] {
    right: 1px;
  }

  .right-\[5px\] {
    right: 5px;
  }

  .-bottom-10 {
    bottom: calc(var(--spacing) * -10);
  }

  .-bottom-12 {
    bottom: calc(var(--spacing) * -12);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-0\.5 {
    bottom: calc(var(--spacing) * .5);
  }

  .bottom-2 {
    bottom: calc(var(--spacing) * 2);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .bottom-\[1px\] {
    bottom: 1px;
  }

  .-left-2 {
    left: calc(var(--spacing) * -2);
  }

  .-left-12 {
    left: calc(var(--spacing) * -12);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1 {
    left: calc(var(--spacing) * 1);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-2\.5 {
    left: calc(var(--spacing) * 2.5);
  }

  .left-14 {
    left: calc(var(--spacing) * 14);
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .isolate {
    isolation: isolate;
  }

  .z-0 {
    z-index: 0;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-49 {
    z-index: 49;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[-1\] {
    z-index: -1;
  }

  .z-\[1\] {
    z-index: 1;
  }

  .col-span-3 {
    grid-column: span 3 / span 3;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .m-0 {
    margin: calc(var(--spacing) * 0);
  }

  .m-2 {
    margin: calc(var(--spacing) * 2);
  }

  .m-6 {
    margin: calc(var(--spacing) * 6);
  }

  .m-auto {
    margin: auto;
  }

  .container {
    margin-inline: auto;
    padding-inline: 2rem;
  }

  @media (width >= 40rem) {
    .container {
      max-width: none;
    }
  }

  @media (width >= 1400px) {
    .container {
      max-width: 1400px;
    }
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .-mx-5 {
    margin-inline: calc(var(--spacing) * -5);
  }

  .mx-0 {
    margin-inline: calc(var(--spacing) * 0);
  }

  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-3\.5 {
    margin-inline: calc(var(--spacing) * 3.5);
  }

  .mx-10 {
    margin-inline: calc(var(--spacing) * 10);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-0\.5 {
    margin-block: calc(var(--spacing) * .5);
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .my-5 {
    margin-block: calc(var(--spacing) * 5);
  }

  .prose {
    color: var(--tw-prose-body);
    --tw-prose-body: oklch(37.3% .034 259.733);
    --tw-prose-headings: oklch(21% .034 264.665);
    --tw-prose-lead: oklch(44.6% .03 256.802);
    --tw-prose-links: oklch(21% .034 264.665);
    --tw-prose-bold: oklch(21% .034 264.665);
    --tw-prose-counters: oklch(55.1% .027 264.364);
    --tw-prose-bullets: oklch(87.2% .01 258.338);
    --tw-prose-hr: oklch(92.8% .006 264.531);
    --tw-prose-quotes: oklch(21% .034 264.665);
    --tw-prose-quote-borders: oklch(92.8% .006 264.531);
    --tw-prose-captions: oklch(55.1% .027 264.364);
    --tw-prose-kbd: oklch(21% .034 264.665);
    --tw-prose-kbd-shadows: NaN NaN NaN;
    --tw-prose-code: oklch(21% .034 264.665);
    --tw-prose-pre-code: oklch(92.8% .006 264.531);
    --tw-prose-pre-bg: oklch(27.8% .033 256.848);
    --tw-prose-th-borders: oklch(87.2% .01 258.338);
    --tw-prose-td-borders: oklch(92.8% .006 264.531);
    --tw-prose-invert-body: oklch(87.2% .01 258.338);
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: oklch(70.7% .022 261.325);
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: oklch(70.7% .022 261.325);
    --tw-prose-invert-bullets: oklch(44.6% .03 256.802);
    --tw-prose-invert-hr: oklch(37.3% .034 259.733);
    --tw-prose-invert-quotes: oklch(96.7% .003 264.542);
    --tw-prose-invert-quote-borders: oklch(37.3% .034 259.733);
    --tw-prose-invert-captions: oklch(70.7% .022 261.325);
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: oklch(87.2% .01 258.338);
    --tw-prose-invert-pre-bg: #00000080;
    --tw-prose-invert-th-borders: oklch(44.6% .03 256.802);
    --tw-prose-invert-td-borders: oklch(37.3% .034 259.733);
    max-width: 65ch;
    font-size: 1rem;
    line-height: 1.75;
  }

  .prose :where(p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose :where([class~="lead"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-lead);
    margin-top: 1.2em;
    margin-bottom: 1.2em;
    font-size: 1.25em;
    line-height: 1.6;
  }

  .prose :where(a):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-links);
    font-weight: 500;
    text-decoration: underline;
  }

  .prose :where(strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-bold);
    font-weight: 600;
  }

  .prose :where(a strong):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(blockquote strong):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(thead th strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    padding-inline-start: 1.625em;
    list-style-type: decimal;
  }

  .prose :where(ol[type="A"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-alpha;
  }

  .prose :where(ol[type="a"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-alpha;
  }

  .prose :where(ol[type="A" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-alpha;
  }

  .prose :where(ol[type="a" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-alpha;
  }

  .prose :where(ol[type="I"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-roman;
  }

  .prose :where(ol[type="i"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-roman;
  }

  .prose :where(ol[type="I" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-roman;
  }

  .prose :where(ol[type="i" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-roman;
  }

  .prose :where(ol[type="1"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: decimal;
  }

  .prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    padding-inline-start: 1.625em;
    list-style-type: disc;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *))::marker {
    color: var(--tw-prose-counters);
    font-weight: 400;
  }

  .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *))::marker {
    color: var(--tw-prose-bullets);
  }

  .prose :where(dt):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.25em;
    font-weight: 600;
  }

  .prose :where(hr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-color: var(--tw-prose-hr);
    border-top-width: 1px;
    margin-top: 3em;
    margin-bottom: 3em;
  }

  .prose :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-quotes);
    border-inline-start-width: .25rem;
    border-inline-start-color: var(--tw-prose-quote-borders);
    quotes: "“""”""‘""’";
    margin-top: 1.6em;
    margin-bottom: 1.6em;
    padding-inline-start: 1em;
    font-style: italic;
    font-weight: 500;
  }

  .prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"], [class~="not-prose"] *)):before {
    content: open-quote;
  }

  .prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"], [class~="not-prose"] *)):after {
    content: close-quote;
  }

  .prose :where(h1):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 0;
    margin-bottom: .888889em;
    font-size: 2.25em;
    font-weight: 800;
    line-height: 1.11111;
  }

  .prose :where(h1 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 900;
  }

  .prose :where(h2):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 2em;
    margin-bottom: 1em;
    font-size: 1.5em;
    font-weight: 700;
    line-height: 1.33333;
  }

  .prose :where(h2 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 800;
  }

  .prose :where(h3):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.6em;
    margin-bottom: .6em;
    font-size: 1.25em;
    font-weight: 600;
    line-height: 1.6;
  }

  .prose :where(h3 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 700;
  }

  .prose :where(h4):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.5em;
    margin-bottom: .5em;
    font-weight: 600;
    line-height: 1.5;
  }

  .prose :where(h4 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 700;
  }

  .prose :where(img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(picture):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
    display: block;
  }

  .prose :where(video):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-kbd);
    box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
    padding-top: .1875em;
    padding-inline-end: .375em;
    padding-bottom: .1875em;
    border-radius: .3125rem;
    padding-inline-start: .375em;
    font-family: inherit;
    font-size: .875em;
    font-weight: 500;
  }

  .prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-code);
    font-size: .875em;
    font-weight: 600;
  }

  .prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)):before, .prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)):after {
    content: "`";
  }

  .prose :where(a code):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(h1 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(h2 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-size: .875em;
  }

  .prose :where(h3 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-size: .9em;
  }

  .prose :where(h4 code):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(blockquote code):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(thead th code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-pre-code);
    background-color: var(--tw-prose-pre-bg);
    padding-top: .857143em;
    padding-inline-end: 1.14286em;
    padding-bottom: .857143em;
    border-radius: .375rem;
    margin-top: 1.71429em;
    margin-bottom: 1.71429em;
    padding-inline-start: 1.14286em;
    font-size: .875em;
    font-weight: 400;
    line-height: 1.71429;
    overflow-x: auto;
  }

  .prose :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-weight: inherit;
    color: inherit;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    background-color: #0000;
    border-width: 0;
    border-radius: 0;
    padding: 0;
  }

  .prose :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *)):before, .prose :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *)):after {
    content: none;
  }

  .prose :where(table):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    table-layout: auto;
    width: 100%;
    margin-top: 2em;
    margin-bottom: 2em;
    font-size: .875em;
    line-height: 1.71429;
  }

  .prose :where(thead):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom-width: 1px;
    border-bottom-color: var(--tw-prose-th-borders);
  }

  .prose :where(thead th):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    vertical-align: bottom;
    padding-inline-end: .571429em;
    padding-bottom: .571429em;
    padding-inline-start: .571429em;
    font-weight: 600;
  }

  .prose :where(tbody tr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom-width: 1px;
    border-bottom-color: var(--tw-prose-td-borders);
  }

  .prose :where(tbody tr:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom-width: 0;
  }

  .prose :where(tbody td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    vertical-align: baseline;
  }

  .prose :where(tfoot):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-top-width: 1px;
    border-top-color: var(--tw-prose-th-borders);
  }

  .prose :where(tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    vertical-align: top;
  }

  .prose :where(th, td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    text-align: start;
  }

  .prose :where(figure > *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose :where(figcaption):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-captions);
    margin-top: .857143em;
    font-size: .875em;
    line-height: 1.42857;
  }

  .prose :where(picture > img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose :where(li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .5em;
    margin-bottom: .5em;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-start: .375em;
  }

  .prose :where(.prose > ul > li p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .75em;
    margin-bottom: .75em;
  }

  .prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
  }

  .prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 1.25em;
  }

  .prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
  }

  .prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 1.25em;
  }

  .prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .75em;
    margin-bottom: .75em;
  }

  .prose :where(dl):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .5em;
    padding-inline-start: 1.625em;
  }

  .prose :where(hr + *):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(h2 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(h3 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(h4 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose :where(thead th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .prose :where(thead th:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .prose :where(tbody td, tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-top: .571429em;
    padding-inline-end: .571429em;
    padding-bottom: .571429em;
    padding-inline-start: .571429em;
  }

  .prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .prose :where(figure):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(.prose > :first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose :where(.prose > :last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 0;
  }

  .-mt-1 {
    margin-top: calc(var(--spacing) * -1);
  }

  .-mt-4 {
    margin-top: calc(var(--spacing) * -4);
  }

  .-mt-\[1px\] {
    margin-top: -1px;
  }

  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }

  .mt-24 {
    margin-top: calc(var(--spacing) * 24);
  }

  .mt-\[-4px\] {
    margin-top: -4px;
  }

  .mt-\[-8px\] {
    margin-top: -8px;
  }

  .mt-\[2\.5rem\] {
    margin-top: 2.5rem;
  }

  .mt-\[2px\] {
    margin-top: 2px;
  }

  .mt-auto {
    margin-top: auto;
  }

  .mr-0\.5 {
    margin-right: calc(var(--spacing) * .5);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-5 {
    margin-right: calc(var(--spacing) * 5);
  }

  .mr-10 {
    margin-right: calc(var(--spacing) * 10);
  }

  .mr-\[1px\] {
    margin-right: 1px;
  }

  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .mb-0\.5 {
    margin-bottom: calc(var(--spacing) * .5);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .mb-\[1px\] {
    margin-bottom: 1px;
  }

  .-ml-4 {
    margin-left: calc(var(--spacing) * -4);
  }

  .ml-0 {
    margin-left: calc(var(--spacing) * 0);
  }

  .ml-0\.5 {
    margin-left: calc(var(--spacing) * .5);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-5 {
    margin-left: calc(var(--spacing) * 5);
  }

  .ml-8 {
    margin-left: calc(var(--spacing) * 8);
  }

  .ml-12 {
    margin-left: calc(var(--spacing) * 12);
  }

  .ml-\[1px\] {
    margin-left: 1px;
  }

  .ml-auto {
    margin-left: auto;
  }

  .line-clamp-1 {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .inline-grid {
    display: inline-grid;
  }

  .table {
    display: table;
  }

  .table-caption {
    display: table-caption;
  }

  .table-cell {
    display: table-cell;
  }

  .table-row {
    display: table-row;
  }

  .field-sizing-content {
    field-sizing: content;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
  }

  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }

  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-32 {
    width: calc(var(--spacing) * 32);
    height: calc(var(--spacing) * 32);
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-0 {
    height: calc(var(--spacing) * 0);
  }

  .h-0\.5 {
    height: calc(var(--spacing) * .5);
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-4\.5 {
    height: calc(var(--spacing) * 4.5);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-11 {
    height: calc(var(--spacing) * 11);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-28 {
    height: calc(var(--spacing) * 28);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-40 {
    height: calc(var(--spacing) * 40);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-100 {
    height: calc(var(--spacing) * 100);
  }

  .h-\[0\.5px\] {
    height: .5px;
  }

  .h-\[1\.15rem\] {
    height: 1.15rem;
  }

  .h-\[1px\] {
    height: 1px;
  }

  .h-\[2\.5px\] {
    height: 2.5px;
  }

  .h-\[2px\] {
    height: 2px;
  }

  .h-\[4px\] {
    height: 4px;
  }

  .h-\[22rem\] {
    height: 22rem;
  }

  .h-\[36px\] {
    height: 36px;
  }

  .h-\[40rem\] {
    height: 40rem;
  }

  .h-\[50\%\] {
    height: 50%;
  }

  .h-\[70vh\] {
    height: 70vh;
  }

  .h-\[80vh\] {
    height: 80vh;
  }

  .h-\[85\%\] {
    height: 85%;
  }

  .h-\[320px\] {
    height: 320px;
  }

  .h-\[400px\] {
    height: 400px;
  }

  .h-\[calc\(100\%-1px\)\] {
    height: calc(100% - 1px);
  }

  .h-\[calc\(100\%-40px\)\] {
    height: calc(100% - 40px);
  }

  .h-\[calc\(100vh\+80px\)\] {
    height: calc(100vh + 80px);
  }

  .h-\[var\(--radix-navigation-menu-viewport-height\)\] {
    height: var(--radix-navigation-menu-viewport-height);
  }

  .h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height);
  }

  .h-auto {
    height: auto;
  }

  .h-fit {
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .h-screen {
    height: 100vh;
  }

  .h-svh {
    height: 100svh;
  }

  .max-h-\(--radix-context-menu-content-available-height\) {
    max-height: var(--radix-context-menu-content-available-height);
  }

  .max-h-\(--radix-dropdown-menu-content-available-height\) {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  .max-h-\(--radix-select-content-available-height\) {
    max-height: var(--radix-select-content-available-height);
  }

  .max-h-0 {
    max-height: calc(var(--spacing) * 0);
  }

  .max-h-20 {
    max-height: calc(var(--spacing) * 20);
  }

  .max-h-32 {
    max-height: calc(var(--spacing) * 32);
  }

  .max-h-40 {
    max-height: calc(var(--spacing) * 40);
  }

  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }

  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }

  .max-h-\[70vh\] {
    max-height: 70vh;
  }

  .max-h-\[80\%\] {
    max-height: 80%;
  }

  .max-h-\[80px\] {
    max-height: 80px;
  }

  .max-h-\[80vh\] {
    max-height: 80vh;
  }

  .max-h-\[300px\] {
    max-height: 300px;
  }

  .max-h-\[350px\] {
    max-height: 350px;
  }

  .max-h-\[400px\] {
    max-height: 400px;
  }

  .max-h-full {
    max-height: 100%;
  }

  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }

  .min-h-4 {
    min-height: calc(var(--spacing) * 4);
  }

  .min-h-5 {
    min-height: calc(var(--spacing) * 5);
  }

  .min-h-6 {
    min-height: calc(var(--spacing) * 6);
  }

  .min-h-16 {
    min-height: calc(var(--spacing) * 16);
  }

  .min-h-\[60px\] {
    min-height: 60px;
  }

  .min-h-\[160px\] {
    min-height: 160px;
  }

  .min-h-\[420px\] {
    min-height: 420px;
  }

  .min-h-\[480px\] {
    min-height: 480px;
  }

  .min-h-full {
    min-height: 100%;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .min-h-svh {
    min-height: 100svh;
  }

  .w-\(--sidebar-width\) {
    width: var(--sidebar-width);
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }

  .w-2\/3 {
    width: 66.6667%;
  }

  .w-2\/5 {
    width: 40%;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-3\/5 {
    width: 60%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-4\.5 {
    width: calc(var(--spacing) * 4.5);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-5\.5 {
    width: calc(var(--spacing) * 5.5);
  }

  .w-5\/6 {
    width: 83.3333%;
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-9 {
    width: calc(var(--spacing) * 9);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-22 {
    width: calc(var(--spacing) * 22);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-42 {
    width: calc(var(--spacing) * 42);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-56 {
    width: calc(var(--spacing) * 56);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-72 {
    width: calc(var(--spacing) * 72);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-96 {
    width: calc(var(--spacing) * 96);
  }

  .w-\[0\.5px\] {
    width: .5px;
  }

  .w-\[1px\] {
    width: 1px;
  }

  .w-\[30rem\] {
    width: 30rem;
  }

  .w-\[32px\] {
    width: 32px;
  }

  .w-\[37rem\] {
    width: 37rem;
  }

  .w-\[48px\] {
    width: 48px;
  }

  .w-\[64px\] {
    width: 64px;
  }

  .w-\[70px\] {
    width: 70px;
  }

  .w-\[72px\] {
    width: 72px;
  }

  .w-\[85\%\] {
    width: 85%;
  }

  .w-\[90\%\] {
    width: 90%;
  }

  .w-\[100px\] {
    width: 100px;
  }

  .w-\[220px\] {
    width: 220px;
  }

  .w-\[224px\] {
    width: 224px;
  }

  .w-\[225px\] {
    width: 225px;
  }

  .w-\[260px\] {
    width: 260px;
  }

  .w-\[275px\] {
    width: 275px;
  }

  .w-\[280px\] {
    width: 280px;
  }

  .w-\[300px\] {
    width: 300px;
  }

  .w-\[320px\] {
    width: 320px;
  }

  .w-\[440px\] {
    width: 440px;
  }

  .w-\[600px\] {
    width: 600px;
  }

  .w-\[fit-content\] {
    width: fit-content;
  }

  .w-auto {
    width: auto;
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-max {
    width: max-content;
  }

  .w-px {
    width: 1px;
  }

  .w-screen {
    width: 100vw;
  }

  .\!max-w-sm {
    max-width: var(--container-sm) !important;
  }

  .max-w-\(--skeleton-width\) {
    max-width: var(--skeleton-width);
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-5xl {
    max-width: var(--container-5xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-9 {
    max-width: calc(var(--spacing) * 9);
  }

  .max-w-32 {
    max-width: calc(var(--spacing) * 32);
  }

  .max-w-52 {
    max-width: calc(var(--spacing) * 52);
  }

  .max-w-\[40px\] {
    max-width: 40px;
  }

  .max-w-\[60px\] {
    max-width: 60px;
  }

  .max-w-\[100px\] {
    max-width: 100px;
  }

  .max-w-\[150px\] {
    max-width: 150px;
  }

  .max-w-\[200px\] {
    max-width: 200px;
  }

  .max-w-\[300px\] {
    max-width: 300px;
  }

  .max-w-\[480px\] {
    max-width: 480px;
  }

  .max-w-\[800px\] {
    max-width: 800px;
  }

  .max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-max {
    max-width: max-content;
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-4 {
    min-width: calc(var(--spacing) * 4);
  }

  .min-w-5 {
    min-width: calc(var(--spacing) * 5);
  }

  .min-w-8 {
    min-width: calc(var(--spacing) * 8);
  }

  .min-w-9 {
    min-width: calc(var(--spacing) * 9);
  }

  .min-w-10 {
    min-width: calc(var(--spacing) * 10);
  }

  .min-w-42 {
    min-width: calc(var(--spacing) * 42);
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-\[12rem\] {
    min-width: 12rem;
  }

  .min-w-\[28px\] {
    min-width: 28px;
  }

  .min-w-\[40px\] {
    min-width: 40px;
  }

  .min-w-\[48px\] {
    min-width: 48px;
  }

  .min-w-\[50px\] {
    min-width: 50px;
  }

  .min-w-\[72px\] {
    min-width: 72px;
  }

  .min-w-\[90vw\] {
    min-width: 90vw;
  }

  .min-w-\[100px\] {
    min-width: 100px;
  }

  .min-w-\[120px\] {
    min-width: 120px;
  }

  .min-w-\[140px\] {
    min-width: 140px;
  }

  .min-w-\[200px\] {
    min-width: 200px;
  }

  .min-w-\[220px\] {
    min-width: 220px;
  }

  .min-w-\[250px\] {
    min-width: 250px;
  }

  .min-w-\[280px\] {
    min-width: 280px;
  }

  .min-w-\[300px\] {
    min-width: 300px;
  }

  .min-w-\[fit-content\] {
    min-width: fit-content;
  }

  .min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width);
  }

  .min-w-fit {
    min-width: fit-content;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-none {
    flex: none;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .flex-grow {
    flex-grow: 1;
  }

  .flex-grow-0 {
    flex-grow: 0;
  }

  .grow {
    flex-grow: 1;
  }

  .grow-0 {
    flex-grow: 0;
  }

  .basis-0 {
    flex-basis: calc(var(--spacing) * 0);
  }

  .basis-full {
    flex-basis: 100%;
  }

  .caption-bottom {
    caption-side: bottom;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .origin-\(--radix-context-menu-content-transform-origin\) {
    transform-origin: var(--radix-context-menu-content-transform-origin);
  }

  .origin-\(--radix-dropdown-menu-content-transform-origin\) {
    transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  }

  .origin-\(--radix-hover-card-content-transform-origin\) {
    transform-origin: var(--radix-hover-card-content-transform-origin);
  }

  .origin-\(--radix-menubar-content-transform-origin\) {
    transform-origin: var(--radix-menubar-content-transform-origin);
  }

  .origin-\(--radix-popover-content-transform-origin\) {
    transform-origin: var(--radix-popover-content-transform-origin);
  }

  .origin-\(--radix-select-content-transform-origin\) {
    transform-origin: var(--radix-select-content-transform-origin);
  }

  .origin-\(--radix-tooltip-content-transform-origin\) {
    transform-origin: var(--radix-tooltip-content-transform-origin);
  }

  .origin-center {
    transform-origin: center;
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-2 {
    --tw-translate-x: calc(var(--spacing) * -2);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-px {
    --tw-translate-x: -1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-px {
    --tw-translate-x: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-2 {
    --tw-translate-y: calc(var(--spacing) * -2);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0\.5 {
    --tw-translate-y: calc(var(--spacing) * .5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[calc\(-50\%_-_2px\)\] {
    --tw-translate-y: calc(-50% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-75 {
    --tw-scale-x: 75%;
    --tw-scale-y: 75%;
    --tw-scale-z: 75%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-x-\[-1\] {
    --tw-scale-x: -1;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-\[0\.2\] {
    scale: .2;
  }

  .rotate-0 {
    rotate: none;
  }

  .rotate-45 {
    rotate: 45deg;
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-edit-panel-in {
    animation: var(--animate-edit-panel-in);
  }

  .animate-layer-panel-in {
    animation: var(--animate-layer-panel-in);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .animate-toolbar-up {
    animation: var(--animate-toolbar-up);
  }

  .cursor-col-resize {
    cursor: col-resize;
  }

  .cursor-copy {
    cursor: copy;
  }

  .cursor-crosshair {
    cursor: crosshair;
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-e-resize {
    cursor: e-resize;
  }

  .cursor-grab {
    cursor: grab;
  }

  .cursor-grabbing {
    cursor: grabbing;
  }

  .cursor-move {
    cursor: move;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-s-resize {
    cursor: s-resize;
  }

  .cursor-se-resize {
    cursor: se-resize;
  }

  .cursor-text {
    cursor: text;
  }

  .touch-none {
    touch-action: none;
  }

  .resize {
    resize: both;
  }

  .resize-none {
    resize: none;
  }

  .scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
  }

  .scroll-mt-24 {
    scroll-margin-top: calc(var(--spacing) * 24);
  }

  .scroll-py-1 {
    scroll-padding-block: calc(var(--spacing) * 1);
  }

  .list-disc {
    list-style-type: disc;
  }

  .list-none {
    list-style-type: none;
  }

  .\[appearance\:textfield\] {
    appearance: textfield;
  }

  .appearance-none {
    appearance: none;
  }

  .auto-cols-max {
    grid-auto-columns: max-content;
  }

  .grid-flow-col {
    grid-auto-flow: column;
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .grid-cols-\[0_1fr\] {
    grid-template-columns: 0 1fr;
  }

  .grid-cols-\[48px_1fr_1fr_1fr_46px\] {
    grid-template-columns: 48px 1fr 1fr 1fr 46px;
  }

  .grid-cols-subgrid {
    grid-template-columns: subgrid;
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .place-items-center {
    place-items: center;
  }

  .content-center {
    align-content: center;
  }

  .content-start {
    align-content: flex-start;
  }

  .items-baseline {
    align-items: baseline;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .items-stretch {
    align-items: stretch;
  }

  .\!justify-center {
    justify-content: center !important;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .justify-items-start {
    justify-items: start;
  }

  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }

  .gap-0\.5 {
    gap: calc(var(--spacing) * .5);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }

  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }

  .gap-18 {
    gap: calc(var(--spacing) * 18);
  }

  .gap-24 {
    gap: calc(var(--spacing) * 24);
  }

  .gap-72 {
    gap: calc(var(--spacing) * 72);
  }

  .gap-\[1px\] {
    gap: 1px;
  }

  .gap-\[32px\] {
    gap: 32px;
  }

  :where(.space-y-0\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * .5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * .5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2.5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-0 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 0) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  .gap-y-0\.5 {
    row-gap: calc(var(--spacing) * .5);
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-border > :not(:last-child)) {
    border-color: hsl(var(--border));
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-\(--border-radius\) {
    border-radius: var(--border-radius);
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-\[2px\] {
    border-radius: 2px;
  }

  .rounded-\[4px\] {
    border-radius: 4px;
  }

  .rounded-\[inherit\] {
    border-radius: inherit;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-none {
    border-radius: 0;
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-xs {
    border-radius: var(--radius-xs);
  }

  .rounded-t {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
  }

  .rounded-t-none {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  .rounded-l-md {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }

  .rounded-l-none {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .rounded-tl-lg {
    border-top-left-radius: var(--radius-lg);
  }

  .rounded-tl-none {
    border-top-left-radius: 0;
  }

  .rounded-tl-sm {
    border-top-left-radius: var(--radius-sm);
  }

  .rounded-tl-xl {
    border-top-left-radius: var(--radius-xl);
  }

  .rounded-r-\[calc\(theme\(borderRadius\.md\)-1px\)\] {
    border-top-right-radius: calc(.375rem - 1px);
    border-bottom-right-radius: calc(.375rem - 1px);
  }

  .rounded-r-md {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }

  .rounded-r-none {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .rounded-b {
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem;
  }

  .rounded-br-none {
    border-bottom-right-radius: 0;
  }

  .rounded-bl-none {
    border-bottom-left-radius: 0;
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-\[0\.5px\] {
    border-style: var(--tw-border-style);
    border-width: .5px;
  }

  .border-\[1\.5px\] {
    border-style: var(--tw-border-style);
    border-width: 1.5px;
  }

  .border-y, .border-y-\[1px\] {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-r-0 {
    border-right-style: var(--tw-border-style);
    border-right-width: 0;
  }

  .border-r-\[0\.5px\] {
    border-right-style: var(--tw-border-style);
    border-right-width: .5px;
  }

  .border-b, .border-b-1 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-\[0\.5px\] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: .5px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-l-0 {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  .border-l-\[0\.5px\] {
    border-left-style: var(--tw-border-style);
    border-left-width: .5px;
  }

  .border-l-\[1px\] {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-none {
    --tw-border-style: none;
    border-style: none;
  }

  .border-\(--color-border\) {
    border-color: var(--color-border);
  }

  .border-amber-200 {
    border-color: #f6c100;
  }

  .border-amber-200\/20 {
    border-color: oklab(83.4756% .00426355 .170543 / .2);
  }

  .border-amber-300 {
    border-color: #d7a800;
  }

  .border-background-secondary {
    border-color: hsl(var(--background-secondary));
  }

  .border-background-secondary\/75 {
    border-color: hsl(var(--background-secondary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-background-secondary\/75 {
      border-color: color-mix(in oklab, hsl(var(--background-secondary)) 75%, transparent);
    }
  }

  .border-blue {
    border-color: #90d1ff;
  }

  .border-blue-400 {
    border-color: #109bff;
  }

  .border-blue-500 {
    border-color: #0081de;
  }

  .border-blue-500\/20 {
    border-color: oklab(59.4594% -.0573393 -.159088 / .2);
  }

  .border-border {
    border-color: hsl(var(--border));
  }

  .border-border-active {
    border-color: hsl(var(--border-active));
  }

  .border-border\/0 {
    border-color: hsl(var(--border));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/0 {
      border-color: color-mix(in oklab, hsl(var(--border)) 0%, transparent);
    }
  }

  .border-border\/10 {
    border-color: hsl(var(--border));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/10 {
      border-color: color-mix(in oklab, hsl(var(--border)) 10%, transparent);
    }
  }

  .border-border\/50 {
    border-color: hsl(var(--border));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/50 {
      border-color: color-mix(in oklab, hsl(var(--border)) 50%, transparent);
    }
  }

  .border-foreground-hover {
    border-color: hsl(var(--foreground-hover));
  }

  .border-foreground-primary {
    border-color: hsl(var(--foreground-primary));
  }

  .border-foreground-primary\/10 {
    border-color: hsl(var(--foreground-primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-foreground-primary\/10 {
      border-color: color-mix(in oklab, hsl(var(--foreground-primary)) 10%, transparent);
    }
  }

  .border-foreground-primary\/40 {
    border-color: hsl(var(--foreground-primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-foreground-primary\/40 {
      border-color: color-mix(in oklab, hsl(var(--foreground-primary)) 40%, transparent);
    }
  }

  .border-foreground-primary\/50 {
    border-color: hsl(var(--foreground-primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-foreground-primary\/50 {
      border-color: color-mix(in oklab, hsl(var(--foreground-primary)) 50%, transparent);
    }
  }

  .border-foreground-primary\/80 {
    border-color: hsl(var(--foreground-primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-foreground-primary\/80 {
      border-color: color-mix(in oklab, hsl(var(--foreground-primary)) 80%, transparent);
    }
  }

  .border-foreground-secondary\/20 {
    border-color: hsl(var(--foreground-secondary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-foreground-secondary\/20 {
      border-color: color-mix(in oklab, hsl(var(--foreground-secondary)) 20%, transparent);
    }
  }

  .border-foreground-tertiary\/50 {
    border-color: hsl(var(--foreground-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-foreground-tertiary\/50 {
      border-color: color-mix(in oklab, hsl(var(--foreground-tertiary)) 50%, transparent);
    }
  }

  .border-foreground\/20 {
    border-color: hsl(var(--foreground));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-foreground\/20 {
      border-color: color-mix(in oklab, hsl(var(--foreground)) 20%, transparent);
    }
  }

  .border-gray-300 {
    border-color: #929292;
  }

  .border-gray-500 {
    border-color: #606060;
  }

  .border-green-500 {
    border-color: #008c2f;
  }

  .border-input {
    border-color: hsl(var(--input));
  }

  .border-primary {
    border-color: hsl(var(--primary));
  }

  .border-primary\/10 {
    border-color: hsl(var(--primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-primary\/10 {
      border-color: color-mix(in oklab, hsl(var(--primary)) 10%, transparent);
    }
  }

  .border-red-500 {
    border-color: #fa003c;
  }

  .border-red-500\/30 {
    border-color: oklab(62.2415% .233164 .0915698 / .3);
  }

  .border-sidebar-border {
    border-color: var(--sidebar-border);
  }

  .border-teal-300 {
    border-color: #00c1a2;
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-white\/5 {
    border-color: #ffffff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/5 {
      border-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .border-white\/10 {
    border-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/10 {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .border-white\/20 {
    border-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/20 {
      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .border-yellow-300 {
    border-color: #d7a800;
  }

  .border-t-transparent {
    border-top-color: #0000;
  }

  .border-l-transparent {
    border-left-color: #0000;
  }

  .\!bg-\[\#FA003C\] {
    background-color: #fa003c !important;
  }

  .\!bg-black {
    background-color: #000 !important;
  }

  .\!bg-transparent {
    background-color: #0000 !important;
  }

  .bg-\(--color-bg\) {
    background-color: var(--color-bg);
  }

  .bg-\[\#FA003C\] {
    background-color: #fa003c;
  }

  .bg-\[\#FA003C\]\/10 {
    background-color: oklab(62.2415% .233164 .0915698 / .1);
  }

  .bg-\[\#FA003C\]\/20 {
    background-color: oklab(62.2415% .233164 .0915698 / .2);
  }

  .bg-accent {
    background-color: hsl(var(--accent));
  }

  .bg-amber-100 {
    background-color: #fff0bc;
  }

  .bg-amber-900\/80 {
    background-color: oklab(27.3424% -.00219379 .0558996 / .8);
  }

  .bg-background {
    background-color: hsl(var(--background));
  }

  .bg-background-active {
    background-color: hsl(var(--background-active));
  }

  .bg-background-onlook {
    background-color: hsl(var(--background-onlook));
  }

  .bg-background-onlook\/20 {
    background-color: hsl(var(--background-onlook));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-onlook\/20 {
      background-color: color-mix(in oklab, hsl(var(--background-onlook)) 20%, transparent);
    }
  }

  .bg-background-onlook\/60 {
    background-color: hsl(var(--background-onlook));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-onlook\/60 {
      background-color: color-mix(in oklab, hsl(var(--background-onlook)) 60%, transparent);
    }
  }

  .bg-background-onlook\/70 {
    background-color: hsl(var(--background-onlook));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-onlook\/70 {
      background-color: color-mix(in oklab, hsl(var(--background-onlook)) 70%, transparent);
    }
  }

  .bg-background-onlook\/80 {
    background-color: hsl(var(--background-onlook));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-onlook\/80 {
      background-color: color-mix(in oklab, hsl(var(--background-onlook)) 80%, transparent);
    }
  }

  .bg-background-primary {
    background-color: hsl(var(--background-primary));
  }

  .bg-background-primary\/10 {
    background-color: hsl(var(--background-primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-primary\/10 {
      background-color: color-mix(in oklab, hsl(var(--background-primary)) 10%, transparent);
    }
  }

  .bg-background-primary\/20 {
    background-color: hsl(var(--background-primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-primary\/20 {
      background-color: color-mix(in oklab, hsl(var(--background-primary)) 20%, transparent);
    }
  }

  .bg-background-secondary {
    background-color: hsl(var(--background-secondary));
  }

  .bg-background-secondary\/75 {
    background-color: hsl(var(--background-secondary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-secondary\/75 {
      background-color: color-mix(in oklab, hsl(var(--background-secondary)) 75%, transparent);
    }
  }

  .bg-background-secondary\/80 {
    background-color: hsl(var(--background-secondary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-secondary\/80 {
      background-color: color-mix(in oklab, hsl(var(--background-secondary)) 80%, transparent);
    }
  }

  .bg-background-secondary\/85 {
    background-color: hsl(var(--background-secondary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-secondary\/85 {
      background-color: color-mix(in oklab, hsl(var(--background-secondary)) 85%, transparent);
    }
  }

  .bg-background-tertiary {
    background-color: hsl(var(--background-tertiary));
  }

  .bg-background-tertiary\/20 {
    background-color: hsl(var(--background-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-tertiary\/20 {
      background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 20%, transparent);
    }
  }

  .bg-background-tertiary\/50 {
    background-color: hsl(var(--background-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background-tertiary\/50 {
      background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 50%, transparent);
    }
  }

  .bg-background\/20 {
    background-color: hsl(var(--background));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/20 {
      background-color: color-mix(in oklab, hsl(var(--background)) 20%, transparent);
    }
  }

  .bg-background\/30 {
    background-color: hsl(var(--background));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/30 {
      background-color: color-mix(in oklab, hsl(var(--background)) 30%, transparent);
    }
  }

  .bg-background\/40 {
    background-color: hsl(var(--background));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/40 {
      background-color: color-mix(in oklab, hsl(var(--background)) 40%, transparent);
    }
  }

  .bg-background\/50 {
    background-color: hsl(var(--background));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/50 {
      background-color: color-mix(in oklab, hsl(var(--background)) 50%, transparent);
    }
  }

  .bg-background\/80 {
    background-color: hsl(var(--background));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/80 {
      background-color: color-mix(in oklab, hsl(var(--background)) 80%, transparent);
    }
  }

  .bg-background\/85 {
    background-color: hsl(var(--background));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/85 {
      background-color: color-mix(in oklab, hsl(var(--background)) 85%, transparent);
    }
  }

  .bg-background\/90 {
    background-color: hsl(var(--background));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/90 {
      background-color: color-mix(in oklab, hsl(var(--background)) 90%, transparent);
    }
  }

  .bg-background\/95 {
    background-color: hsl(var(--background));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/95 {
      background-color: color-mix(in oklab, hsl(var(--background)) 95%, transparent);
    }
  }

  .bg-black {
    background-color: #000;
  }

  .bg-black\/10 {
    background-color: oklab(0% none none / .1);
  }

  .bg-black\/20 {
    background-color: oklab(0% none none / .2);
  }

  .bg-black\/30 {
    background-color: oklab(0% none none / .3);
  }

  .bg-black\/50 {
    background-color: oklab(0% none none / .5);
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-400 {
    background-color: #109bff;
  }

  .bg-blue-500 {
    background-color: #0081de;
  }

  .bg-blue-500\/10 {
    background-color: oklab(59.4594% -.0573393 -.159088 / .1);
  }

  .bg-blue-600 {
    background-color: #006ab5;
  }

  .bg-blue-900\/60 {
    background-color: oklab(27.6058% -.0290023 -.0643575 / .6);
  }

  .bg-border {
    background-color: hsl(var(--border));
  }

  .bg-card {
    background-color: hsl(var(--card));
  }

  .bg-current {
    background-color: currentColor;
  }

  .bg-destructive {
    background-color: hsl(var(--destructive));
  }

  .bg-foreground {
    background-color: hsl(var(--foreground));
  }

  .bg-foreground-hover {
    background-color: hsl(var(--foreground-hover));
  }

  .bg-foreground-primary {
    background-color: hsl(var(--foreground-primary));
  }

  .bg-foreground-primary\/20 {
    background-color: hsl(var(--foreground-primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-foreground-primary\/20 {
      background-color: color-mix(in oklab, hsl(var(--foreground-primary)) 20%, transparent);
    }
  }

  .bg-foreground-primary\/80 {
    background-color: hsl(var(--foreground-primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-foreground-primary\/80 {
      background-color: color-mix(in oklab, hsl(var(--foreground-primary)) 80%, transparent);
    }
  }

  .bg-foreground-secondary\/30 {
    background-color: hsl(var(--foreground-secondary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-foreground-secondary\/30 {
      background-color: color-mix(in oklab, hsl(var(--foreground-secondary)) 30%, transparent);
    }
  }

  .bg-foreground\/10 {
    background-color: hsl(var(--foreground));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-foreground\/10 {
      background-color: color-mix(in oklab, hsl(var(--foreground)) 10%, transparent);
    }
  }

  .bg-gray-900 {
    background-color: #1a1a1a;
  }

  .bg-muted {
    background-color: hsl(var(--muted));
  }

  .bg-muted\/50 {
    background-color: hsl(var(--muted));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/50 {
      background-color: color-mix(in oklab, hsl(var(--muted)) 50%, transparent);
    }
  }

  .bg-popover {
    background-color: hsl(var(--popover));
  }

  .bg-primary {
    background-color: hsl(var(--primary));
  }

  .bg-primary\/20 {
    background-color: hsl(var(--primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/20 {
      background-color: color-mix(in oklab, hsl(var(--primary)) 20%, transparent);
    }
  }

  .bg-purple-300\/30 {
    background-color: oklab(77.23% .0958276 -.119797 / .3);
  }

  .bg-purple-300\/50 {
    background-color: oklab(77.23% .0958276 -.119797 / .5);
  }

  .bg-purple-400\/30 {
    background-color: oklab(70.0253% .124694 -.162434 / .3);
  }

  .bg-purple-700\/70 {
    background-color: oklab(46.9933% .120177 -.22285 / .7);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-red-500 {
    background-color: #fa003c;
  }

  .bg-red-500\/10 {
    background-color: oklab(62.2415% .233164 .0915698 / .1);
  }

  .bg-red-900\/80 {
    background-color: oklab(28.7548% .110178 .0332238 / .8);
  }

  .bg-secondary {
    background-color: hsl(var(--secondary));
  }

  .bg-secondary\/20 {
    background-color: hsl(var(--secondary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-secondary\/20 {
      background-color: color-mix(in oklab, hsl(var(--secondary)) 20%, transparent);
    }
  }

  .bg-sidebar {
    background-color: var(--sidebar);
  }

  .bg-sidebar-border {
    background-color: var(--sidebar-border);
  }

  .bg-teal-50 {
    background-color: var(--color-teal-50);
  }

  .bg-teal-100 {
    background-color: #cbfff6;
  }

  .bg-teal-400\/20 {
    background-color: oklab(64.7657% -.121153 .00937426 / .2);
  }

  .bg-teal-400\/90 {
    background-color: oklab(64.7657% -.121153 .00937426 / .9);
  }

  .bg-teal-500 {
    background-color: #008b74;
  }

  .bg-teal-500\/40 {
    background-color: oklab(56.8692% -.106409 .00830203 / .4);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/10 {
    background-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/10 {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .bg-white\/20 {
    background-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/20 {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-none {
    background-image: none;
  }

  .from-gray-800\/40 {
    --tw-gradient-from: oklab(23.9292% -7.45058e-9 1.49012e-8 / .4);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-gray-500\/40 {
    --tw-gradient-via: oklab(48.9056% -5.96046e-8 2.98023e-8 / .4);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-gray-400\/40 {
    --tw-gradient-to: oklab(57.2683% -5.96046e-8 2.98023e-8 / .4);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .fill-\[\#313131\] {
    fill: #313131;
  }

  .fill-current {
    fill: currentColor;
  }

  .fill-foreground {
    fill: hsl(var(--foreground));
  }

  .fill-foreground\/50 {
    fill: hsl(var(--foreground));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .fill-foreground\/50 {
      fill: color-mix(in oklab, hsl(var(--foreground)) 50%, transparent);
    }
  }

  .fill-gray-200 {
    fill: #acacac;
  }

  .fill-none {
    fill: none;
  }

  .fill-primary {
    fill: hsl(var(--primary));
  }

  .fill-teal-400 {
    fill: #00a68b;
  }

  .fill-white {
    fill: var(--color-white);
  }

  .stroke-\[\#313131\] {
    stroke: #313131;
  }

  .stroke-gray-300 {
    stroke: #929292;
  }

  .stroke-gray-400 {
    stroke: #787878;
  }

  .stroke-white {
    stroke: var(--color-white);
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-0\.5 {
    padding: calc(var(--spacing) * .5);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-16 {
    padding: calc(var(--spacing) * 16);
  }

  .p-\[3px\] {
    padding: 3px;
  }

  .p-px {
    padding: 1px;
  }

  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }

  .px-0\.5 {
    padding-inline: calc(var(--spacing) * .5);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }

  .px-12 {
    padding-inline: calc(var(--spacing) * 12);
  }

  .px-16 {
    padding-inline: calc(var(--spacing) * 16);
  }

  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .py-32 {
    padding-block: calc(var(--spacing) * 32);
  }

  .py-48 {
    padding-block: calc(var(--spacing) * 48);
  }

  .py-\[0\.5px\] {
    padding-block: .5px;
  }

  .py-\[18px\] {
    padding-block: 18px;
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-0\.5 {
    padding-top: calc(var(--spacing) * .5);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }

  .pt-24 {
    padding-top: calc(var(--spacing) * 24);
  }

  .pt-28 {
    padding-top: calc(var(--spacing) * 28);
  }

  .pr-0 {
    padding-right: calc(var(--spacing) * 0);
  }

  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }

  .pr-1\.5 {
    padding-right: calc(var(--spacing) * 1.5);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-2\.5 {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .pr-16 {
    padding-right: calc(var(--spacing) * 16);
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }

  .pb-24 {
    padding-bottom: calc(var(--spacing) * 24);
  }

  .pl-1\.5 {
    padding-left: calc(var(--spacing) * 1.5);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }

  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }

  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }

  .pl-7 {
    padding-left: calc(var(--spacing) * 7);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-\[7\.5rem\] {
    padding-left: 7.5rem;
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .text-start {
    text-align: start;
  }

  .align-middle {
    vertical-align: middle;
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .font-sans {
    font-family: var(--font-sans);
  }

  .text-large {
    font-size: 1rem;
    line-height: var(--tw-leading, 1.4rem);
    letter-spacing: var(--tw-tracking, .02rem);
    font-weight: var(--tw-font-weight, normal);
  }

  .text-micro {
    font-size: .6875rem;
    line-height: var(--tw-leading, normal);
    letter-spacing: var(--tw-tracking, .005rem);
    font-weight: var(--tw-font-weight, normal);
  }

  .text-mini {
    font-size: .75rem;
    line-height: var(--tw-leading, normal);
    letter-spacing: var(--tw-tracking, .01rem);
    font-weight: var(--tw-font-weight, normal);
  }

  .text-regular {
    font-size: .9375rem;
    line-height: var(--tw-leading, 1.4rem);
    letter-spacing: var(--tw-tracking, .02rem);
    font-weight: var(--tw-font-weight, 300);
  }

  .text-small {
    font-size: .8125rem;
    line-height: var(--tw-leading, 1.3rem);
    letter-spacing: var(--tw-tracking, 0rem);
    font-weight: var(--tw-font-weight, 300);
  }

  .text-title1 {
    font-size: 2.25rem;
    line-height: var(--tw-leading, auto);
    font-weight: var(--tw-font-weight, normal);
  }

  .text-title3 {
    font-size: 1.25rem;
    line-height: var(--tw-leading, normal);
    font-weight: var(--tw-font-weight, normal);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[0\.7rem\] {
    font-size: .7rem;
  }

  .text-\[0\.8rem\] {
    font-size: .8rem;
  }

  .text-\[4\.75rem\] {
    font-size: 4.75rem;
  }

  .text-\[4vw\] {
    font-size: 4vw;
  }

  .text-\[5vw\] {
    font-size: 5vw;
  }

  .text-\[6vw\] {
    font-size: 6vw;
  }

  .text-\[10px\] {
    font-size: 10px;
  }

  .text-\[12px\] {
    font-size: 12px;
  }

  .text-\[18px\] {
    font-size: 18px;
  }

  .text-\[40px\] {
    font-size: 40px;
  }

  .\!leading-\[0\.9\] {
    --tw-leading: .9 !important;
    line-height: .9 !important;
  }

  .leading-\[1\.0\] {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-\[1\.1\] {
    --tw-leading: 1.1;
    line-height: 1.1;
  }

  .leading-\[1\.05\] {
    --tw-leading: 1.05;
    line-height: 1.05;
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-wrap {
    text-wrap: wrap;
  }

  .break-words {
    overflow-wrap: break-word;
  }

  .text-ellipsis {
    text-overflow: ellipsis;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  .\!text-primary {
    color: hsl(var(--primary)) !important;
  }

  .text-accent-foreground {
    color: hsl(var(--accent-foreground));
  }

  .text-amber-300 {
    color: #d7a800;
  }

  .text-amber-600 {
    color: #7f6300;
  }

  .text-amber-800 {
    color: #493900;
  }

  .text-amber-800\/60 {
    color: oklab(35.1966% -.00137753 .0719415 / .6);
  }

  .text-amber-800\/80 {
    color: oklab(35.1966% -.00137753 .0719415 / .8);
  }

  .text-background {
    color: hsl(var(--background));
  }

  .text-background-primary {
    color: hsl(var(--background-primary));
  }

  .text-background-tertiary {
    color: hsl(var(--background-tertiary));
  }

  .text-black {
    color: #000;
  }

  .text-blue-200 {
    color: #90d1ff;
  }

  .text-blue-300 {
    color: #53b8ff;
  }

  .text-blue-400 {
    color: #109bff;
  }

  .text-blue-500 {
    color: #0081de;
  }

  .text-card-foreground {
    color: hsl(var(--card-foreground));
  }

  .text-current {
    color: currentColor;
  }

  .text-destructive {
    color: hsl(var(--destructive));
  }

  .text-foreground {
    color: hsl(var(--foreground));
  }

  .text-foreground-active {
    color: hsl(var(--foreground-active));
  }

  .text-foreground-hover {
    color: hsl(var(--foreground-hover));
  }

  .text-foreground-onlook {
    color: hsl(var(--foreground-onlook));
  }

  .text-foreground-onlook\/70 {
    color: hsl(var(--foreground-onlook));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground-onlook\/70 {
      color: color-mix(in oklab, hsl(var(--foreground-onlook)) 70%, transparent);
    }
  }

  .text-foreground-primary {
    color: hsl(var(--foreground-primary));
  }

  .text-foreground-primary\/50 {
    color: hsl(var(--foreground-primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground-primary\/50 {
      color: color-mix(in oklab, hsl(var(--foreground-primary)) 50%, transparent);
    }
  }

  .text-foreground-secondary {
    color: hsl(var(--foreground-secondary));
  }

  .text-foreground-secondary\/80 {
    color: hsl(var(--foreground-secondary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground-secondary\/80 {
      color: color-mix(in oklab, hsl(var(--foreground-secondary)) 80%, transparent);
    }
  }

  .text-foreground-tertiary {
    color: hsl(var(--foreground-tertiary));
  }

  .text-foreground-tertiary\/50 {
    color: hsl(var(--foreground-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground-tertiary\/50 {
      color: color-mix(in oklab, hsl(var(--foreground-tertiary)) 50%, transparent);
    }
  }

  .text-foreground-tertiary\/80 {
    color: hsl(var(--foreground-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground-tertiary\/80 {
      color: color-mix(in oklab, hsl(var(--foreground-tertiary)) 80%, transparent);
    }
  }

  .text-foreground\/50 {
    color: hsl(var(--foreground));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground\/50 {
      color: color-mix(in oklab, hsl(var(--foreground)) 50%, transparent);
    }
  }

  .text-gray-200 {
    color: #acacac;
  }

  .text-gray-300 {
    color: #929292;
  }

  .text-gray-500 {
    color: #606060;
  }

  .text-gray-600 {
    color: #494949;
  }

  .text-gray-800 {
    color: #1f1f1f;
  }

  .text-gray-900 {
    color: #1a1a1a;
  }

  .text-green-300 {
    color: #00c441;
  }

  .text-green-400 {
    color: #00a838;
  }

  .text-muted {
    color: hsl(var(--muted));
  }

  .text-muted-foreground {
    color: hsl(var(--muted-foreground));
  }

  .text-popover-foreground {
    color: hsl(var(--popover-foreground));
  }

  .text-primary {
    color: hsl(var(--primary));
  }

  .text-primary-foreground {
    color: hsl(var(--primary-foreground));
  }

  .text-purple-100 {
    color: #f7edff;
  }

  .text-purple-500 {
    color: #ae4aff;
  }

  .text-purple-600 {
    color: #920eff;
  }

  .text-purple-800 {
    color: #57009e;
  }

  .text-red, .text-red-400 {
    color: #ff5b82;
  }

  .text-red-500 {
    color: #fa003c;
  }

  .text-secondary-foreground {
    color: hsl(var(--secondary-foreground));
  }

  .text-sidebar-foreground {
    color: var(--sidebar-foreground);
  }

  .text-sidebar-foreground\/70 {
    color: var(--sidebar-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-sidebar-foreground\/70 {
      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);
    }
  }

  .text-teal-200 {
    color: #00deba;
  }

  .text-teal-300 {
    color: #00c1a2;
  }

  .text-teal-400 {
    color: #00a68b;
  }

  .text-teal-500 {
    color: #008b74;
  }

  .text-teal-700 {
    color: #005849;
  }

  .text-teal-900 {
    color: #002a23;
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-300 {
    color: #d7a800;
  }

  .text-yellow-400 {
    color: #b99000;
  }

  .capitalize {
    text-transform: capitalize;
  }

  .lowercase {
    text-transform: lowercase;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .line-through {
    text-decoration-line: line-through;
  }

  .overline {
    text-decoration-line: overline;
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .caret-\[\#FA003C\] {
    caret-color: #fa003c;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-40 {
    opacity: .4;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-80 {
    opacity: .8;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-0 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-background-secondary\/50 {
    --tw-shadow-color: hsl(var(--background-secondary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-background-secondary\/50 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, hsl(var(--background-secondary)) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-black\/20 {
    --tw-shadow-color: #0003;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-black\/20 {
      --tw-shadow-color: color-mix(in oklab, oklab(0% none none / .2) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-black\/40 {
    --tw-shadow-color: #0006;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-black\/40 {
      --tw-shadow-color: color-mix(in oklab, oklab(0% none none / .4) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-blue-500\/50 {
    --tw-shadow-color: #0081de80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-blue-500\/50 {
      --tw-shadow-color: color-mix(in oklab, oklab(59.4594% -.0573393 -.159088 / .5) var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-foreground-active {
    --tw-ring-color: hsl(var(--foreground-active));
  }

  .ring-ring\/50 {
    --tw-ring-color: hsl(var(--ring));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-ring\/50 {
      --tw-ring-color: color-mix(in oklab, hsl(var(--ring)) 50%, transparent);
    }
  }

  .ring-sidebar-ring {
    --tw-ring-color: var(--sidebar-ring);
  }

  .ring-offset-background {
    --tw-ring-offset-color: hsl(var(--background));
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .outline-0 {
    outline-style: var(--tw-outline-style);
    outline-width: 0;
  }

  .outline-4 {
    outline-style: var(--tw-outline-style);
    outline-width: 4px;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-xl {
    --tw-drop-shadow-size: drop-shadow(0 9px 7px var(--tw-drop-shadow-color, #0000001a));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur {
    --tw-backdrop-blur: blur(8px);
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-lg {
    --tw-backdrop-blur: blur(var(--blur-lg));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xl {
    --tw-backdrop-blur: blur(var(--blur-xl));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[height\] {
    transition-property: height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[left\,right\,width\] {
    transition-property: left, right, width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[margin\,opacity\] {
    transition-property: margin, opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\,height\,padding\] {
    transition-property: width, height, padding;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\] {
    transition-property: width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-none {
    transition-property: none;
  }

  .delay-300 {
    transition-delay: .3s;
  }

  .duration-100 {
    --tw-duration: .1s;
    transition-duration: .1s;
  }

  .duration-150 {
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-1000 {
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }

  .prose-stone {
    --tw-prose-body: oklch(37.4% .01 67.558);
    --tw-prose-headings: oklch(21.6% .006 56.043);
    --tw-prose-lead: oklch(44.4% .011 73.639);
    --tw-prose-links: oklch(21.6% .006 56.043);
    --tw-prose-bold: oklch(21.6% .006 56.043);
    --tw-prose-counters: oklch(55.3% .013 58.071);
    --tw-prose-bullets: oklch(86.9% .005 56.366);
    --tw-prose-hr: oklch(92.3% .003 48.717);
    --tw-prose-quotes: oklch(21.6% .006 56.043);
    --tw-prose-quote-borders: oklch(92.3% .003 48.717);
    --tw-prose-captions: oklch(55.3% .013 58.071);
    --tw-prose-kbd: oklch(21.6% .006 56.043);
    --tw-prose-kbd-shadows: NaN NaN NaN;
    --tw-prose-code: oklch(21.6% .006 56.043);
    --tw-prose-pre-code: oklch(92.3% .003 48.717);
    --tw-prose-pre-bg: oklch(26.8% .007 34.298);
    --tw-prose-th-borders: oklch(86.9% .005 56.366);
    --tw-prose-td-borders: oklch(92.3% .003 48.717);
    --tw-prose-invert-body: oklch(86.9% .005 56.366);
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: oklch(70.9% .01 56.259);
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: oklch(70.9% .01 56.259);
    --tw-prose-invert-bullets: oklch(44.4% .011 73.639);
    --tw-prose-invert-hr: oklch(37.4% .01 67.558);
    --tw-prose-invert-quotes: oklch(97% .001 106.424);
    --tw-prose-invert-quote-borders: oklch(37.4% .01 67.558);
    --tw-prose-invert-captions: oklch(70.9% .01 56.259);
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: oklch(86.9% .005 56.366);
    --tw-prose-invert-pre-bg: #00000080;
    --tw-prose-invert-th-borders: oklch(44.4% .011 73.639);
    --tw-prose-invert-td-borders: oklch(37.4% .01 67.558);
  }

  .prose-invert {
    --tw-prose-body: var(--tw-prose-invert-body);
    --tw-prose-headings: var(--tw-prose-invert-headings);
    --tw-prose-lead: var(--tw-prose-invert-lead);
    --tw-prose-links: var(--tw-prose-invert-links);
    --tw-prose-bold: var(--tw-prose-invert-bold);
    --tw-prose-counters: var(--tw-prose-invert-counters);
    --tw-prose-bullets: var(--tw-prose-invert-bullets);
    --tw-prose-hr: var(--tw-prose-invert-hr);
    --tw-prose-quotes: var(--tw-prose-invert-quotes);
    --tw-prose-quote-borders: var(--tw-prose-invert-quote-borders);
    --tw-prose-captions: var(--tw-prose-invert-captions);
    --tw-prose-kbd: var(--tw-prose-invert-kbd);
    --tw-prose-kbd-shadows: var(--tw-prose-invert-kbd-shadows);
    --tw-prose-code: var(--tw-prose-invert-code);
    --tw-prose-pre-code: var(--tw-prose-invert-pre-code);
    --tw-prose-pre-bg: var(--tw-prose-invert-pre-bg);
    --tw-prose-th-borders: var(--tw-prose-invert-th-borders);
    --tw-prose-td-borders: var(--tw-prose-invert-td-borders);
  }

  .animate-in {
    --tw-enter-opacity: initial;
    --tw-enter-scale: initial;
    --tw-enter-rotate: initial;
    --tw-enter-translate-x: initial;
    --tw-enter-translate-y: initial;
    animation-name: enter;
    animation-duration: .15s;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .\[--md-scale\:0\] {
    --md-scale: 0;
  }

  .\[stop-color\:var\(--color-gray-50\)\] {
    stop-color: var(--color-gray-50);
  }

  .\[stop-color\:var\(--color-gray-200\)\] {
    stop-color: var(--color-gray-200);
  }

  .delay-300 {
    animation-delay: .3s;
  }

  .duration-100 {
    animation-duration: .1s;
  }

  .duration-150 {
    animation-duration: .15s;
  }

  .duration-200 {
    animation-duration: .2s;
  }

  .duration-300 {
    animation-duration: .3s;
  }

  .duration-1000 {
    animation-duration: 1s;
  }

  .ease-in-out {
    animation-timing-function: cubic-bezier(.4, 0, .2, 1);
  }

  .ease-linear {
    animation-timing-function: linear;
  }

  .fade-in-0 {
    --tw-enter-opacity: 0;
  }

  .ring-inset {
    --tw-ring-inset: inset;
  }

  .running {
    animation-play-state: running;
  }

  .zoom-in-95 {
    --tw-enter-scale: .95;
  }

  .group-focus-within\/menu-item\:opacity-100:is(:where(.group\/menu-item):focus-within *) {
    opacity: 1;
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:rotate-12:is(:where(.group):hover *) {
      rotate: 12deg;
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-background-primary:is(:where(.group):hover *) {
      background-color: hsl(var(--background-primary));
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-background-secondary:is(:where(.group):hover *) {
      background-color: hsl(var(--background-secondary));
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-foreground\/20:is(:where(.group):hover *) {
      background-color: hsl(var(--foreground));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\:bg-foreground\/20:is(:where(.group):hover *) {
        background-color: color-mix(in oklab, hsl(var(--foreground)) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\:text-foreground:is(:where(.group):hover *) {
      color: hsl(var(--foreground));
    }
  }

  @media (hover: hover) {
    .group-hover\:text-foreground-active:is(:where(.group):hover *) {
      color: hsl(var(--foreground-active));
    }
  }

  @media (hover: hover) {
    .group-hover\:text-foreground-primary:is(:where(.group):hover *) {
      color: hsl(var(--foreground-primary));
    }
  }

  @media (hover: hover) {
    .group-hover\:text-foreground-secondary:is(:where(.group):hover *) {
      color: hsl(var(--foreground-secondary));
    }
  }

  @media (hover: hover) {
    .group-hover\:text-foreground-tertiary:is(:where(.group):hover *) {
      color: hsl(var(--foreground-tertiary));
    }
  }

  @media (hover: hover) {
    .group-hover\:text-teal-100:is(:where(.group):hover *) {
      color: #cbfff6;
    }
  }

  @media (hover: hover) {
    .group-hover\:text-white:is(:where(.group):hover *) {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\/menu-item\:opacity-100:is(:where(.group\/menu-item):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\/palette\:opacity-100:is(:where(.group\/palette):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\/panel\:opacity-100:is(:where(.group\/panel):hover *) {
      opacity: 1;
    }
  }

  .group-has-data-\[sidebar\=menu-action\]\/menu-item\:pr-8:is(:where(.group\/menu-item):has([data-sidebar="menu-action"]) *) {
    padding-right: calc(var(--spacing) * 8);
  }

  .group-data-\[collapsible\=icon\]\:-mt-8:is(:where(.group)[data-collapsible="icon"] *) {
    margin-top: calc(var(--spacing) * -8);
  }

  .group-data-\[collapsible\=icon\]\:hidden:is(:where(.group)[data-collapsible="icon"] *) {
    display: none;
  }

  .group-data-\[collapsible\=icon\]\:size-8\!:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--spacing) * 8) !important;
    height: calc(var(--spacing) * 8) !important;
  }

  .group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\):is(:where(.group)[data-collapsible="icon"] *) {
    width: var(--sidebar-width-icon);
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4)));
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4))  + 2px);
  }

  .group-data-\[collapsible\=icon\]\:overflow-hidden:is(:where(.group)[data-collapsible="icon"] *) {
    overflow: hidden;
  }

  .group-data-\[collapsible\=icon\]\:p-0\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 0) !important;
  }

  .group-data-\[collapsible\=icon\]\:p-2\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 2) !important;
  }

  .group-data-\[collapsible\=icon\]\:opacity-0:is(:where(.group)[data-collapsible="icon"] *) {
    opacity: 0;
  }

  .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    right: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    left: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:w-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    width: calc(var(--spacing) * 0);
  }

  .group-data-\[collapsible\=offcanvas\]\:translate-x-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .group-data-\[side\=left\]\:-right-4:is(:where(.group)[data-side="left"] *) {
    right: calc(var(--spacing) * -4);
  }

  .group-data-\[side\=left\]\:border-r:is(:where(.group)[data-side="left"] *) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .group-data-\[side\=right\]\:left-0:is(:where(.group)[data-side="right"] *) {
    left: calc(var(--spacing) * 0);
  }

  .group-data-\[side\=right\]\:rotate-180:is(:where(.group)[data-side="right"] *) {
    rotate: 180deg;
  }

  .group-data-\[side\=right\]\:border-l:is(:where(.group)[data-side="right"] *) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .group-data-\[state\=open\]\:-rotate-180:is(:where(.group)[data-state="open"] *) {
    rotate: -180deg;
  }

  .group-data-\[state\=open\]\:rotate-180:is(:where(.group)[data-state="open"] *) {
    rotate: 180deg;
  }

  .group-data-\[variant\=floating\]\:rounded-lg:is(:where(.group)[data-variant="floating"] *) {
    border-radius: var(--radius-lg);
  }

  .group-data-\[variant\=floating\]\:border:is(:where(.group)[data-variant="floating"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .group-data-\[variant\=floating\]\:border-sidebar-border:is(:where(.group)[data-variant="floating"] *) {
    border-color: var(--sidebar-border);
  }

  .group-data-\[variant\=floating\]\:shadow-sm:is(:where(.group)[data-variant="floating"] *) {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .group-data-\[vaul-drawer-direction\=bottom\]\/drawer-content\:block:is(:where(.group\/drawer-content)[data-vaul-drawer-direction="bottom"] *) {
    display: block;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:top-full:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    top: 100%;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:mt-1\.5:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    overflow: hidden;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:rounded-md:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    border-radius: var(--radius-md);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:border:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:bg-popover:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    background-color: hsl(var(--popover));
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:text-popover-foreground:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    color: hsl(var(--popover-foreground));
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:shadow:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:duration-200:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:duration-200:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    animation-duration: .2s;
  }

  @media (hover: hover) {
    .peer-hover\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button):hover ~ *) {
      color: var(--sidebar-accent-foreground);
    }
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button)[data-active="true"] ~ *) {
    color: var(--sidebar-accent-foreground);
  }

  .peer-data-\[size\=default\]\/menu-button\:top-1\.5:is(:where(.peer\/menu-button)[data-size="default"] ~ *) {
    top: calc(var(--spacing) * 1.5);
  }

  .peer-data-\[size\=lg\]\/menu-button\:top-2\.5:is(:where(.peer\/menu-button)[data-size="lg"] ~ *) {
    top: calc(var(--spacing) * 2.5);
  }

  .peer-data-\[size\=sm\]\/menu-button\:top-1:is(:where(.peer\/menu-button)[data-size="sm"] ~ *) {
    top: calc(var(--spacing) * 1);
  }

  .selection\:bg-\[\#FA003C\]\/30 ::selection, .selection\:bg-\[\#FA003C\]\/30::selection {
    background-color: oklab(62.2415% .233164 .0915698 / .3);
  }

  .selection\:bg-primary ::selection, .selection\:bg-primary::selection {
    background-color: hsl(var(--primary));
  }

  .selection\:text-\[\#FA003C\] ::selection, .selection\:text-\[\#FA003C\]::selection {
    color: #fa003c;
  }

  .selection\:text-primary-foreground ::selection, .selection\:text-primary-foreground::selection {
    color: hsl(var(--primary-foreground));
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .file\:text-foreground::file-selector-button {
    color: hsl(var(--foreground));
  }

  .placeholder\:text-foreground-disabled::placeholder {
    color: hsl(var(--foreground-disabled));
  }

  .placeholder\:text-foreground-primary\/50::placeholder {
    color: hsl(var(--foreground-primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .placeholder\:text-foreground-primary\/50::placeholder {
      color: color-mix(in oklab, hsl(var(--foreground-primary)) 50%, transparent);
    }
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: hsl(var(--muted-foreground));
  }

  .before\:absolute:before {
    content: var(--tw-content);
    position: absolute;
  }

  .before\:inset-0:before {
    content: var(--tw-content);
    inset: calc(var(--spacing) * 0);
  }

  .before\:aspect-square:before {
    content: var(--tw-content);
    aspect-ratio: 1;
  }

  .before\:size-full:before {
    content: var(--tw-content);
    width: 100%;
    height: 100%;
  }

  .before\:rounded-\(--border-radius\):before {
    content: var(--tw-content);
    border-radius: var(--border-radius);
  }

  .before\:\[background-image\:var\(--background-radial-gradient\)\]:before {
    content: var(--tw-content);
    background-image: var(--background-radial-gradient);
  }

  .before\:\[background-size\:300\%_300\%\]:before {
    content: var(--tw-content);
    background-size: 300% 300%;
  }

  .before\:\[mask-composite\:exclude\]\!:before {
    content: var(--tw-content);
    mask-composite: exclude !important;
  }

  .before\:p-\(--border-width\):before {
    content: var(--tw-content);
    padding: var(--border-width);
  }

  .before\:opacity-0:before {
    content: var(--tw-content);
    opacity: 0;
  }

  .before\:opacity-100:before {
    content: var(--tw-content);
    opacity: 1;
  }

  .before\:transition-opacity:before {
    content: var(--tw-content);
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .before\:duration-1000:before {
    content: var(--tw-content);
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .before\:will-change-\[background-position\]:before {
    content: var(--tw-content);
    will-change: background-position;
  }

  .before\:content-\[\"\"\]:before {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .before\:\[-webkit-mask-composite\:xor\]\!:before {
    content: var(--tw-content);
    -webkit-mask-composite: xor !important;
  }

  .before\:\[mask\:var\(--mask-linear-gradient\)\]:before {
    content: var(--tw-content);
    mask: var(--mask-linear-gradient);
  }

  .before\:duration-1000:before {
    content: var(--tw-content);
    animation-duration: 1s;
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:-inset-2:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * -2);
  }

  .after\:inset-y-0:after {
    content: var(--tw-content);
    inset-block: calc(var(--spacing) * 0);
  }

  .after\:left-1\/2:after {
    content: var(--tw-content);
    left: 50%;
  }

  .after\:w-\[2px\]:after {
    content: var(--tw-content);
    width: 2px;
  }

  .group-data-\[collapsible\=offcanvas\]\:after\:left-full:is(:where(.group)[data-collapsible="offcanvas"] *):after {
    content: var(--tw-content);
    left: 100%;
  }

  .first\:rounded-l-md:first-child {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }

  .first\:border-l:first-child {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .last\:mb-0:last-child {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .last\:rounded-r-md:last-child {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }

  .last\:border-b-0:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .focus-within\:relative:focus-within {
    position: relative;
  }

  .focus-within\:z-20:focus-within {
    z-index: 20;
  }

  @media (hover: hover) {
    .hover\:cursor-pointer:hover {
      cursor: pointer;
    }
  }

  @media (hover: hover) {
    .hover\:border:hover {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }

  @media (hover: hover) {
    .hover\:border-blue-200\/80:hover {
      border-color: oklab(83.3684% -.0460982 -.0801898 / .8);
    }
  }

  @media (hover: hover) {
    .hover\:border-blue-300:hover {
      border-color: #53b8ff;
    }
  }

  @media (hover: hover) {
    .hover\:border-border:hover {
      border-color: hsl(var(--border));
    }
  }

  @media (hover: hover) {
    .hover\:border-border-hover:hover {
      border-color: hsl(var(--border-hover));
    }
  }

  @media (hover: hover) {
    .hover\:border-foreground-secondary\/50:hover {
      border-color: hsl(var(--foreground-secondary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-foreground-secondary\/50:hover {
        border-color: color-mix(in oklab, hsl(var(--foreground-secondary)) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-red-500:hover {
      border-color: #fa003c;
    }
  }

  @media (hover: hover) {
    .hover\:border-teal-400:hover {
      border-color: #00a68b;
    }
  }

  @media (hover: hover) {
    .hover\:border-teal-500\/70:hover {
      border-color: oklab(56.8692% -.106409 .00830203 / .7);
    }
  }

  @media (hover: hover) {
    .hover\:\!bg-background-onlook:hover {
      background-color: hsl(var(--background-onlook)) !important;
    }
  }

  @media (hover: hover) {
    .hover\:\!bg-red-200\/80:hover {
      background-color: oklab(84.3578% .0911696 .00447112 / .8) !important;
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: hsl(var(--accent));
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent\/50:hover {
      background-color: hsl(var(--accent));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-accent\/50:hover {
        background-color: color-mix(in oklab, hsl(var(--accent)) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-amber-50:hover {
      background-color: var(--color-amber-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-amber-200:hover {
      background-color: #f6c100;
    }
  }

  @media (hover: hover) {
    .hover\:bg-background-hover:hover {
      background-color: hsl(var(--background-hover));
    }
  }

  @media (hover: hover) {
    .hover\:bg-background-onlook:hover {
      background-color: hsl(var(--background-onlook));
    }
  }

  @media (hover: hover) {
    .hover\:bg-background-primary:hover {
      background-color: hsl(var(--background-primary));
    }
  }

  @media (hover: hover) {
    .hover\:bg-background-secondary:hover {
      background-color: hsl(var(--background-secondary));
    }
  }

  @media (hover: hover) {
    .hover\:bg-background-secondary\/70:hover {
      background-color: hsl(var(--background-secondary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-background-secondary\/70:hover {
        background-color: color-mix(in oklab, hsl(var(--background-secondary)) 70%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-background-tertiary:hover {
      background-color: hsl(var(--background-tertiary));
    }
  }

  @media (hover: hover) {
    .hover\:bg-background-tertiary\/10:hover {
      background-color: hsl(var(--background-tertiary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-background-tertiary\/10:hover {
        background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-background-tertiary\/20:hover {
      background-color: hsl(var(--background-tertiary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-background-tertiary\/20:hover {
        background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-background-tertiary\/50:hover {
      background-color: hsl(var(--background-tertiary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-background-tertiary\/50:hover {
        background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-background-tertiary\/70:hover {
      background-color: hsl(var(--background-tertiary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-background-tertiary\/70:hover {
        background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 70%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-black:hover {
      background-color: #000;
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-400:hover {
      background-color: #109bff;
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-500:hover {
      background-color: #0081de;
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-500\/20:hover {
      background-color: oklab(59.4594% -.0573393 -.159088 / .2);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: #00538f;
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: hsl(var(--destructive));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, hsl(var(--destructive)) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-foreground-hover:hover {
      background-color: hsl(var(--foreground-hover));
    }
  }

  @media (hover: hover) {
    .hover\:bg-foreground-primary:hover {
      background-color: hsl(var(--foreground-primary));
    }
  }

  @media (hover: hover) {
    .hover\:bg-foreground-primary\/10:hover {
      background-color: hsl(var(--foreground-primary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-foreground-primary\/10:hover {
        background-color: color-mix(in oklab, hsl(var(--foreground-primary)) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-foreground-primary\/80:hover {
      background-color: hsl(var(--foreground-primary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-foreground-primary\/80:hover {
        background-color: color-mix(in oklab, hsl(var(--foreground-primary)) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-foreground\/5:hover {
      background-color: hsl(var(--foreground));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-foreground\/5:hover {
        background-color: color-mix(in oklab, hsl(var(--foreground)) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted:hover {
      background-color: hsl(var(--muted));
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/50:hover {
      background-color: hsl(var(--muted));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/50:hover {
        background-color: color-mix(in oklab, hsl(var(--muted)) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary:hover {
      background-color: hsl(var(--primary));
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: hsl(var(--primary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, hsl(var(--primary)) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-500:hover {
      background-color: #fa003c;
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-500\/20:hover {
      background-color: oklab(62.2415% .233164 .0915698 / .2);
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: hsl(var(--secondary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, hsl(var(--secondary)) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-sidebar-accent:hover {
      background-color: var(--sidebar-accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-teal-100:hover {
      background-color: #cbfff6;
    }
  }

  @media (hover: hover) {
    .hover\:bg-teal-200\/50:hover {
      background-color: oklab(80.4182% -.150743 .012428 / .5);
    }
  }

  @media (hover: hover) {
    .hover\:bg-teal-400:hover {
      background-color: #00a68b;
    }
  }

  @media (hover: hover) {
    .hover\:bg-teal-400\/40:hover {
      background-color: oklab(64.7657% -.121153 .00937426 / .4);
    }
  }

  @media (hover: hover) {
    .hover\:bg-transparent:hover {
      background-color: #0000;
    }
  }

  @media (hover: hover) {
    .hover\:\!text-foreground-active:hover {
      color: hsl(var(--foreground-active)) !important;
    }
  }

  @media (hover: hover) {
    .hover\:\!text-red-700:hover {
      color: #a40028 !important;
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: hsl(var(--accent-foreground));
    }
  }

  @media (hover: hover) {
    .hover\:text-amber-600:hover {
      color: #7f6300;
    }
  }

  @media (hover: hover) {
    .hover\:text-amber-900:hover {
      color: #312600;
    }
  }

  @media (hover: hover) {
    .hover\:text-background:hover {
      color: hsl(var(--background));
    }
  }

  @media (hover: hover) {
    .hover\:text-background-primary:hover {
      color: hsl(var(--background-primary));
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-500:hover {
      color: #0081de;
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground:hover {
      color: hsl(var(--foreground));
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground-active:hover {
      color: hsl(var(--foreground-active));
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground-hover:hover {
      color: hsl(var(--foreground-hover));
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground-onlook:hover {
      color: hsl(var(--foreground-onlook));
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground-primary:hover {
      color: hsl(var(--foreground-primary));
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground-secondary:hover {
      color: hsl(var(--foreground-secondary));
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-50:hover {
      color: #fff;
    }
  }

  @media (hover: hover) {
    .hover\:text-muted-foreground:hover {
      color: hsl(var(--muted-foreground));
    }
  }

  @media (hover: hover) {
    .hover\:text-primary-foreground:hover {
      color: hsl(var(--primary-foreground));
    }
  }

  @media (hover: hover) {
    .hover\:text-red:hover {
      color: #ff5b82;
    }
  }

  @media (hover: hover) {
    .hover\:text-red-600:hover {
      color: #ce0032;
    }
  }

  @media (hover: hover) {
    .hover\:text-sidebar-accent-foreground:hover {
      color: var(--sidebar-accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-teal-100:hover {
      color: #cbfff6;
    }
  }

  @media (hover: hover) {
    .hover\:text-teal-800:hover {
      color: #004036;
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:text-white\/70:hover {
      color: #ffffffb3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-white\/70:hover {
        color: color-mix(in oklab, var(--color-white) 70%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-40:hover {
      opacity: .4;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-60:hover {
      opacity: .6;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-80:hover {
      opacity: .8;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow:hover {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
      --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:ring-2:hover {
      --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:ring-4:hover {
      --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-blue-500\/70:hover {
      --tw-shadow-color: #0081deb3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:shadow-blue-500\/70:hover {
        --tw-shadow-color: color-mix(in oklab, oklab(59.4594% -.0573393 -.159088 / .7) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:group-data-\[collapsible\=offcanvas\]\:bg-sidebar:hover:is(:where(.group)[data-collapsible="offcanvas"] *) {
      background-color: var(--sidebar);
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-sidebar-border:hover:after {
      content: var(--tw-content);
      background-color: var(--sidebar-border);
    }
  }

  .focus\:z-10:focus {
    z-index: 10;
  }

  .focus\:border-blue-400\/40:focus {
    border-color: oklab(67.4489% -.0657983 -.168066 / .4);
  }

  .focus\:bg-accent:focus {
    background-color: hsl(var(--accent));
  }

  .focus\:bg-background-secondary:focus {
    background-color: hsl(var(--background-secondary));
  }

  .focus\:bg-background-tertiary:focus {
    background-color: hsl(var(--background-tertiary));
  }

  .focus\:bg-background-tertiary\/20:focus {
    background-color: hsl(var(--background-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:bg-background-tertiary\/20:focus {
      background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 20%, transparent);
    }
  }

  .focus\:bg-blue-500\/30:focus {
    background-color: oklab(59.4594% -.0573393 -.159088 / .3);
  }

  .focus\:bg-primary:focus {
    background-color: hsl(var(--primary));
  }

  .focus\:text-accent-foreground:focus {
    color: hsl(var(--accent-foreground));
  }

  .focus\:text-blue-200:focus {
    color: #90d1ff;
  }

  .focus\:text-foreground-primary:focus {
    color: hsl(var(--foreground-primary));
  }

  .focus\:text-primary-foreground:focus {
    color: hsl(var(--primary-foreground));
  }

  .focus\:shadow-\[0_0_15px_rgba\(59\,130\,246\,0\.3\)\]:focus {
    --tw-shadow: 0 0 15px var(--tw-shadow-color, #3b82f64d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-0:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-1:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: #0081de;
  }

  .focus\:ring-border:focus {
    --tw-ring-color: hsl(var(--border));
  }

  .focus\:ring-primary:focus {
    --tw-ring-color: hsl(var(--primary));
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: hsl(var(--ring));
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:z-10:focus-visible {
    z-index: 10;
  }

  .focus-visible\:border-red-500:focus-visible {
    border-color: #fa003c;
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: hsl(var(--ring));
  }

  .focus-visible\:ring-0:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-4:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, hsl(var(--destructive)) 20%, transparent);
    }
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: hsl(var(--ring));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, hsl(var(--ring)) 50%, transparent);
    }
  }

  .focus-visible\:ring-yellow-300:focus-visible {
    --tw-ring-color: #d7a800;
  }

  .focus-visible\:ring-offset-0:focus-visible {
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:outline-hidden:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus-visible\:outline-hidden:focus-visible {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus-visible\:outline-1:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .focus-visible\:outline-2:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 2px;
  }

  .focus-visible\:outline-offset-2:focus-visible {
    outline-offset: 2px;
  }

  .focus-visible\:outline-primary:focus-visible {
    outline-color: hsl(var(--primary));
  }

  .focus-visible\:outline-ring:focus-visible {
    outline-color: hsl(var(--ring));
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .active\:cursor-grabbing:active {
    cursor: grabbing;
  }

  .active\:border:active {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .active\:border-0:active {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .active\:border-border:active {
    border-color: hsl(var(--border));
  }

  .active\:bg-background-tertiary\/20:active {
    background-color: hsl(var(--background-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:bg-background-tertiary\/20:active {
      background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 20%, transparent);
    }
  }

  .active\:bg-sidebar-accent:active {
    background-color: var(--sidebar-accent);
  }

  .active\:bg-transparent:active {
    background-color: #0000;
  }

  .active\:text-sidebar-accent-foreground:active {
    color: var(--sidebar-accent-foreground);
  }

  .active\:text-white:active {
    color: var(--color-white);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  :where([data-side="left"]) .in-data-\[side\=left\]\:cursor-w-resize {
    cursor: w-resize;
  }

  :where([data-side="right"]) .in-data-\[side\=right\]\:cursor-e-resize {
    cursor: e-resize;
  }

  .has-disabled\:opacity-50:has(:disabled) {
    opacity: .5;
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .has-data-\[variant\=inset\]\:bg-sidebar:has([data-variant="inset"]) {
    background-color: var(--sidebar);
  }

  .has-\[\>svg\]\:grid-cols-\[calc\(var\(--spacing\)\*4\)_1fr\]:has( > svg) {
    grid-template-columns: calc(var(--spacing) * 4) 1fr;
  }

  .has-\[\>svg\]\:gap-x-3:has( > svg) {
    column-gap: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-disabled\:pointer-events-none[aria-disabled="true"] {
    pointer-events: none;
  }

  .aria-disabled\:opacity-50[aria-disabled="true"] {
    opacity: .5;
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: hsl(var(--destructive));
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, hsl(var(--destructive)) 20%, transparent);
    }
  }

  .aria-selected\:bg-accent[aria-selected="true"] {
    background-color: hsl(var(--accent));
  }

  .aria-selected\:bg-primary[aria-selected="true"] {
    background-color: hsl(var(--primary));
  }

  .aria-selected\:text-accent-foreground[aria-selected="true"] {
    color: hsl(var(--accent-foreground));
  }

  .aria-selected\:text-muted-foreground[aria-selected="true"] {
    color: hsl(var(--muted-foreground));
  }

  .aria-selected\:text-primary-foreground[aria-selected="true"] {
    color: hsl(var(--primary-foreground));
  }

  .aria-selected\:opacity-100[aria-selected="true"] {
    opacity: 1;
  }

  .data-\[active\=true\]\:z-10[data-active="true"] {
    z-index: 10;
  }

  .data-\[active\=true\]\:border-ring[data-active="true"] {
    border-color: hsl(var(--ring));
  }

  .data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
    background-color: hsl(var(--accent));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
      background-color: color-mix(in oklab, hsl(var(--accent)) 50%, transparent);
    }
  }

  .data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
    background-color: var(--sidebar-accent);
  }

  .data-\[active\=true\]\:font-medium[data-active="true"] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .data-\[active\=true\]\:text-accent-foreground[data-active="true"] {
    color: hsl(var(--accent-foreground));
  }

  .data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"] {
    color: var(--sidebar-accent-foreground);
  }

  .data-\[active\=true\]\:ring-\[3px\][data-active="true"] {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
    --tw-ring-color: hsl(var(--ring));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
      --tw-ring-color: color-mix(in oklab, hsl(var(--ring)) 50%, transparent);
    }
  }

  @media (hover: hover) {
    .data-\[active\=true\]\:hover\:bg-accent[data-active="true"]:hover {
      background-color: hsl(var(--accent));
    }
  }

  .data-\[active\=true\]\:focus\:bg-accent[data-active="true"]:focus {
    background-color: hsl(var(--accent));
  }

  .data-\[active\=true\]\:aria-invalid\:border-destructive[data-active="true"][aria-invalid="true"] {
    border-color: hsl(var(--destructive));
  }

  .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active="true"][aria-invalid="true"] {
    --tw-ring-color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active="true"][aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, hsl(var(--destructive)) 20%, transparent);
    }
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
    pointer-events: none;
  }

  .data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
    opacity: .5;
  }

  .data-\[error\=true\]\:text-destructive[data-error="true"] {
    color: hsl(var(--destructive));
  }

  .data-\[highlighted\]\:border-border[data-highlighted] {
    border-color: hsl(var(--border));
  }

  .data-\[highlighted\]\:bg-background-tertiary\/10[data-highlighted] {
    background-color: hsl(var(--background-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[highlighted\]\:bg-background-tertiary\/10[data-highlighted] {
      background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 10%, transparent);
    }
  }

  .data-\[highlighted\]\:text-foreground[data-highlighted] {
    color: hsl(var(--foreground));
  }

  .data-\[highlighted\]\:text-foreground-primary[data-highlighted] {
    color: hsl(var(--foreground-primary));
  }

  .data-\[highlighted\]\:text-white[data-highlighted] {
    color: var(--color-white);
  }

  .data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
  }

  .data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
    --tw-enter-translate-x: 13rem;
  }

  .data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
    --tw-enter-translate-x: -13rem;
  }

  .data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
    --tw-exit-translate-x: 13rem;
  }

  .data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
    --tw-exit-translate-x: -13rem;
  }

  .data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"] {
    --tw-enter-opacity: initial;
    --tw-enter-scale: initial;
    --tw-enter-rotate: initial;
    --tw-enter-translate-x: initial;
    --tw-enter-translate-y: initial;
    animation-name: enter;
    animation-duration: .15s;
  }

  .data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
    --tw-enter-opacity: 0;
  }

  .data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"] {
    --tw-exit-opacity: initial;
    --tw-exit-scale: initial;
    --tw-exit-rotate: initial;
    --tw-exit-translate-x: initial;
    --tw-exit-translate-y: initial;
    animation-name: exit;
    animation-duration: .15s;
  }

  .data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"] {
    --tw-exit-opacity: 0;
  }

  .data-\[orientation\=horizontal\]\:h-1\.5[data-orientation="horizontal"] {
    height: calc(var(--spacing) * 1.5);
  }

  .data-\[orientation\=horizontal\]\:h-full[data-orientation="horizontal"] {
    height: 100%;
  }

  .data-\[orientation\=horizontal\]\:h-px[data-orientation="horizontal"] {
    height: 1px;
  }

  .data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"] {
    width: 100%;
  }

  .data-\[orientation\=vertical\]\:h-full[data-orientation="vertical"] {
    height: 100%;
  }

  .data-\[orientation\=vertical\]\:min-h-44[data-orientation="vertical"] {
    min-height: calc(var(--spacing) * 44);
  }

  .data-\[orientation\=vertical\]\:w-1\.5[data-orientation="vertical"] {
    width: calc(var(--spacing) * 1.5);
  }

  .data-\[orientation\=vertical\]\:w-auto[data-orientation="vertical"] {
    width: auto;
  }

  .data-\[orientation\=vertical\]\:w-full[data-orientation="vertical"] {
    width: 100%;
  }

  .data-\[orientation\=vertical\]\:w-px[data-orientation="vertical"] {
    width: 1px;
  }

  .data-\[orientation\=vertical\]\:flex-col[data-orientation="vertical"] {
    flex-direction: column;
  }

  .data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
    color: hsl(var(--muted-foreground));
  }

  .data-\[selected\=true\]\:bg-accent[data-selected="true"] {
    background-color: hsl(var(--accent));
  }

  .data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
    color: hsl(var(--accent-foreground));
  }

  .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: -.5rem;
  }

  .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: .5rem;
  }

  .data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: -.5rem;
  }

  .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: .5rem;
  }

  .data-\[size\=default\]\:h-9[data-size="default"] {
    height: calc(var(--spacing) * 9);
  }

  .data-\[size\=sm\]\:h-8[data-size="sm"] {
    height: calc(var(--spacing) * 8);
  }

  :is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90 > *)[data-slot="alert-description"] {
    color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    :is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90 > *)[data-slot="alert-description"] {
      color: color-mix(in oklab, hsl(var(--destructive)) 90%, transparent);
    }
  }

  :is(.\*\*\:data-\[slot\=command-input-wrapper\]\:h-12 *)[data-slot="command-input-wrapper"] {
    height: calc(var(--spacing) * 12);
  }

  :is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:ring-0 *)[data-slot="navigation-menu-link"]:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  :is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:outline-none *)[data-slot="navigation-menu-link"]:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  :is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  :is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
    display: flex;
  }

  :is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
    align-items: center;
  }

  :is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
    gap: calc(var(--spacing) * 2);
  }

  .data-\[state\=active\]\:bg-background[data-state="active"] {
    background-color: hsl(var(--background));
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[state\=checked\]\:translate-x-\[calc\(100\%-2px\)\][data-state="checked"] {
    --tw-translate-x: calc(100% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[state\=checked\]\:border-primary[data-state="checked"] {
    border-color: hsl(var(--primary));
  }

  .data-\[state\=checked\]\:bg-primary[data-state="checked"] {
    background-color: hsl(var(--primary));
  }

  .data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
    color: hsl(var(--primary-foreground));
  }

  .data-\[state\=closed\]\:animate-accordion-up[data-state="closed"] {
    animation: var(--animate-accordion-up);
  }

  .data-\[state\=closed\]\:duration-300[data-state="closed"] {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .data-\[state\=closed\]\:animate-out[data-state="closed"] {
    --tw-exit-opacity: initial;
    --tw-exit-scale: initial;
    --tw-exit-rotate: initial;
    --tw-exit-translate-x: initial;
    --tw-exit-translate-y: initial;
    animation-name: exit;
    animation-duration: .15s;
  }

  .data-\[state\=closed\]\:duration-300[data-state="closed"] {
    animation-duration: .3s;
  }

  .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
    --tw-exit-translate-y: 100%;
  }

  .data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
    --tw-exit-translate-x: -100%;
  }

  .data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
    --tw-exit-translate-x: 100%;
  }

  .data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
    --tw-exit-translate-y: -100%;
  }

  .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:animate-out:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    --tw-exit-opacity: initial;
    --tw-exit-scale: initial;
    --tw-exit-rotate: initial;
    --tw-exit-translate-x: initial;
    --tw-exit-translate-y: initial;
    animation-name: exit;
    animation-duration: .15s;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:fade-out-0:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:zoom-out-95:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .data-\[state\=hidden\]\:animate-out[data-state="hidden"] {
    --tw-exit-opacity: initial;
    --tw-exit-scale: initial;
    --tw-exit-rotate: initial;
    --tw-exit-translate-x: initial;
    --tw-exit-translate-y: initial;
    animation-name: exit;
    animation-duration: .15s;
  }

  .data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=on\]\:bg-accent[data-state="on"] {
    background-color: hsl(var(--accent));
  }

  .data-\[state\=on\]\:text-accent-foreground[data-state="on"] {
    color: hsl(var(--accent-foreground));
  }

  .data-\[state\=open\]\:animate-accordion-down[data-state="open"] {
    animation: var(--animate-accordion-down);
  }

  .data-\[state\=open\]\:border[data-state="open"] {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .data-\[state\=open\]\:border-border[data-state="open"] {
    border-color: hsl(var(--border));
  }

  .data-\[state\=open\]\:bg-accent[data-state="open"] {
    background-color: hsl(var(--accent));
  }

  .data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
    background-color: hsl(var(--accent));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
      background-color: color-mix(in oklab, hsl(var(--accent)) 50%, transparent);
    }
  }

  .data-\[state\=open\]\:bg-background-tertiary\/20[data-state="open"] {
    background-color: hsl(var(--background-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[state\=open\]\:bg-background-tertiary\/20[data-state="open"] {
      background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 20%, transparent);
    }
  }

  .data-\[state\=open\]\:bg-secondary[data-state="open"] {
    background-color: hsl(var(--secondary));
  }

  .data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
    color: hsl(var(--accent-foreground));
  }

  .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: hsl(var(--muted-foreground));
  }

  .data-\[state\=open\]\:text-white[data-state="open"] {
    color: var(--color-white);
  }

  .data-\[state\=open\]\:opacity-100[data-state="open"] {
    opacity: 1;
  }

  .data-\[state\=open\]\:duration-500[data-state="open"] {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .data-\[state\=open\]\:animate-in[data-state="open"] {
    --tw-enter-opacity: initial;
    --tw-enter-scale: initial;
    --tw-enter-rotate: initial;
    --tw-enter-translate-x: initial;
    --tw-enter-translate-y: initial;
    animation-name: enter;
    animation-duration: .15s;
  }

  .data-\[state\=open\]\:duration-500[data-state="open"] {
    animation-duration: .5s;
  }

  .data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
    --tw-enter-translate-y: 100%;
  }

  .data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
    --tw-enter-translate-x: -100%;
  }

  .data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
    --tw-enter-translate-x: 100%;
  }

  .data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
    --tw-enter-translate-y: -100%;
  }

  .data-\[state\=open\]\:zoom-in-90[data-state="open"] {
    --tw-enter-scale: .9;
  }

  .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:animate-in:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    --tw-enter-opacity: initial;
    --tw-enter-scale: initial;
    --tw-enter-rotate: initial;
    --tw-enter-translate-x: initial;
    --tw-enter-translate-y: initial;
    animation-name: enter;
    animation-duration: .15s;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:fade-in-0:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:zoom-in-95:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    --tw-enter-scale: .95;
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:bg-accent[data-state="open"]:hover {
      background-color: hsl(var(--accent));
    }
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:bg-sidebar-accent[data-state="open"]:hover {
      background-color: var(--sidebar-accent);
    }
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:text-sidebar-accent-foreground[data-state="open"]:hover {
      color: var(--sidebar-accent-foreground);
    }
  }

  .data-\[state\=open\]\:focus\:bg-accent[data-state="open"]:focus {
    background-color: hsl(var(--accent));
  }

  .data-\[state\=selected\]\:bg-muted[data-state="selected"] {
    background-color: hsl(var(--muted));
  }

  .data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
    background-color: hsl(var(--input));
  }

  .data-\[state\=visible\]\:animate-in[data-state="visible"] {
    --tw-enter-opacity: initial;
    --tw-enter-scale: initial;
    --tw-enter-rotate: initial;
    --tw-enter-translate-x: initial;
    --tw-enter-translate-y: initial;
    animation-name: enter;
    animation-duration: .15s;
  }

  .data-\[state\=visible\]\:fade-in[data-state="visible"] {
    --tw-enter-opacity: 0;
  }

  .data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
    color: hsl(var(--destructive));
  }

  .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
    background-color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, hsl(var(--destructive)) 10%, transparent);
    }
  }

  .data-\[variant\=destructive\]\:focus\:text-destructive[data-variant="destructive"]:focus {
    color: hsl(var(--destructive));
  }

  .data-\[variant\=outline\]\:border-l-0[data-variant="outline"] {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  .data-\[variant\=outline\]\:shadow-xs[data-variant="outline"] {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[variant\=outline\]\:first\:border-l[data-variant="outline"]:first-child {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .data-\[vaul-drawer-direction\=bottom\]\:inset-x-0[data-vaul-drawer-direction="bottom"] {
    inset-inline: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:bottom-0[data-vaul-drawer-direction="bottom"] {
    bottom: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:mt-24[data-vaul-drawer-direction="bottom"] {
    margin-top: calc(var(--spacing) * 24);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:max-h-\[80vh\][data-vaul-drawer-direction="bottom"] {
    max-height: 80vh;
  }

  .data-\[vaul-drawer-direction\=bottom\]\:rounded-t-lg[data-vaul-drawer-direction="bottom"] {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:border-t[data-vaul-drawer-direction="bottom"] {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .data-\[vaul-drawer-direction\=left\]\:inset-y-0[data-vaul-drawer-direction="left"] {
    inset-block: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=left\]\:left-0[data-vaul-drawer-direction="left"] {
    left: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=left\]\:w-3\/4[data-vaul-drawer-direction="left"] {
    width: 75%;
  }

  .data-\[vaul-drawer-direction\=left\]\:border-r[data-vaul-drawer-direction="left"] {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .data-\[vaul-drawer-direction\=right\]\:inset-y-0[data-vaul-drawer-direction="right"] {
    inset-block: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=right\]\:right-0[data-vaul-drawer-direction="right"] {
    right: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=right\]\:w-3\/4[data-vaul-drawer-direction="right"] {
    width: 75%;
  }

  .data-\[vaul-drawer-direction\=right\]\:border-l[data-vaul-drawer-direction="right"] {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .data-\[vaul-drawer-direction\=top\]\:inset-x-0[data-vaul-drawer-direction="top"] {
    inset-inline: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=top\]\:top-0[data-vaul-drawer-direction="top"] {
    top: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=top\]\:mb-24[data-vaul-drawer-direction="top"] {
    margin-bottom: calc(var(--spacing) * 24);
  }

  .data-\[vaul-drawer-direction\=top\]\:max-h-\[80vh\][data-vaul-drawer-direction="top"] {
    max-height: 80vh;
  }

  .data-\[vaul-drawer-direction\=top\]\:rounded-b-lg[data-vaul-drawer-direction="top"] {
    border-bottom-right-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }

  .data-\[vaul-drawer-direction\=top\]\:border-b[data-vaul-drawer-direction="top"] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  @media (prefers-reduced-motion: no-preference) {
    .motion-safe\:before\:animate-shine:before {
      content: var(--tw-content);
      animation: var(--animate-shine);
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 40rem) {
    .sm\:ml-6 {
      margin-left: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:block {
      display: block;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex {
      display: flex;
    }
  }

  @media (width >= 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-sm {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-2\.5 {
      gap: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-5 {
      gap: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:pr-2\.5 {
      padding-right: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:pl-2\.5 {
      padding-left: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (width >= 40rem) {
    .data-\[vaul-drawer-direction\=left\]\:sm\:max-w-sm[data-vaul-drawer-direction="left"] {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 40rem) {
    .data-\[vaul-drawer-direction\=right\]\:sm\:max-w-sm[data-vaul-drawer-direction="right"] {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 48rem) {
    .md\:absolute {
      position: absolute;
    }
  }

  @media (width >= 48rem) {
    .md\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:mt-32 {
      margin-top: calc(var(--spacing) * 32);
    }
  }

  @media (width >= 48rem) {
    .md\:mr-8 {
      margin-right: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:block {
      display: block;
    }
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:w-1\/2 {
      width: 50%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
      width: var(--radix-navigation-menu-viewport-width);
    }
  }

  @media (width >= 48rem) {
    .md\:w-auto {
      width: auto;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[100px\] {
      max-width: 100px;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:items-center {
      align-items: center;
    }
  }

  @media (width >= 48rem) {
    .md\:items-start {
      align-items: flex-start;
    }
  }

  @media (width >= 48rem) {
    .md\:items-stretch {
      align-items: stretch;
    }
  }

  @media (width >= 48rem) {
    .md\:justify-between {
      justify-content: space-between;
    }
  }

  @media (width >= 48rem) {
    .md\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 48rem) {
    .md\:gap-0 {
      gap: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-12 {
      gap: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-24 {
      gap: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 48rem) {
    .md\:border-r {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }

  @media (width >= 48rem) {
    .md\:border-foreground-tertiary\/30 {
      border-color: hsl(var(--foreground-tertiary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .md\:border-foreground-tertiary\/30 {
        border-color: color-mix(in oklab, hsl(var(--foreground-tertiary)) 30%, transparent);
      }
    }
  }

  @media (width >= 48rem) {
    .md\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:pr-8 {
      padding-right: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-\[5vw\] {
      font-size: 5vw;
    }
  }

  @media (width >= 48rem) {
    .md\:opacity-0 {
      opacity: 0;
    }
  }

  @media (width >= 48rem) {
    .md\:\[--md-scale\:1\] {
      --md-scale: 1;
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:m-2:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:ml-0:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin-left: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:rounded-xl:is(:where(.peer)[data-variant="inset"] ~ *) {
      border-radius: var(--radius-xl);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:shadow-sm:is(:where(.peer)[data-variant="inset"] ~ *) {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2:is(:where(.peer)[data-variant="inset"] ~ *):is(:where(.peer)[data-state="collapsed"] ~ *) {
      margin-left: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:after\:hidden:after {
      content: var(--tw-content);
      display: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:block {
      display: block;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-\[200px\] {
      max-width: 200px;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  .dark\:flex:is(.dark *) {
    display: flex;
  }

  .dark\:hidden:is(.dark *) {
    display: none;
  }

  .dark\:border-amber-500\/20:is(.dark *) {
    border-color: oklab(59.2393% .00230032 .121035 / .2);
  }

  .dark\:border-input:is(.dark *) {
    border-color: hsl(var(--input));
  }

  .dark\:border-teal-300:is(.dark *) {
    border-color: #00c1a2;
  }

  .dark\:border-teal-600:is(.dark *) {
    border-color: #00715e;
  }

  .dark\:border-teal-700:is(.dark *) {
    border-color: #005849;
  }

  .dark\:border-zinc-700:is(.dark *) {
    border-color: var(--color-zinc-700);
  }

  .dark\:\!bg-\[\#FA003C\]:is(.dark *) {
    background-color: #fa003c !important;
  }

  .dark\:bg-\[\#FA003C\]\/10:is(.dark *) {
    background-color: oklab(62.2415% .233164 .0915698 / .1);
  }

  .dark\:bg-\[\#FA003C\]\/20:is(.dark *) {
    background-color: oklab(62.2415% .233164 .0915698 / .2);
  }

  .dark\:bg-\[\#FA003C\]\/90:is(.dark *) {
    background-color: oklab(62.2415% .233164 .0915698 / .9);
  }

  .dark\:bg-amber-950:is(.dark *) {
    background-color: #211a00;
  }

  .dark\:bg-background:is(.dark *) {
    background-color: hsl(var(--background));
  }

  .dark\:bg-background\/85:is(.dark *) {
    background-color: hsl(var(--background));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-background\/85:is(.dark *) {
      background-color: color-mix(in oklab, hsl(var(--background)) 85%, transparent);
    }
  }

  .dark\:bg-black:is(.dark *) {
    background-color: #000;
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, hsl(var(--destructive)) 60%, transparent);
    }
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: hsl(var(--input));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, hsl(var(--input)) 30%, transparent);
    }
  }

  .dark\:bg-purple-500\/50:is(.dark *) {
    background-color: oklab(62.6899% .147301 -.207903 / .5);
  }

  .dark\:bg-purple-900\/30:is(.dark *) {
    background-color: oklab(29.8438% .0807827 -.13671 / .3);
  }

  .dark\:bg-purple-900\/50:is(.dark *) {
    background-color: oklab(29.8438% .0807827 -.13671 / .5);
  }

  .dark\:bg-purple-900\/60:is(.dark *) {
    background-color: oklab(29.8438% .0807827 -.13671 / .6);
  }

  .dark\:bg-red-950\/50:is(.dark *) {
    background-color: oklab(23.1429% .0895901 .0230367 / .5);
  }

  .dark\:bg-teal-700:is(.dark *) {
    background-color: #005849;
  }

  .dark\:bg-teal-900\/80:is(.dark *) {
    background-color: oklab(25.5091% -.0467573 .00122999 / .8);
  }

  .dark\:bg-teal-950:is(.dark *) {
    background-color: #00211c;
  }

  .dark\:bg-transparent:is(.dark *) {
    background-color: #0000;
  }

  .dark\:bg-zinc-800:is(.dark *) {
    background-color: var(--color-zinc-800);
  }

  .dark\:fill-\[\#CECECE\]:is(.dark *) {
    fill: #cecece;
  }

  .dark\:fill-foreground:is(.dark *) {
    fill: hsl(var(--foreground));
  }

  .dark\:fill-foreground\/50:is(.dark *) {
    fill: hsl(var(--foreground));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:fill-foreground\/50:is(.dark *) {
      fill: color-mix(in oklab, hsl(var(--foreground)) 50%, transparent);
    }
  }

  .dark\:fill-gray-900:is(.dark *) {
    fill: #1a1a1a;
  }

  .dark\:fill-primary:is(.dark *) {
    fill: hsl(var(--primary));
  }

  .dark\:stroke-\[\#CECECE\]:is(.dark *) {
    stroke: #cecece;
  }

  .dark\:stroke-gray-500:is(.dark *) {
    stroke: #606060;
  }

  .dark\:stroke-gray-600:is(.dark *) {
    stroke: #494949;
  }

  .dark\:stroke-primary:is(.dark *) {
    stroke: hsl(var(--primary));
  }

  .dark\:\!text-primary:is(.dark *) {
    color: hsl(var(--primary)) !important;
  }

  .dark\:text-amber-200:is(.dark *) {
    color: #f6c100;
  }

  .dark\:text-amber-200\/60:is(.dark *) {
    color: oklab(83.4756% .00426355 .170543 / .6);
  }

  .dark\:text-amber-200\/80:is(.dark *) {
    color: oklab(83.4756% .00426355 .170543 / .8);
  }

  .dark\:text-amber-400:is(.dark *) {
    color: #b99000;
  }

  .dark\:text-gray-100:is(.dark *) {
    color: #c7c7c7;
  }

  .dark\:text-muted-foreground:is(.dark *) {
    color: hsl(var(--muted-foreground));
  }

  .dark\:text-primary:is(.dark *) {
    color: hsl(var(--primary));
  }

  .dark\:text-purple-100:is(.dark *) {
    color: #f7edff;
  }

  .dark\:text-purple-200:is(.dark *) {
    color: #e1bbff;
  }

  .dark\:text-purple-300:is(.dark *) {
    color: #d198ff;
  }

  .dark\:text-red-200:is(.dark *) {
    color: #ffb3c6;
  }

  .dark\:text-teal-100:is(.dark *) {
    color: #cbfff6;
  }

  .dark\:text-teal-200:is(.dark *) {
    color: #00deba;
  }

  .dark\:text-white:is(.dark *) {
    color: var(--color-white);
  }

  .dark\:text-yellow-200:is(.dark *) {
    color: #f6c100;
  }

  .dark\:invert:is(.dark *) {
    --tw-invert: invert(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .dark\:\[stop-color\:var\(--color-gray-700\)\]:is(.dark *) {
    stop-color: var(--color-gray-700);
  }

  .dark\:\[stop-color\:var\(--color-gray-900\)\]:is(.dark *) {
    stop-color: var(--color-gray-900);
  }

  @media (hover: hover) {
    .dark\:hover\:border-teal-400:is(.dark *):hover {
      border-color: #00a68b;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-teal-500:is(.dark *):hover {
      border-color: #008b74;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:\!bg-red-800:is(.dark *):hover {
      background-color: #7c001e !important;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: hsl(var(--accent));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, hsl(var(--accent)) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-amber-700:is(.dark *):hover {
      background-color: #644e00;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-amber-900:is(.dark *):hover {
      background-color: #312600;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: hsl(var(--input));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, hsl(var(--input)) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-teal-500\/20:is(.dark *):hover {
      background-color: oklab(56.8692% -.106409 .00830203 / .2);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-teal-700:is(.dark *):hover {
      background-color: #005849;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-teal-800:is(.dark *):hover {
      background-color: #004036;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:\!text-red-100:is(.dark *):hover {
      color: #ffecf1 !important;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-amber-100:is(.dark *):hover {
      color: #fff0bc;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-amber-400:is(.dark *):hover {
      color: #b99000;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-teal-100:is(.dark *):hover {
      color: #cbfff6;
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, hsl(var(--destructive)) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, hsl(var(--destructive)) 40%, transparent);
    }
  }

  .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active="true"][aria-invalid="true"] {
    --tw-ring-color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active="true"][aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, hsl(var(--destructive)) 40%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:bg-none:is(.dark *)[data-state="active"] {
    background-image: none;
  }

  .dark\:data-\[state\=active\]\:text-foreground:is(.dark *)[data-state="active"] {
    color: hsl(var(--foreground));
  }

  .dark\:data-\[state\=checked\]\:bg-primary:is(.dark *)[data-state="checked"] {
    background-color: hsl(var(--primary));
  }

  .dark\:data-\[state\=checked\]\:bg-primary-foreground:is(.dark *)[data-state="checked"] {
    background-color: hsl(var(--primary-foreground));
  }

  .dark\:data-\[state\=unchecked\]\:bg-foreground:is(.dark *)[data-state="unchecked"] {
    background-color: hsl(var(--foreground));
  }

  .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
    background-color: hsl(var(--input));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
      background-color: color-mix(in oklab, hsl(var(--input)) 80%, transparent);
    }
  }

  .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
    background-color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, hsl(var(--destructive)) 20%, transparent);
    }
  }

  @media (hover: hover) {
    .dark\:\[\&\]\:hover\:bg-\[\#FA003C\]:is(.dark *):hover {
      background-color: #fa003c;
    }
  }

  .\[\&_\.letter\]\:\!fill-foreground\/50 .letter {
    fill: hsl(var(--foreground)) !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&_\.letter\]\:\!fill-foreground\/50 .letter {
      fill: color-mix(in oklab, hsl(var(--foreground)) 50%, transparent) !important;
    }
  }

  .\[\&_\.letter\]\:\!fill-purple-300\/50 .letter {
    fill: oklab(77.23% .0958276 -.119797 / .5) !important;
  }

  .\[\&_\.letter\]\:\!fill-purple-400\/50 .letter {
    fill: oklab(70.0253% .124694 -.162434 / .5) !important;
  }

  .dark\:\[\&_\.letter\]\:\!fill-foreground\/50:is(.dark *) .letter {
    fill: hsl(var(--foreground)) !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:\[\&_\.letter\]\:\!fill-foreground\/50:is(.dark *) .letter {
      fill: color-mix(in oklab, hsl(var(--foreground)) 50%, transparent) !important;
    }
  }

  .dark\:\[\&_\.letter\]\:\!fill-purple-200\/50:is(.dark *) .letter {
    fill: oklab(84.7715% .0644669 -.0776359 / .5) !important;
  }

  .dark\:\[\&_\.letter\]\:\!fill-purple-300\/50:is(.dark *) .letter {
    fill: oklab(77.23% .0958276 -.119797 / .5) !important;
  }

  .\[\&_\.level\]\:\!fill-foreground .level {
    fill: hsl(var(--foreground)) !important;
  }

  .\[\&_\.level\]\:\!fill-purple-300 .level {
    fill: #d198ff !important;
  }

  .\[\&_\.level\]\:\!fill-purple-400 .level {
    fill: #c174ff !important;
  }

  .dark\:\[\&_\.level\]\:\!fill-foreground:is(.dark *) .level {
    fill: hsl(var(--foreground)) !important;
  }

  .dark\:\[\&_\.level\]\:\!fill-purple-200:is(.dark *) .level {
    fill: #e1bbff !important;
  }

  .dark\:\[\&_\.level\]\:\!fill-purple-300:is(.dark *) .level {
    fill: #d198ff !important;
  }

  .\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
    fill: hsl(var(--muted-foreground));
  }

  .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
    stroke: hsl(var(--border));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
      stroke: color-mix(in oklab, hsl(var(--border)) 50%, transparent);
    }
  }

  .\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
    stroke: hsl(var(--border));
  }

  .\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke="#fff"] {
    stroke: #0000;
  }

  .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke="#ccc"] {
    stroke: hsl(var(--border));
  }

  .\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector {
    fill: hsl(var(--muted));
  }

  .\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
    fill: hsl(var(--muted));
  }

  .\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke="#ccc"] {
    stroke: hsl(var(--border));
  }

  .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke="#fff"] {
    stroke: #0000;
  }

  .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
    color: hsl(var(--muted-foreground));
  }

  .\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~ [cmdk-group] {
    padding-top: calc(var(--spacing) * 0);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
    height: calc(var(--spacing) * 12);
  }

  .\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
    padding-block: calc(var(--spacing) * 3);
  }

  .\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_kbd\]\:text-\[1\.1em\] kbd {
    font-size: 1.1em;
  }

  .\[\&_p\]\:leading-relaxed p {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .\[\&_path\]\:\!fill-purple-300 path {
    fill: #d198ff !important;
  }

  .\[\&_path\]\:\!fill-purple-400 path {
    fill: #c174ff !important;
  }

  .\[\&_path\]\:\!fill-white path {
    fill: var(--color-white) !important;
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
    color: hsl(var(--muted-foreground));
  }

  .\[\&_tr\]\:border-b tr {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .\[\&_tr\:last-child\]\:border-0 tr:last-child {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .\[\&\:\:-moz-range-thumb\]\:h-4::-moz-range-thumb {
    height: calc(var(--spacing) * 4);
  }

  .\[\&\:\:-moz-range-thumb\]\:w-4::-moz-range-thumb {
    width: calc(var(--spacing) * 4);
  }

  .\[\&\:\:-moz-range-thumb\]\:cursor-grab::-moz-range-thumb {
    cursor: grab;
  }

  .\[\&\:\:-moz-range-thumb\]\:appearance-none::-moz-range-thumb {
    appearance: none;
  }

  .\[\&\:\:-moz-range-thumb\]\:rounded-full::-moz-range-thumb {
    border-radius: 3.40282e38px;
  }

  .\[\&\:\:-moz-range-thumb\]\:border-0::-moz-range-thumb {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .\[\&\:\:-moz-range-thumb\]\:bg-white::-moz-range-thumb {
    background-color: var(--color-white);
  }

  @media (hover: hover) {
    .hover\:\[\&\:\:-moz-range-thumb\]\:bg-white\/90:hover::-moz-range-thumb {
      background-color: #ffffffe6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:\[\&\:\:-moz-range-thumb\]\:bg-white\/90:hover::-moz-range-thumb {
        background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
      }
    }
  }

  .active\:\[\&\:\:-moz-range-thumb\]\:cursor-grabbing:active::-moz-range-thumb {
    cursor: grabbing;
  }

  .\[\&\:\:-moz-range-track\]\:h-3::-moz-range-track {
    height: calc(var(--spacing) * 3);
  }

  .\[\&\:\:-moz-range-track\]\:rounded-full::-moz-range-track {
    border-radius: 3.40282e38px;
  }

  .\[\&\:\:-moz-range-track\]\:bg-background-tertiary\/50::-moz-range-track {
    background-color: hsl(var(--background-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&\:\:-moz-range-track\]\:bg-background-tertiary\/50::-moz-range-track {
      background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 50%, transparent);
    }
  }

  .\[\&\:\:-ms-thumb\]\:h-4::-ms-thumb {
    height: calc(var(--spacing) * 4);
  }

  .\[\&\:\:-ms-thumb\]\:w-4::-ms-thumb {
    width: calc(var(--spacing) * 4);
  }

  .\[\&\:\:-ms-thumb\]\:cursor-grab::-ms-thumb {
    cursor: grab;
  }

  .\[\&\:\:-ms-thumb\]\:appearance-none::-ms-thumb {
    appearance: none;
  }

  .\[\&\:\:-ms-thumb\]\:rounded-full::-ms-thumb {
    border-radius: 3.40282e38px;
  }

  .\[\&\:\:-ms-thumb\]\:bg-white::-ms-thumb {
    background-color: var(--color-white);
  }

  @media (hover: hover) {
    .hover\:\[\&\:\:-ms-thumb\]\:bg-white\/90:hover::-ms-thumb {
      background-color: #ffffffe6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:\[\&\:\:-ms-thumb\]\:bg-white\/90:hover::-ms-thumb {
        background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
      }
    }
  }

  .active\:\[\&\:\:-ms-thumb\]\:cursor-grabbing:active::-ms-thumb {
    cursor: grabbing;
  }

  .\[\&\:\:-webkit-inner-spin-button\]\:appearance-none::-webkit-inner-spin-button {
    appearance: none;
  }

  .\[\&\:\:-webkit-outer-spin-button\]\:appearance-none::-webkit-outer-spin-button {
    appearance: none;
  }

  .\[\&\:\:-webkit-slider-runnable-track\]\:h-3::-webkit-slider-runnable-track {
    height: calc(var(--spacing) * 3);
  }

  .\[\&\:\:-webkit-slider-runnable-track\]\:rounded-full::-webkit-slider-runnable-track {
    border-radius: 3.40282e38px;
  }

  .\[\&\:\:-webkit-slider-runnable-track\]\:bg-background-tertiary\/50::-webkit-slider-runnable-track {
    background-color: hsl(var(--background-tertiary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&\:\:-webkit-slider-runnable-track\]\:bg-background-tertiary\/50::-webkit-slider-runnable-track {
      background-color: color-mix(in oklab, hsl(var(--background-tertiary)) 50%, transparent);
    }
  }

  .\[\&\:\:-webkit-slider-thumb\]\:mt-\[-2px\]::-webkit-slider-thumb {
    margin-top: -2px;
  }

  .\[\&\:\:-webkit-slider-thumb\]\:h-4::-webkit-slider-thumb {
    height: calc(var(--spacing) * 4);
  }

  .\[\&\:\:-webkit-slider-thumb\]\:w-4::-webkit-slider-thumb {
    width: calc(var(--spacing) * 4);
  }

  .\[\&\:\:-webkit-slider-thumb\]\:cursor-grab::-webkit-slider-thumb {
    cursor: grab;
  }

  .\[\&\:\:-webkit-slider-thumb\]\:appearance-none::-webkit-slider-thumb {
    appearance: none;
  }

  .\[\&\:\:-webkit-slider-thumb\]\:rounded-full::-webkit-slider-thumb {
    border-radius: 3.40282e38px;
  }

  .\[\&\:\:-webkit-slider-thumb\]\:bg-white::-webkit-slider-thumb {
    background-color: var(--color-white);
  }

  @media (hover: hover) {
    .hover\:\[\&\:\:-webkit-slider-thumb\]\:bg-white\/90:hover::-webkit-slider-thumb {
      background-color: #ffffffe6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:\[\&\:\:-webkit-slider-thumb\]\:bg-white\/90:hover::-webkit-slider-thumb {
        background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
      }
    }
  }

  .active\:\[\&\:\:-webkit-slider-thumb\]\:cursor-grabbing:active::-webkit-slider-thumb {
    cursor: grabbing;
  }

  .\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has( > .day-range-end) {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }

  .\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has( > .day-range-start) {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }

  .\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]) {
    border-radius: var(--radius-md);
  }

  .\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
    background-color: hsl(var(--accent));
  }

  .first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:first-child:has([aria-selected]) {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }

  .last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:last-child:has([aria-selected]) {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }

  .\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end) {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }

  .\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
    padding-right: calc(var(--spacing) * 0);
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  :is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
    display: flex;
  }

  :is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
    align-items: center;
  }

  :is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
    gap: calc(var(--spacing) * 2);
  }

  :is(.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive[data-variant="destructive"] > *):is(svg) {
    color: hsl(var(--destructive)) !important;
  }

  .\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
    --tw-translate-y: 2px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>button\]\:hidden > button {
    display: none;
  }

  .\[\&\>span\:last-child\]\:truncate > span:last-child {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .\[\&\>svg\]\:pointer-events-none > svg {
    pointer-events: none;
  }

  .\[\&\>svg\]\:size-3 > svg {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:size-3\.5 > svg {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .\[\&\>svg\]\:size-4 > svg {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:h-2\.5 > svg {
    height: calc(var(--spacing) * 2.5);
  }

  .\[\&\>svg\]\:h-3 > svg {
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:w-2\.5 > svg {
    width: calc(var(--spacing) * 2.5);
  }

  .\[\&\>svg\]\:w-3 > svg {
    width: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:shrink-0 > svg {
    flex-shrink: 0;
  }

  .\[\&\>svg\]\:translate-y-0\.5 > svg {
    --tw-translate-y: calc(var(--spacing) * .5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>svg\]\:text-current > svg {
    color: currentColor;
  }

  .\[\&\>svg\]\:text-muted-foreground > svg {
    color: hsl(var(--muted-foreground));
  }

  .\[\&\>svg\]\:text-sidebar-accent-foreground > svg {
    color: var(--sidebar-accent-foreground);
  }

  .\[\&\>tr\]\:last\:border-b-0 > tr:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .\[\&\[data-dragging-image\=true\]\]\:bg-teal-500\/40[data-dragging-image="true"] {
    background-color: oklab(56.8692% -.106409 .00830203 / .4);
  }

  .\[\&\[data-state\=open\]\]\:opacity-100[data-state="open"] {
    opacity: 1;
  }

  .\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state="open"] > svg {
    rotate: 180deg;
  }

  [data-side="left"][data-collapsible="offcanvas"] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
    right: calc(var(--spacing) * -2);
  }

  [data-side="left"][data-state="collapsed"] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
    cursor: e-resize;
  }

  [data-side="right"][data-collapsible="offcanvas"] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
    left: calc(var(--spacing) * -2);
  }

  [data-side="right"][data-state="collapsed"] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
    cursor: w-resize;
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-accent:hover {
      background-color: hsl(var(--accent));
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-destructive\/90:hover {
      background-color: hsl(var(--destructive));
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, hsl(var(--destructive)) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-primary\/90:hover {
      background-color: hsl(var(--primary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, hsl(var(--primary)) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-secondary\/90:hover {
      background-color: hsl(var(--secondary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-secondary\/90:hover {
        background-color: color-mix(in oklab, hsl(var(--secondary)) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:text-accent-foreground:hover {
      color: hsl(var(--accent-foreground));
    }
  }
}

:root {
  --np-primary-card-background: #dbeafe;
  --np-primary-card-background-hover: #bfdbfe;
  --np-primary-card-border: #93c5fd;
  --np-primary-card-border-hover: #60a5fa;
  --np-primary-card-text: #1e3a8a;
  --np-primary-card-subtext: #1d4ed8;
  --np-primary-icon-background: #3b82f6;
  --np-primary-icon-shape: #dbeafe;
  --np-secondary-card-background: #ccfbf1;
  --np-secondary-card-background-hover: #99f6e4;
  --np-secondary-card-border: #5eead4;
  --np-secondary-card-border-hover: #2dd4bf;
  --np-secondary-card-text: #134e4a;
  --np-secondary-card-subtext: #0f766e;
  --np-secondary-icon-background: #14b8a6;
  --np-secondary-icon-shape: #ccfbf1;
  --background: 0 0% 100%;
  --background-onlook: 0 0% 90%;
  --background-brand: 171 100% 67%;
  --background-brand-secondary: 171 100% 77%;
  --background-primary: 0 0% 100%;
  --background-secondary: 0 0% 95%;
  --background-positive: 147 100% 44%;
  --background-tertiary: 0 0% 90%;
  --background-toolbar-base: 0 0% 100%;
  --background-hover: 0 0% 95%;
  --background-active: 0 0% 90%;
  --background-disabled: 0 0% 95%;
  --foreground: 0 0% 10%;
  --foreground-onlook: 0 0% 20%;
  --foreground-brand: 344 100% 66%;
  --foreground-primary: 0 0% 10%;
  --foreground-secondary: 0 0% 20%;
  --foreground-tertiary: 0 0% 33%;
  --foreground-quadranary: 0 0% 47%;
  --foreground-positive: 147 100% 18%;
  --foreground-hover: 0 0% 12%;
  --foreground-active: 0 0% 10%;
  --foreground-disabled: 0 0% 90%;
  --primary: 0 0% 10%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 95%;
  --secondary-foreground: 0 0% 10%;
  --destructive: 344 100% 66%;
  --destructive-foreground: 0 0% 100%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 10%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 10%;
  --icon: 0 0% 33%;
  --icon-hover: 0 0% 10%;
  --icon-active: 0 0% 10%;
  --icon-disabled: 0 0% 20%;
  --border: 0 0% 90%;
  --border-active: 0 0% 67%;
  --border-hover: 0 0% 57%;
  --muted: 0 0% 95%;
  --muted-foreground: 0 0% 20%;
  --accent: 0 0% 95%;
  --accent-foreground: 0 0% 10%;
  --input: 0 0% 95%;
  --ring: 0 0% 90%;
  --radius: .5rem;
  --color-amber-default: oklch(.72 .11 178);
  --color-amber-100: oklch(.95 .05 178);
  --color-amber-200: oklch(.85 .08 178);
  --color-amber-300: oklch(.8 .09 178);
  --color-amber-400: oklch(.72 .11 178);
  --color-amber-500: oklch(.65 .12 178);
  --color-amber-600: oklch(.55 .13 178);
  --color-amber-700: oklch(.45 .14 178);
  --color-amber-800: oklch(.35 .15 178);
  --color-amber-900: oklch(.25 .16 178);
  --color-amber-950: oklch(.15 .17 178);
  --color-black-default: oklch(0 0 0);
  --color-black-30: oklch(0 0 0 / .3);
  --color-black-60: oklch(0 0 0 / .6);
  --color-black-85: oklch(0 0 0 / .85);
  --color-purple-default: oklch(.75 .2 300);
  --color-purple-100: oklch(.95 .05 300);
  --color-purple-200: oklch(.85 .1 300);
  --color-purple-300: oklch(.8 .15 300);
  --color-purple-400: oklch(.75 .2 300);
  --color-purple-500: oklch(.65 .25 300);
  --color-purple-600: oklch(.55 .3 300);
  --color-purple-700: oklch(.45 .35 300);
  --color-purple-800: oklch(.35 .4 300);
  --color-purple-900: oklch(.25 .45 300);
  --color-purple-950: oklch(.15 .5 300);
  --color-red-default: 344 100% 68%;
  --color-red-100: 344 100% 96%;
  --color-red-200: 344 100% 85%;
  --color-red-300: 344 100% 77%;
  --color-red-400: 344 100% 68%;
  --color-red-500: 344 100% 49%;
  --color-red-600: 344 100% 40%;
  --color-red-700: 344 100% 32%;
  --color-red-800: 344 100% 24%;
  --color-red-900: 344 100% 17%;
  --color-red-950: 344 100% 12%;
  --color-blue-default: 206 100% 78%;
  --color-blue-100: 206 100% 94%;
  --color-blue-200: 206 100% 78%;
  --color-blue-300: 206 100% 66%;
  --color-blue-400: 206 100% 53%;
  --color-blue-500: 206 100% 44%;
  --color-blue-600: 206 100% 35%;
  --color-blue-700: 206 100% 28%;
  --color-blue-800: 206 100% 20%;
  --color-blue-900: 206 100% 14%;
  --color-blue-950: 206 100% 9%;
  --color-gray-default: 0 0% 29%;
  --color-gray-50: 0 0% 100%;
  --color-gray-100: 0 0% 78%;
  --color-gray-200: 0 0% 67%;
  --color-gray-300: 0 0% 57%;
  --color-gray-400: 0 0% 47%;
  --color-gray-500: 0 0% 38%;
  --color-gray-600: 0 0% 29%;
  --color-gray-700: 0 0% 20%;
  --color-gray-800: 0 0% 12%;
  --color-gray-900: 0 0% 10%;
  --color-green-default: 147 100% 17%;
  --color-green-100: 147 100% 93%;
  --color-green-200: 147 100% 44%;
  --color-green-300: 147 100% 38%;
  --color-green-400: 147 100% 33%;
  --color-green-500: 147 100% 27%;
  --color-green-600: 147 100% 22%;
  --color-green-700: 147 100% 17%;
  --color-green-800: 147 100% 13%;
  --color-green-900: 147 100% 8%;
  --color-green-950: 147 100% 7%;
  --color-teal-default: 171 100% 44%;
  --color-teal-100: 171 100% 90%;
  --color-teal-200: 171 100% 44%;
  --color-teal-300: 171 100% 38%;
  --color-teal-400: 171 100% 32%;
  --color-teal-500: 171 100% 27%;
  --color-teal-600: 171 100% 22%;
  --color-teal-700: 171 100% 17%;
  --color-teal-800: 171 100% 13%;
  --color-teal-900: 171 100% 8%;
  --color-teal-950: 171 100% 6%;
  --color-yellow-default: 47 100% 20%;
  --color-yellow-100: 48 100% 87%;
  --color-yellow-200: 46 100% 48%;
  --color-yellow-300: 46 100% 42%;
  --color-yellow-400: 47 100% 36%;
  --color-yellow-500: 47 100% 30%;
  --color-yellow-600: 47 100% 25%;
  --color-yellow-700: 47 100% 20%;
  --color-yellow-800: 47 100% 14%;
  --color-yellow-900: 47 100% 9%;
  --color-yellow-950: 47 100% 6%;
  --font-size-title1: 2.25rem;
  --font-leading-title1: auto;
  --font-weight-title1: var(--font-weight-normal);
  --font-size-title2: 1.5rem;
  --font-leading-title2: normal;
  --font-weight-title2: var(--font-weight-normal);
  --font-size-title3: 1.25rem;
  --font-leading-title3: normal;
  --font-weight-title3: var(--font-weight-normal);
  --font-size-large-plus: 1rem;
  --font-leading-large-plus: 1.4rem;
  --font-weight-large-plus: var(--font-weight-medium);
  --font-tracking-large-plus: .02rem;
  --font-size-large: 1rem;
  --font-leading-large: 1.4rem;
  --font-weight-large: var(--font-weight-normal);
  --font-tracking-large: .02rem;
  --font-size-regular-plus: .9375rem;
  --font-leading-regular-plus: 1.4rem;
  --font-weight-regular-plus: var(--font-weight-medium);
  --font-tracking-regular-plus: .02rem;
  --font-size-regular: .9375rem;
  --font-leading-regular: 1.4rem;
  --font-weight-regular: var(--font-weight-light);
  --font-tracking-regular: .02rem;
  --font-size-small-plus: .8125rem;
  --font-leading-small-plus: 1.3rem;
  --font-weight-small-plus: var(--font-weight-medium);
  --font-tracking-small-plus: 0rem;
  --font-size-small: .8125rem;
  --font-leading-small: 1.3rem;
  --font-weight-small: var(--font-weight-light);
  --font-tracking-small: 0rem;
  --font-size-mini-plus: .75rem;
  --font-leading-mini-plus: normal;
  --font-weight-mini-plus: var(--font-weight-medium);
  --font-tracking-mini-plus: .01rem;
  --font-size-mini: .75rem;
  --font-leading-mini: normal;
  --font-weight-mini: var(--font-weight-normal);
  --font-tracking-mini: .01rem;
  --font-size-micro-plus: .6875rem;
  --font-leading-micro-plus: normal;
  --font-weight-micro-plus: var(--font-weight-medium);
  --font-tracking-micro-plus: .005rem;
  --font-size-micro: .6875rem;
  --font-leading-micro: normal;
  --font-weight-micro: var(--font-weight-normal);
  --font-tracking-micro: .005rem;
  --sidebar: #fafafa;
  --sidebar-foreground: #3f3f46;
  --sidebar-primary: #18181b;
  --sidebar-primary-foreground: #fafafa;
  --sidebar-accent: #f4f4f5;
  --sidebar-accent-foreground: #18181b;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #3b82f6;
}

.dark {
  --np-primary-card-background: #172554;
  --np-primary-card-background-hover: #1e3a8a;
  --np-primary-card-border: #1e40af;
  --np-primary-card-border-hover: #2563eb;
  --np-primary-card-text: #dbeafe;
  --np-primary-card-subtext: #93c5fd;
  --np-primary-icon-background: #3b82f6;
  --np-primary-icon-shape: #dbeafe;
  --np-secondary-card-background: #042f2e;
  --np-secondary-card-background-hover: #155e75;
  --np-secondary-card-border: #155e75;
  --np-secondary-card-border-hover: #0891b2;
  --np-secondary-card-text: #ccfbf1;
  --np-secondary-card-subtext: #5eead4;
  --np-secondary-icon-background: #14b8a6;
  --np-secondary-icon-shape: #ccfbf1;
  --background: 20 14.3% 4.1%;
  --background-onlook: 0 0% 10%;
  --background-brand: 171 100% 17%;
  --background-brand-secondary: 171 100% 27%;
  --background-primary: 0 0% 10%;
  --background-secondary: 0 0% 12%;
  --background-positive: 0 0% 12%;
  --background-tertiary: 0 0% 20%;
  --background-toolbar-base: 0 0% 0% .85;
  --background-hover: 0 0% 12%;
  --background-active: 0 0% 20%;
  --background-disabled: 0 0% 10%;
  --foreground: 60 9.1% 97.8%;
  --foreground-onlook: 0 0% 67%;
  --foreground-brand: 344 100% 66%;
  --foreground-primary: 0 0% 100%;
  --foreground-secondary: 0 0% 67%;
  --foreground-tertiary: 0 0% 57%;
  --foreground-quadranary: 0 0% 38%;
  --foreground-positive: 147 100% 44%;
  --foreground-hover: 0 0% 78%;
  --foreground-active: 0 0% 100%;
  --foreground-disabled: 0 0% 10%;
  --primary: 60 9.1% 97.8%;
  --primary-foreground: 24 9.8% 10%;
  --secondary: 12 6.5% 15.1%;
  --secondary-foreground: 60 9.1% 97.8%;
  --destructive: 345.35 100% 16.86%;
  --destructive-foreground: 344.21 100% 96.27%;
  --card: 20 14.3% 4.1%;
  --card-foreground: 60 9.1% 97.8%;
  --popover: 20 14.3% 4.1%;
  --popover-foreground: 60 9.1% 97.8%;
  --icon: 0 0% 57%;
  --icon-hover: 0 0% 100%;
  --icon-active: 0 0% 100%;
  --icon-disabled: 0 0% 67%;
  --border: 0 0% 12%;
  --border-active: 0 0% 47%;
  --border-hover: 0 0% 38%;
  --muted: 12 6.5% 15.1%;
  --muted-foreground: 24 5.4% 63.9%;
  --accent: 12 6.5% 15.1%;
  --accent-foreground: 60 9.1% 97.8%;
  --input: 12 6.5% 15.1%;
  --ring: 24 5.7% 82.9%;
  --sidebar: #18181b;
  --sidebar-foreground: #f4f4f5;
  --sidebar-primary: #1d4ed8;
  --sidebar-primary-foreground: #fff;
  --sidebar-accent: #27272a;
  --sidebar-accent-foreground: #f4f4f5;
  --sidebar-border: #27272a;
  --sidebar-ring: #3b82f6;
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-ordinal {
  syntax: "*";
  inherits: false
}

@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }

  to {
    height: 0;
  }
}

@keyframes layer-panel-in {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes edit-panel-in {
  from {
    transform: translateX(15rem);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes toolbar-up {
  0% {
    transform: translateY(150%)translateX(-50%);
  }

  50% {
    transform: translateY(150%)translateX(-50%);
  }

  100% {
    transform: translateY(0)translateX(-50%);
  }
}

@keyframes shine {
  0% {
    background-position: 0 0;
  }

  50% {
    background-position: 100% 100%;
  }

  100% {
    background-position: 0 0;
  }
}


/* [next]/internal/font/google/inter_9e72d27f.module.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n_wU-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n_wU-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n_wU-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n_wU-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n_wU-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n_wU-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Fallback;
  src: local(Arial);
  ascent-override: 90.44%;
  descent-override: 22.52%;
  line-gap-override: 0.0%;
  size-adjust: 107.12%;
}

.inter_9e72d27f-module__JKMi0a__className {
  font-family: Inter, Inter Fallback;
  font-style: normal;
}

.inter_9e72d27f-module__JKMi0a__variable {
  --font-inter: "Inter", "Inter Fallback";
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__60fc87c8._.css.map*/