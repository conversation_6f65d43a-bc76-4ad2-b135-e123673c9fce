{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/components/posthog-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostHogProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/components/posthog-provider.tsx <module evaluation>\",\n    \"PostHogProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qFACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/components/posthog-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostHogProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/components/posthog-provider.tsx\",\n    \"PostHogProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iEACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/hooks/use-feature-flags.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FeatureFlagsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeatureFlagsProvider() from the server but FeatureFlagsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/hooks/use-feature-flags.tsx <module evaluation>\",\n    \"FeatureFlagsProvider\",\n);\nexport const useFeatureFlags = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFeatureFlags() from the server but useFeatureFlags is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/hooks/use-feature-flags.tsx <module evaluation>\",\n    \"useFeatureFlags\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,iFACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iFACA", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/hooks/use-feature-flags.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FeatureFlagsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeatureFlagsProvider() from the server but FeatureFlagsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/hooks/use-feature-flags.tsx\",\n    \"FeatureFlagsProvider\",\n);\nexport const useFeatureFlags = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFeatureFlags() from the server but useFeatureFlags is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/hooks/use-feature-flags.tsx\",\n    \"useFeatureFlags\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,6DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,6DACA", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/trpc/react.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TRPCReactProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TRPCReactProvider() from the server but TRPCReactProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/trpc/react.tsx <module evaluation>\",\n    \"TRPCReactProvider\",\n);\nexport const api = registerClientReference(\n    function() { throw new Error(\"Attempted to call api() from the server but api is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/trpc/react.tsx <module evaluation>\",\n    \"api\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,oEACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,oEACA", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/trpc/react.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TRPCReactProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TRPCReactProvider() from the server but TRPCReactProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/trpc/react.tsx\",\n    \"TRPCReactProvider\",\n);\nexport const api = registerClientReference(\n    function() { throw new Error(\"Attempted to call api() from the server but api is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/trpc/react.tsx\",\n    \"api\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,gDACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,gDACA", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/ui/src/components/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/ui/src/components/sonner.tsx <module evaluation>\",\n    \"toast\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,uEACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,uEACA", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/ui/src/components/sonner.tsx\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/ui/src/components/sonner.tsx\",\n    \"toast\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,mDACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,mDACA", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/csb.ts"], "sourcesContent": ["export const CSB_BLANK_TEMPLATE_ID = 'c3kdf2';\nexport const CSB_PREVIEW_TASK_NAME = 'dev';\n"], "names": [], "mappings": ";;;;AAAO,MAAM,wBAAwB;AAC9B,MAAM,wBAAwB", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/dom.ts"], "sourcesContent": ["export const DOM_IGNORE_TAGS = ['SCRIPT', 'STYLE', 'LINK', 'META', 'NOSCRIPT'];\nexport const INLINE_ONLY_CONTAINERS = new Set([\n    'a',\n    'abbr',\n    'area',\n    'audio',\n    'b',\n    'bdi',\n    'bdo',\n    'br',\n    'button',\n    'canvas',\n    'cite',\n    'code',\n    'data',\n    'datalist',\n    'del',\n    'dfn',\n    'em',\n    'embed',\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'i',\n    'iframe',\n    'img',\n    'input',\n    'ins',\n    'kbd',\n    'label',\n    'li',\n    'map',\n    'mark',\n    'meter',\n    'noscript',\n    'object',\n    'output',\n    'p',\n    'picture',\n    'progress',\n    'q',\n    'ruby',\n    's',\n    'samp',\n    'script',\n    'select',\n    'slot',\n    'small',\n    'span',\n    'strong',\n    'sub',\n    'sup',\n    'svg',\n    'template',\n    'textarea',\n    'time',\n    'u',\n    'var',\n    'video',\n    'wbr',\n]);\n"], "names": [], "mappings": ";;;;AAAO,MAAM,kBAAkB;IAAC;IAAU;IAAS;IAAQ;IAAQ;CAAW;AACvE,MAAM,yBAAyB,IAAI,IAAI;IAC1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/frame.ts"], "sourcesContent": ["export enum Orientation {\n    Portrait = 'Portrait',\n    Landscape = 'Landscape',\n}\n\nexport enum Theme {\n    Light = 'light',\n    Dark = 'dark',\n    System = 'system',\n}\n\ntype DeviceOptions = Record<string, Record<string, string>>;\n\nexport const DEVICE_OPTIONS: DeviceOptions = {\n    Custom: {\n        Custom: 'Custom',\n    },\n    Phone: {\n        'Android Compact': '412x917',\n        'Android Medium': '700x840',\n        'Android Small': '360x640',\n        'Android Large': '360x800',\n        'iPhone 16': '393x852',\n        'iPhone 16 Pro': '402x874',\n        'iPhone 16 Pro Max': '440x956',\n        'iPhone 16 Plus': '430x932',\n        'iPhone 14 & 15 Pro': '430x932',\n        'iPhone 14 & 15': '393x852',\n        'iPhone 13 & 14': '390x844',\n        'iPhone 13 Pro Max': '428x926',\n        'iPhone 13 / 13 Pro': '390x844',\n        'iPhone 11 Pro Max': '414x896',\n        'iPhone 11 Pro / X': '375x812',\n        'iPhone 8 Plus': '414x736',\n        'iPhone 8': '375x667',\n        'iPhone SE': '320x568',\n    },\n    Tablet: {\n        'Android Expanded': '1280x800',\n        'Surface Pro 8': '1440x960',\n        'Surface Pro 4': '1368x912',\n        'iPad Mini 8.3': '744x1133',\n        'iPad Mini 5': '768x1024',\n        'iPad Pro 11': '834x1194',\n        'iPad Pro 12.9': '1024x1366',\n    },\n    Laptop: {\n        'MacBook Air': '1280x832',\n        MacBook: '1152x700',\n        'MacBook Pro 14': '1512x982',\n        'MacBook Pro 16': '1728x1117',\n        'MacBook Pro': '1440x900',\n        'Surface Book': '1500x1000',\n    },\n    Desktop: {\n        Desktop: '1440x1024',\n        Wireframe: '1440x1024',\n        TV: '1280x720',\n        iMac: '1280x720',\n    },\n};\n"], "names": [], "mappings": ";;;;;AAAO,IAAA,AAAK,qCAAA;;;WAAA;;AAKL,IAAA,AAAK,+BAAA;;;;WAAA;;AAQL,MAAM,iBAAgC;IACzC,QAAQ;QACJ,QAAQ;IACZ;IACA,OAAO;QACH,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;QACjB,aAAa;QACb,iBAAiB;QACjB,qBAAqB;QACrB,kBAAkB;QAClB,sBAAsB;QACtB,kBAAkB;QAClB,kBAAkB;QAClB,qBAAqB;QACrB,sBAAsB;QACtB,qBAAqB;QACrB,qBAAqB;QACrB,iBAAiB;QACjB,YAAY;QACZ,aAAa;IACjB;IACA,QAAQ;QACJ,oBAAoB;QACpB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,iBAAiB;IACrB;IACA,QAAQ;QACJ,eAAe;QACf,SAAS;QACT,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,gBAAgB;IACpB;IACA,SAAS;QACL,SAAS;QACT,WAAW;QACX,IAAI;QACJ,MAAM;IACV;AACJ", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/editor.ts"], "sourcesContent": ["import { Orientation, Theme } from './frame';\nexport const APP_NAME = 'Onlook';\nexport const APP_SCHEMA = 'onlook';\nexport const HOSTING_DOMAIN = 'onlook.live';\nexport const CUSTOM_OUTPUT_DIR = '.next-prod';\nexport const MAX_NAME_LENGTH = 50;\n\nexport enum EditorAttributes {\n    // DOM attributes\n    ONLOOK_TOOLBAR = 'onlook-toolbar',\n    ONLOOK_RECT_ID = 'onlook-rect',\n    ONLOOK_STYLESHEET_ID = 'onlook-stylesheet',\n    ONLOOK_STUB_ID = 'onlook-drag-stub',\n    ONLOOK_MOVE_KEY_PREFIX = 'olk-',\n    OVERLAY_CONTAINER_ID = 'overlay-container',\n    CANVAS_CONTAINER_ID = 'canvas-container',\n    STYLESHEET_ID = 'onlook-default-stylesheet',\n\n    // IDs\n    DATA_ONLOOK_ID = 'data-oid',\n    DATA_ONLOOK_INSTANCE_ID = 'data-oiid',\n    DATA_ONLOOK_DOM_ID = 'data-odid',\n    DATA_ONLOOK_COMPONENT_NAME = 'data-ocname',\n\n    // Data attributes\n    DATA_ONLOOK_IGNORE = 'data-onlook-ignore',\n    DATA_ONLOOK_INSERTED = 'data-onlook-inserted',\n    DATA_ONLOOK_DRAG_SAVED_STYLE = 'data-onlook-drag-saved-style',\n    DATA_ONLOOK_DRAGGING = 'data-onlook-dragging',\n    DATA_ONLOOK_DRAG_DIRECTION = 'data-onlook-drag-direction',\n    DATA_ONLOOK_DRAG_START_POSITION = 'data-onlook-drag-start-position',\n    DATA_ONLOOK_NEW_INDEX = 'data-onlook-new-index',\n    DATA_ONLOOK_EDITING_TEXT = 'data-onlook-editing-text',\n    DATA_ONLOOK_DYNAMIC_TYPE = 'data-onlook-dynamic-type',\n    DATA_ONLOOK_CORE_ELEMENT_TYPE = 'data-onlook-core-element-type',\n}\n\nexport const DefaultSettings = {\n    SCALE: 0.7,\n    PAN_POSITION: { x: 175, y: 100 },\n    URL: 'http://localhost:3000/',\n    FRAME_POSITION: { x: 0, y: 0 },\n    FRAME_DIMENSION: { width: 1536, height: 960 },\n    ASPECT_RATIO_LOCKED: false,\n    DEVICE: 'Custom:Custom',\n    THEME: Theme.System,\n    ORIENTATION: Orientation.Portrait,\n    MIN_DIMENSIONS: { width: '280px', height: '360px' },\n    COMMANDS: {\n        run: 'bun run dev',\n        build: 'bun run build',\n        install: 'bun install',\n    },\n    IMAGE_FOLDER: 'public/images',\n    IMAGE_DIMENSION: { width: '100px', height: '100px' },\n    FONT_FOLDER: 'public/fonts',\n    FONT_CONFIG: 'app/fonts.ts',\n    TAILWIND_CONFIG: 'tailwind.config.ts',\n    CHAT_SETTINGS: {\n        showSuggestions: true,\n        autoApplyCode: true,\n        expandCodeBlocks: true,\n        showMiniChat: true,\n    },\n    EDITOR_SETTINGS: {\n        shouldWarnDelete: true,\n        enableBunReplace: true,\n        buildFlags: '--no-lint',\n    },\n};\n\nexport const DEFAULT_COLOR_NAME = 'DEFAULT';\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,WAAW;AACjB,MAAM,aAAa;AACnB,MAAM,iBAAiB;AACvB,MAAM,oBAAoB;AAC1B,MAAM,kBAAkB;AAExB,IAAA,AAAK,0CAAA;IACR,iBAAiB;;;;;;;;;IAUjB,MAAM;;;;;IAMN,kBAAkB;;;;;;;;;;;WAjBV;;AA8BL,MAAM,kBAAkB;IAC3B,OAAO;IACP,cAAc;QAAE,GAAG;QAAK,GAAG;IAAI;IAC/B,KAAK;IACL,gBAAgB;QAAE,GAAG;QAAG,GAAG;IAAE;IAC7B,iBAAiB;QAAE,OAAO;QAAM,QAAQ;IAAI;IAC5C,qBAAqB;IACrB,QAAQ;IACR,OAAO,qIAAA,CAAA,QAAK,CAAC,MAAM;IACnB,aAAa,qIAAA,CAAA,cAAW,CAAC,QAAQ;IACjC,gBAAgB;QAAE,OAAO;QAAS,QAAQ;IAAQ;IAClD,UAAU;QACN,KAAK;QACL,OAAO;QACP,SAAS;IACb;IACA,cAAc;IACd,iBAAiB;QAAE,OAAO;QAAS,QAAQ;IAAQ;IACnD,aAAa;IACb,aAAa;IACb,iBAAiB;IACjB,eAAe;QACX,iBAAiB;QACjB,eAAe;QACf,kBAAkB;QAClB,cAAc;IAClB;IACA,iBAAiB;QACb,kBAAkB;QAClB,kBAAkB;QAClB,YAAY;IAChB;AACJ;AAEO,MAAM,qBAAqB", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/files.ts"], "sourcesContent": ["import { CUSTOM_OUTPUT_DIR } from './editor';\n\nexport const IGNORED_DIRECTORIES = [\n    'node_modules',\n    'dist',\n    'build',\n    'public',\n    'static',\n    '.git',\n    '.next',\n    CUSTOM_OUTPUT_DIR,\n];\n\nexport const JSX_FILE_EXTENSIONS = ['.jsx', '.tsx'];\n\nexport const JS_FILE_EXTENSIONS = ['.js', '.ts', '.mjs', '.cjs'];\n\nexport const SUPPORTED_LOCK_FILES = [\n    'bun.lock',\n    'package-lock.json',\n    'yarn.lock',\n    'pnpm-lock.yaml',\n];\n\nexport const BINARY_EXTENSIONS = [\n    '.jpg',\n    '.jpeg',\n    '.png',\n    '.gif',\n    '.bmp',\n    '.svg',\n    '.ico',\n    '.webp',\n    '.pdf',\n    '.zip',\n    '.tar',\n    '.gz',\n    '.rar',\n    '.7z',\n    '.mp3',\n    '.mp4',\n    '.wav',\n    '.avi',\n    '.mov',\n    '.wmv',\n    '.exe',\n    '.bin',\n    '.dll',\n    '.so',\n    '.dylib',\n    '.woff',\n    '.woff2',\n    '.ttf',\n    '.eot',\n    '.otf',\n];\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,sBAAsB;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA,sIAAA,CAAA,oBAAiB;CACpB;AAEM,MAAM,sBAAsB;IAAC;IAAQ;CAAO;AAE5C,MAAM,qBAAqB;IAAC;IAAO;IAAO;IAAQ;CAAO;AAEzD,MAAM,uBAAuB;IAChC;IACA;IACA;IACA;CACH;AAEM,MAAM,oBAAoB;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/freestyle.ts"], "sourcesContent": ["export const FRESTYLE_CUSTOM_HOSTNAME = '_freestyle_custom_hostname';\nexport const FREESTYLE_IP_ADDRESS = '*************';\n"], "names": [], "mappings": ";;;;AAAO,MAAM,2BAA2B;AACjC,MAAM,uBAAuB", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/language.ts"], "sourcesContent": ["export enum Language {\n    English = 'en',\n    Japanese = 'ja',\n    Chinese = 'zh',\n    Korean = 'ko',\n}\n\nexport const LANGUAGE_DISPLAY_NAMES: Record<Language, string> = {\n    [Language.English]: 'English',\n    [Language.Japanese]: '日本語',\n    [Language.Chinese]: '中文',\n    [Language.Korean]: '한국어',\n} as const;\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,kCAAA;;;;;WAAA;;AAOL,MAAM,yBAAmD;IAC5D,MAAkB,EAAE;IACpB,MAAmB,EAAE;IACrB,MAAkB,EAAE;IACpB,MAAiB,EAAE;AACvB", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/links.ts"], "sourcesContent": ["export enum Links {\n    DISCORD = 'https://discord.gg/hERDfFZCsH',\n    GITHUB = 'https://github.com/onlook-dev/onlook',\n    USAGE_DOCS = 'https://github.com/onlook-dev/onlook/wiki/How-to-set-up-my-project%3F',\n    WIKI = 'https://github.com/onlook-dev/onlook/wiki',\n    OPEN_ISSUE = 'https://github.com/onlook-dev/onlook/issues/new/choose',\n}\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,+BAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/storage.ts"], "sourcesContent": ["export const STORAGE_BUCKETS = {\n    PREVIEW_IMAGES: 'preview_images',\n} as const;\n"], "names": [], "mappings": ";;;AAAO,MAAM,kBAAkB;IAC3B,gBAAgB;AACpB", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/constants/src/index.ts"], "sourcesContent": ["export * from './csb';\nexport * from './dom';\nexport * from './editor';\nexport * from './files';\nexport * from './frame';\nexport * from './freestyle';\nexport * from './language';\nexport * from './links';\nexport * from './storage';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/i18n/request.ts"], "sourcesContent": ["import { Language } from '@onlook/constants';\r\nimport { getRequestConfig } from 'next-intl/server';\r\nimport { cookies, headers } from 'next/headers';\r\n\r\nexport default getRequestConfig(async () => {\r\n    const locale = await getLanguage();\r\n    return {\r\n        locale,\r\n        messages: (await import(`../../messages/${locale}.json`)).default,\r\n    };\r\n});\r\n\r\nexport async function getLanguage(): Promise<Language> {\r\n    const cookieStore = await cookies();\r\n    const locale = cookieStore.get('locale');\r\n\r\n    if (locale) {\r\n        return locale.value as Language;\r\n    } else {\r\n        return detectLanguage();\r\n    }\r\n}\r\n\r\nasync function detectLanguage(): Promise<Language> {\r\n    const headersList = await headers();\r\n    const acceptLanguage = headersList.get('accept-language') || '';\r\n\r\n    // Try to find a matching language from header preferences\r\n    for (const lang of acceptLanguage.split(',')) {\r\n        // Get base language code (e.g., 'en' from 'en-US')\r\n        const langCode = lang.split('-')[0];\r\n\r\n        if (Object.values(Language).includes(langCode as Language)) {\r\n            return langCode as Language;\r\n        }\r\n    }\r\n    return Language.English;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;uCAEe,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE;IAC5B,MAAM,SAAS,MAAM;IACrB,OAAO;QACH;QACA,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACrE;AACJ;AAEO,eAAe;IAClB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,YAAY,GAAG,CAAC;IAE/B,IAAI,QAAQ;QACR,OAAO,OAAO,KAAK;IACvB,OAAO;QACH,OAAO;IACX;AACJ;AAEA,eAAe;IACX,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,iBAAiB,YAAY,GAAG,CAAC,sBAAsB;IAE7D,0DAA0D;IAC1D,KAAK,MAAM,QAAQ,eAAe,KAAK,CAAC,KAAM;QAC1C,mDAAmD;QACnD,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;QAEnC,IAAI,OAAO,MAAM,CAAC,wIAAA,CAAA,WAAQ,EAAE,QAAQ,CAAC,WAAuB;YACxD,OAAO;QACX;IACJ;IACA,OAAO,wIAAA,CAAA,WAAQ,CAAC,OAAO;AAC3B", "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/_components/theme.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/app/_components/theme.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+EACA", "debugId": null}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/_components/theme.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/app/_components/theme.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2DACA", "debugId": null}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/auth/auth-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/app/auth/auth-context.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\nexport const useAuthContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthContext() from the server but useAuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/app/auth/auth-context.tsx <module evaluation>\",\n    \"useAuthContext\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+EACA", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/auth/auth-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/app/auth/auth-context.tsx\",\n    \"AuthProvider\",\n);\nexport const useAuthContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthContext() from the server but useAuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/app/auth/auth-context.tsx\",\n    \"useAuthContext\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2DACA", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/layout.tsx"], "sourcesContent": ["import '@/styles/globals.css';\r\nimport '@onlook/ui/globals.css';\r\n\r\nimport { PostHogProvider } from '@/components/posthog-provider';\r\nimport { FeatureFlagsProvider } from '@/hooks/use-feature-flags';\r\nimport { TRPCReactProvider } from '@/trpc/react';\r\nimport { Toaster } from '@onlook/ui/sonner';\r\nimport { type Metadata } from 'next';\r\nimport { NextIntlClientProvider } from 'next-intl';\r\nimport { getLocale } from 'next-intl/server';\r\nimport { Inter } from 'next/font/google';\r\nimport { ThemeProvider } from './_components/theme';\r\nimport { AuthProvider } from './auth/auth-context';\r\n\r\nexport const metadata: Metadata = {\r\n    title: 'Onlook – Cursor for Designers',\r\n    description: 'The power of Cursor for your own website. Onlook lets you edit your React website and write your changes back to code in real-time. Iterate and experiment with AI.',\r\n    icons: [{ rel: 'icon', url: '/favicon.ico' }],\r\n    openGraph: {\r\n        url: 'https://onlook.com/',\r\n        type: 'website',\r\n        title: 'Onlook – Cursor for Designers',\r\n        description: 'The power of Cursor for your own website. Onlook lets you edit your React website and write your changes back to code in real-time. Iterate and experiment with AI.',\r\n        images: [\r\n            {\r\n                url: 'https://framerusercontent.com/images/ScnnNT7JpmUya7afqGAets8.png',\r\n            },\r\n        ],\r\n    },\r\n    twitter: {\r\n        card: 'summary_large_image',\r\n        site: '@onlookdev',\r\n        creator: '@onlookdev',\r\n        title: 'Onlook – Cursor for Designers',\r\n        description: 'The power of Cursor for your own website. Onlook lets you edit your React website and write your changes back to code in real-time. Iterate and experiment with AI.',\r\n        images: [\r\n            {\r\n                url: 'https://framerusercontent.com/images/ScnnNT7JpmUya7afqGAets8.png',\r\n            },\r\n        ],\r\n    },\r\n};\r\n\r\nconst inter = Inter({\r\n    subsets: ['latin'],\r\n    variable: '--font-inter',\r\n});\r\n\r\nexport default async function RootLayout({ children }: { children: React.ReactNode }) {\r\n    const locale = await getLocale();\r\n\r\n    return (\r\n        <html lang={locale} className={inter.variable} suppressHydrationWarning>\r\n            <body>\r\n                <FeatureFlagsProvider>\r\n                    <PostHogProvider>\r\n                        <ThemeProvider\r\n                            attribute=\"class\"\r\n                            forcedTheme=\"dark\"\r\n                            enableSystem\r\n                            disableTransitionOnChange\r\n                        >\r\n                            <TRPCReactProvider>\r\n                                <AuthProvider>\r\n                                    <NextIntlClientProvider>\r\n                                        {children}\r\n                                        <Toaster />\r\n                                    </NextIntlClientProvider>\r\n                                </AuthProvider>\r\n                            </TRPCReactProvider>\r\n                        </ThemeProvider>\r\n                    </PostHogProvider>\r\n                </FeatureFlagsProvider>\r\n            </body>\r\n        </html>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AAEA;AACA;;AAEA;AACA;;;;;;;;;;;;;AAEO,MAAM,WAAqB;IAC9B,OAAO;IACP,aAAa;IACb,OAAO;QAAC;YAAE,KAAK;YAAQ,KAAK;QAAe;KAAE;IAC7C,WAAW;QACP,KAAK;QACL,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YACJ;gBACI,KAAK;YACT;SACH;IACL;IACA,SAAS;QACL,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;YACJ;gBACI,KAAK;YACT;SACH;IACL;AACJ;AAOe,eAAe,WAAW,EAAE,QAAQ,EAAiC;IAChF,MAAM,SAAS,MAAM,CAAA,GAAA,4OAAA,CAAA,YAAS,AAAD;IAE7B,qBACI,8OAAC;QAAK,MAAM;QAAQ,WAAW,yIAAA,CAAA,UAAK,CAAC,QAAQ;QAAE,wBAAwB;kBACnE,cAAA,8OAAC;sBACG,cAAA,8OAAC,iKAAA,CAAA,uBAAoB;0BACjB,cAAA,8OAAC,kKAAA,CAAA,kBAAe;8BACZ,cAAA,8OAAC,4JAAA,CAAA,gBAAa;wBACV,WAAU;wBACV,aAAY;wBACZ,YAAY;wBACZ,yBAAyB;kCAEzB,cAAA,8OAAC,8IAAA,CAAA,oBAAiB;sCACd,cAAA,8OAAC,+JAAA,CAAA,eAAY;0CACT,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;;wCAClB;sDACD,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhD", "debugId": null}}]}