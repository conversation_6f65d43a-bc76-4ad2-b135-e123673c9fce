---
title: UI Overview
description: Overview of the Onlook user interface
---

# UI Overview

Onlook's interface is designed to be intuitive for designers while providing powerful capabilities for editing React components.

## Main Interface Areas

The Onlook interface is divided into several key areas:

- **Canvas**: The central area where you see and interact with your components
- **Layers Panel**: Shows the component hierarchy, similar to layers in design tools
- **Properties Panel**: Edit properties of the selected component
- **Style Editor**: Modify Tailwind styles through a visual interface
- **Code Panel**: View and edit the generated code
- **AI Chat**: Interact with AI to help generate and modify components

## Navigation

- Use the **Project Selector** to switch between projects
- The **File Browser** allows you to navigate through your project files
- The **Breadcrumb Navigation** shows your current location in the component hierarchy

## Keyboard Shortcuts

Onlook provides various keyboard shortcuts to speed up your workflow:

- **Ctrl/Cmd + S**: Save changes
- **Ctrl/Cmd + Z**: Undo
- **Ctrl/Cmd + Shift + Z**: Redo
- **Ctrl/Cmd + C**: Copy selected component
- **Ctrl/Cmd + V**: Paste component
- **Delete**: Remove selected component
- **Escape**: Deselect current selection
