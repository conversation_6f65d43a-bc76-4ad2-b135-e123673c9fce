---
title: Design to Code Workflow
description: Learn how to convert designs to code using Onlook
---

# Design to Code Workflow

This tutorial will guide you through converting designs to code using Onlook.

## Prerequisites

Before you begin, make sure you have:

- Installed Onlook (see [Installation](/docs/getting-started/installation))
- A design file (Figma, Sketch, or image)
- Basic understanding of React and Tailwind CSS

## Step 1: Import Your Design

1. Open Onlook
2. Create a new project or open an existing one
3. Click "Import Design" in the toolbar
4. Choose your design file
5. Wait for the import to complete

## Step 2: Convert Design to Components

1. Select elements in your imported design
2. Use the "Convert to Component" option
3. Customize the generated component
4. Adjust properties and styles as needed

## Step 3: Customize Generated Code

1. Open the Code Panel
2. Review the generated code
3. Make any necessary adjustments
4. Add functionality as needed

## Step 4: Integrate with Your Workflow

1. Export the components
2. Integrate them into your existing project
3. Test the components in your application

## Next Steps

Now that you've converted designs to code, you can:

- Learn more about [AI Features](/docs/user-guide/ai-features)
- Explore [Styling](/docs/user-guide/styling)
- Try more advanced [Editing Techniques](/docs/user-guide/editing-projects)
