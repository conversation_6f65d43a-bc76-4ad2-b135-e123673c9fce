---
title: Creating Your First Project
description: Learn how to create your first project in Onlook
---

# Creating Your First Project

This tutorial will guide you through creating your first project in Onlook.

## Prerequisites

Before you begin, make sure you have:

- Installed Onlook (see [Installation](/docs/getting-started/installation))
- Basic understanding of React and Tailwind CSS

## Step 1: Create a New Project

1. Open Onlook
2. Click on "New Project" in the welcome screen
3. Choose a template or start from scratch
4. Enter a name for your project
5. Click "Create"

## Step 2: Explore the Interface

Take some time to explore the Onlook interface:

- **Canvas**: The central area where you see and interact with your components
- **Layers Panel**: Shows the component hierarchy
- **Properties Panel**: Edit properties of the selected component
- **Style Editor**: Modify Tailwind styles
- **Code Panel**: View and edit the generated code

## Step 3: Add Components

1. Click the "+" button in the toolbar
2. Choose a component from the library
3. Drag it onto the canvas
4. Use the Properties Panel to customize it

## Step 4: Style Your Components

1. Select a component on the canvas
2. Use the Style Editor to apply Tailwind classes
3. Adjust spacing, colors, typography, and other properties

## Step 5: Preview and Export

1. Click "Preview" to see how your project looks
2. Make any final adjustments
3. Click "Export" to export your project
4. Choose your export options (code, images, etc.)

## Next Steps

Now that you've created your first project, you can:

- Learn more about [Editing Projects](/docs/user-guide/editing-projects)
- Explore [AI Features](/docs/user-guide/ai-features)
- Try the [Design to Code Workflow](/docs/tutorials/design-to-code)
