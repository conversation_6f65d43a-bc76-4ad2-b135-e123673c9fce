---
title: Developers
description: Technical documentation for Onlook
---

# Developer Documentation

This section provides technical documentation for developers who want to understand Onlook's architecture, contribute to the project, or extend its functionality.

## Overview

Onlook is structured as a monorepo with several interconnected apps and packages, primarily built using Next.js, React, TypeScript, and Vite.

<Cards>
  <Card title="Running Locally" href="/docs/developer/running-locally" />
  <Card title="Contributing" href="/docs/developer/contributing" />
  <Card title="Architecture" href="/docs/developer/architecture" />
</Cards>
