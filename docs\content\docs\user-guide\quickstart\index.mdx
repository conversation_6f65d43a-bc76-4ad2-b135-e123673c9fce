---
title: Quickstart
description: Get up and running with Onlook quickly
---

# Quickstart Guide

This guide will help you get started with Onlook in just a few minutes.

![Quickstart Banner](/images/quickstart-banner.png)
*Get up and running with <PERSON>look in minutes*

## What You'll Need

- A modern web browser (Chrome, Firefox, Safari, or Edge)
- Basic familiarity with React and TailwindCSS (helpful but not required)
- A GitHub account (optional, for saving and deploying projects)

## Step 1: Clone the Repository

Start by cloning the Onlook repository:

```bash
git clone https://github.com/onlook-dev/onlook.git
cd onlook
```

## Step 2: Install Dependencies

Onlook uses <PERSON><PERSON> as its package manager:

```bash
bun install --frozen-lockfile
```

## Step 3: Start Onlook

Run the following command to start Onlook locally:

```bash
bun dev
```

This will start Onlook on [http://localhost:3000](http://localhost:3000).

## Step 4: Create Your First Project

1. Open Onlook in your browser
2. Click on "New Project" button
3. Choose a template or start from scratch
4. Give your project a name and click "Create"

![Create Project](/images/create-project.png)
*Creating a new project in Onlook*

## Step 5: Start Editing

Once your project is created, you'll be taken to the Onlook editor. Here you can:

- Add components from the component library
- Edit text and images
- Adjust styles using the style panel
- Preview your changes in real-time

## Step 6: Deploy Your Project (Optional)

When you're ready to share your project:

1. Click the "Deploy" button in the top toolbar
2. Choose your deployment options
3. Click "Deploy Now"

Your project will be deployed and you'll receive a URL to share.

## Next Steps

Now that you've created your first project, you might want to:

- Learn more about [Onlook's features](/docs/features)
- Follow our [tutorials](/docs/tutorials) for specific use cases
- Join our [Discord community](https://discord.gg/hERDfFZCsH) for help and inspiration

Congratulations! You're now ready to start building with Onlook.
