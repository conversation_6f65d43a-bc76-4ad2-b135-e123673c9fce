{"name": "@onlook/models", "description": "Common models shared between onlook packages", "version": "0.0.0", "repository": {"type": "git", "url": "https://github.com/onlook-dev/onlook.git"}, "type": "module", "main": "src/index.ts", "scripts": {"clean": "rm -rf node_modules", "lint": "eslint --fix", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "keywords": ["onlook", "models"], "author": {"name": "Onlook", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://onlook.com", "exports": {".": "./src/index.ts", "./*": "./src/*/index.ts"}, "devDependencies": {"@onlook/typescript": "*", "typescript": "^5.5.4", "ai": "^4.2.6"}, "dependencies": {"zod": "^3.23.8"}}