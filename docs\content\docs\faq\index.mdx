---
title: FAQ
description: Frequently asked questions about Onlook
---

# Frequently Asked Questions

Here are some common questions about Onlook. If you don't find your answer here, feel free to join our [Discord community](https://discord.gg/hERDfFZCsH) for help.

![FAQ Banner](/images/faq-banner.png)
*Get answers to your common questions about Onlook*

## General Questions

### What is Onlook?

Onlook is an open-source "Cursor for Designers" that enables designers to make live edits to React and TailwindCSS projects directly within the browser DOM. It provides a seamless integration between design and development.

### Is Onlook free to use?

Yes, Onlook is completely open-source and free to use. You can clone the repository from [GitHub](https://github.com/onlook-dev/onlook) and run it locally.

### What types of projects can I build with Onlook?

Onlook works with any React and TailwindCSS project. You can build websites, web applications, dashboards, and more.

## Technical Questions

### What technologies does Onlook use?

Onlook is built with:
- Next.js
- Drizzle
- Supabase
- Bun

### Can I use Onlook with my existing project?

Yes, Onlook will run on any Next.js + TailwindCSS project. You can import your project and start editing right away.

### How does Onlook compare to similar tools?

Onlook is an open-source alternative to tools like Bolt.new, Lovable, V0, and Figma Make, with its own unique features and approach to the design-to-code workflow.

## Getting Help

### Where can I report bugs or request features?

You can report bugs or request features on our [GitHub Issues page](https://github.com/onlook-dev/onlook/issues).

### How can I contribute to Onlook?

Check out our [Contributing Guide](/docs/developer/contributing) for information on how to contribute to Onlook.
