{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/utils/cn.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n    return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACtC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACxB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/utils/truncate.ts"], "sourcesContent": ["export const platformSlash = '/';\n\nexport const truncate = (str: string | null | undefined, length: number): string | null => {\n    if (!str || str.length <= length) return str ?? null;\n    return `${str.slice(0, length - 3)}...`;\n};\n\nexport function getTruncatedFileName(fileName: string): string {\n    const parts = fileName.split(platformSlash);\n    return parts[parts.length - 1] ?? '';\n}\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,gBAAgB;AAEtB,MAAM,WAAW,CAAC,KAAgC;IACrD,IAAI,CAAC,OAAO,IAAI,MAAM,IAAI,QAAQ,OAAO,OAAO;IAChD,OAAO,GAAG,IAAI,KAAK,CAAC,GAAG,SAAS,GAAG,GAAG,CAAC;AAC3C;AAEO,SAAS,qBAAqB,QAAgB;IACjD,MAAM,QAAQ,SAAS,KAAK,CAAC;IAC7B,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,IAAI;AACtC", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/utils/index.ts"], "sourcesContent": ["export * from './cn';\nexport * from './truncate';\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/icons/header-level-icons/h1Icon.tsx"], "sourcesContent": ["import type React from 'react';\n\ninterface H1IconProps {\n    className?: string;\n    letterClassName?: string;\n    levelClassName?: string;\n    [key: string]: any;\n}\n\nconst H1Icon: React.FC<H1IconProps> = ({\n    className,\n    letterClassName,\n    levelClassName,\n    ...props\n}) => (\n    <svg\n        width=\"15\"\n        height=\"15\"\n        viewBox=\"0 0 15 15\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className={className}\n        {...props}\n    >\n        <path\n            className={levelClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M10.95 4.98927C11.2538 4.98927 11.5 5.23551 11.5 5.53927V12.6877H10.4V5.53927C10.4 5.23551 10.6462 4.98927 10.95 4.98927Z\"\n        />\n        <path\n            className={levelClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M12.9 12.5174C12.9 12.7659 12.6985 12.9674 12.45 12.9674H9.45C9.20147 12.9674 9 12.7659 9 12.5174C9 12.2689 9.20147 12.0674 9.45 12.0674H12.45C12.6985 12.0674 12.9 12.2689 12.9 12.5174Z\"\n        />\n        <path\n            className={levelClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M11.3389 5.2128C11.4661 5.42627 11.3963 5.70249 11.1828 5.82976C10.5947 6.1804 10.1817 6.34855 9.89757 6.42654C9.75473 6.46574 9.64168 6.48294 9.55336 6.48806C9.50912 6.49063 9.47095 6.49017 9.43834 6.48788C9.42203 6.48674 9.40714 6.48514 9.39361 6.48324C9.38684 6.48229 9.38042 6.48127 9.37433 6.48019L9.36546 6.47854L9.36122 6.47769L9.35914 6.47726L9.3571 6.47682C9.11412 6.4246 8.95948 6.18529 9.0117 5.94231C9.0607 5.71437 9.27434 5.56417 9.50121 5.58957C9.51779 5.58861 9.56818 5.58366 9.65933 5.55864C9.84321 5.50817 10.1805 5.36767 10.7219 5.0449C10.9354 4.91762 11.2116 4.99933 11.3389 5.2128Z\"\n        />\n        <path\n            className={letterClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M8.30469 2.50078C8.30469 2.25225 8.50616 2.05078 8.75469 2.05078H12.2547C12.5032 2.05078 12.7047 2.25225 12.7047 2.50078C12.7047 2.74931 12.5032 2.95078 12.2547 2.95078H11.0503V4.00039C11.0336 4.00013 11.0168 4 11 4C10.6368 4 10.2848 4.06052 9.95034 4.17393V2.95078H8.75469C8.50616 2.95078 8.30469 2.74931 8.30469 2.50078ZM7.31541 7.05062H5.05034V2.95078H6.25469C6.50322 2.95078 6.70469 2.74931 6.70469 2.50078C6.70469 2.25225 6.50322 2.05078 6.25469 2.05078H2.75469C2.50616 2.05078 2.30469 2.25225 2.30469 2.50078C2.30469 2.74931 2.50616 2.95078 2.75469 2.95078H3.95034V12.0508H2.75469C2.50616 12.0508 2.30469 12.2523 2.30469 12.5008C2.30469 12.7493 2.50616 12.9508 2.75469 12.9508H6.25469C6.50322 12.9508 6.70469 12.7493 6.70469 12.5008C6.70469 12.2523 6.50322 12.0508 6.25469 12.0508H5.05034V7.95062H7.08824C7.14151 7.63886 7.21802 7.33787 7.31541 7.05062Z\"\n        />\n    </svg>\n);\n\nexport default H1Icon;\n"], "names": [], "mappings": ";;;;;AASA,MAAM,SAAgC,CAAC,EACnC,SAAS,EACT,eAAe,EACf,cAAc,EACd,GAAG,OACN,iBACG,8OAAC;QACG,OAAM;QACN,QAAO;QACP,SAAQ;QACR,OAAM;QACN,WAAW;QACV,GAAG,KAAK;;0BAET,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;;;;;;;uCAKC", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/icons/header-level-icons/h2Icon.tsx"], "sourcesContent": ["import type React from 'react';\n\ninterface H2IconProps {\n    className?: string;\n    letterClassName?: string;\n    levelClassName?: string;\n    [key: string]: any;\n}\n\nconst H2Icon: React.FC<H2IconProps> = ({\n    className,\n    letterClassName,\n    levelClassName,\n    ...props\n}) => (\n    <svg\n        width=\"15\"\n        height=\"15\"\n        viewBox=\"0 0 15 15\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className={className}\n        {...props}\n    >\n        <path\n            className={levelClassName}\n            d=\"M9.05 7.29992C9.05 7.54845 9.25147 7.74992 9.5 7.74992C9.74853 7.74992 9.95 7.54845 9.95 7.29992H9.05ZM10.8093 10.4177L11.1278 10.7356L10.8093 10.4177ZM9 12.0266C8.75147 12.0266 8.55 12.2281 8.55 12.4766C8.55 12.7252 8.75147 12.9266 9 12.9266V12.0266ZM12.6504 12.9266C12.8989 12.9266 13.1004 12.7252 13.1004 12.4766C13.1004 12.2281 12.8989 12.0266 12.6504 12.0266V12.9266ZM9.50039 12.5001C9.50039 12.5021 9.50259 12.4522 9.58114 12.3303C9.65564 12.2148 9.77189 12.0753 9.93149 11.9071C10.2683 11.552 10.6665 11.1985 11.1441 10.7188C12.025 9.83402 13.05 8.63938 13.05 7.20007H12.15C12.15 8.23064 11.4002 9.18601 10.5063 10.0838C10.0965 10.4953 9.60724 10.9412 9.27855 11.2877C9.10537 11.4702 8.9443 11.6571 8.82469 11.8427C8.70912 12.022 8.60039 12.248 8.60039 12.5001H9.50039ZM13.05 7.20007C13.05 6.48662 12.8238 5.90977 12.4397 5.51106C12.0576 5.11452 11.5464 4.92376 11.0379 4.9374C9.99158 4.96547 9.05 5.8345 9.05 7.29992H9.95C9.95 6.26498 10.5584 5.85059 11.0621 5.83708C11.3286 5.82993 11.5924 5.92884 11.7916 6.13554C11.9887 6.34007 12.15 6.68002 12.15 7.20007H13.05ZM9.60031 12.5001C9.60031 12.4887 9.60679 12.4324 9.67486 12.315C9.74052 12.2017 9.84384 12.0647 9.98776 11.9002C10.2793 11.567 10.6742 11.1901 11.1278 10.7356L10.4908 10.0998C10.0625 10.5288 9.62789 10.9447 9.31041 11.3076C9.14982 11.4911 9.00376 11.678 8.89616 11.8637C8.79096 12.0453 8.70031 12.2631 8.70031 12.5001H9.60031ZM11.1278 10.7356C12.0133 9.84842 13.05 8.64775 13.05 7.20007H12.15C12.15 8.23736 11.3909 9.198 10.4908 10.0998L11.1278 10.7356ZM9 12.9266H12.6504V12.0266H9V12.9266Z\"\n        />\n        <path\n            className={letterClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M8.30469 2.50078C8.30469 2.25225 8.50616 2.05078 8.75469 2.05078H12.2547C12.5032 2.05078 12.7047 2.25225 12.7047 2.50078C12.7047 2.74931 12.5032 2.95078 12.2547 2.95078H11.0503V4.00039C11.0336 4.00013 11.0168 4 11 4C10.6368 4 10.2848 4.06052 9.95034 4.17393V2.95078H8.75469C8.50616 2.95078 8.30469 2.74931 8.30469 2.50078ZM7.31541 7.05062H5.05034V2.95078H6.25469C6.50322 2.95078 6.70469 2.74931 6.70469 2.50078C6.70469 2.25225 6.50322 2.05078 6.25469 2.05078H2.75469C2.50616 2.05078 2.30469 2.25225 2.30469 2.50078C2.30469 2.74931 2.50616 2.95078 2.75469 2.95078H3.95034V12.0508H2.75469C2.50616 12.0508 2.30469 12.2523 2.30469 12.5008C2.30469 12.7493 2.50616 12.9508 2.75469 12.9508H6.25469C6.50322 12.9508 6.70469 12.7493 6.70469 12.5008C6.70469 12.2523 6.50322 12.0508 6.25469 12.0508H5.05034V7.95062H7.08824C7.14151 7.63886 7.21802 7.33787 7.31541 7.05062Z\"\n        />\n    </svg>\n);\n\nexport default H2Icon;\n"], "names": [], "mappings": ";;;;;AASA,MAAM,SAAgC,CAAC,EACnC,SAAS,EACT,eAAe,EACf,cAAc,EACd,GAAG,OACN,iBACG,8OAAC;QACG,OAAM;QACN,QAAO;QACP,SAAQ;QACR,OAAM;QACN,WAAW;QACV,GAAG,KAAK;;0BAET,8OAAC;gBACG,WAAW;gBACX,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;;;;;;;uCAKC", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/icons/header-level-icons/h3Icon.tsx"], "sourcesContent": ["import type React from 'react';\n\ninterface H3IconProps {\n    className?: string;\n    letterClassName?: string;\n    levelClassName?: string;\n    [key: string]: any;\n}\n\nconst H3Icon: React.FC<H3IconProps> = ({\n    className,\n    letterClassName,\n    levelClassName,\n    ...props\n}) => (\n    <svg\n        width=\"15\"\n        height=\"15\"\n        viewBox=\"0 0 15 15\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className={className}\n        {...props}\n    >\n        <path\n            className={letterClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M8.30469 2.50078C8.30469 2.25225 8.50616 2.05078 8.75469 2.05078H12.2547C12.5032 2.05078 12.7047 2.25225 12.7047 2.50078C12.7047 2.74931 12.5032 2.95078 12.2547 2.95078H11.0503V4.00039C11.0336 4.00013 11.0168 4 11 4C10.6368 4 10.2848 4.06052 9.95034 4.17393V2.95078H8.75469C8.50616 2.95078 8.30469 2.74931 8.30469 2.50078ZM7.31541 7.05062H5.05034V2.95078H6.25469C6.50322 2.95078 6.70469 2.74931 6.70469 2.50078C6.70469 2.25225 6.50322 2.05078 6.25469 2.05078H2.75469C2.50616 2.05078 2.30469 2.25225 2.30469 2.50078C2.30469 2.74931 2.50616 2.95078 2.75469 2.95078H3.95034V12.0508H2.75469C2.50616 12.0508 2.30469 12.2523 2.30469 12.5008C2.30469 12.7493 2.50616 12.9508 2.75469 12.9508H6.25469C6.50322 12.9508 6.70469 12.7493 6.70469 12.5008C6.70469 12.2523 6.50322 12.0508 6.25469 12.0508H5.05034V7.95062H7.08824C7.14151 7.63886 7.21802 7.33787 7.31541 7.05062Z\"\n        />\n        <path\n            className={levelClassName}\n            d=\"M12.5769 5.43111C12.9281 5.81094 13.125 6.33894 13.125 6.99809C13.125 7.73234 12.7391 8.31179 12.2365 8.69182C11.7397 9.06747 11.1011 9.27356 10.5 9.27356C10.2377 9.27356 10.025 9.06089 10.025 8.79856C10.025 8.53622 10.2377 8.32356 10.5 8.32356C10.8989 8.32356 11.3353 8.18224 11.6635 7.93406C11.9859 7.69028 12.175 7.36949 12.175 6.99809C12.175 6.53404 12.0407 6.2505 11.8794 6.07605C11.7138 5.89694 11.4778 5.78779 11.2013 5.75186C10.9226 5.71565 10.6266 5.75801 10.3808 5.85713C10.1276 5.95923 9.98082 6.09832 9.92478 6.21029C9.80738 6.44489 9.52203 6.5399 9.28743 6.4225C9.05283 6.30511 8.95782 6.01976 9.07522 5.78515C9.26918 5.39755 9.64112 5.13109 10.0255 4.97608C10.4172 4.8181 10.8774 4.75181 11.3237 4.80978C11.7722 4.86804 12.23 5.05593 12.5769 5.43111Z\"\n        />\n        <path\n            className={levelClassName}\n            d=\"M10.9515 13.2395C12.0965 13.2361 13.4 12.482 13.4 10.9978C13.4 10.3712 13.2697 9.86427 13.0352 9.46235C12.7996 9.05845 12.4766 8.79126 12.1414 8.61835C11.5175 8.29648 10.8379 8.29785 10.5687 8.2984C10.5584 8.29842 10.5486 8.29844 10.5395 8.29844C10.2633 8.29844 10.0395 8.5223 10.0395 8.79844C10.0395 9.07458 10.2633 9.29844 10.5395 9.29844C10.8066 9.29844 11.2789 9.29859 11.6829 9.50705C11.8738 9.60551 12.0447 9.74892 12.1715 9.96625C12.2994 10.1856 12.4 10.5105 12.4 10.9978C12.4 11.7413 11.7535 12.2371 10.9485 12.2395C10.5624 12.2407 10.2007 12.1183 9.9422 11.8877C9.69346 11.6658 9.5 11.3087 9.5 10.7495C9.5 10.4733 9.27614 10.2495 9 10.2495C8.72386 10.2495 8.5 10.4733 8.5 10.7495C8.5 11.5642 8.79404 12.2036 9.27655 12.634C9.74927 13.0556 10.3626 13.2413 10.9515 13.2395Z\"\n        />\n    </svg>\n);\n\nexport default H3Icon;\n"], "names": [], "mappings": ";;;;;AASA,MAAM,SAAgC,CAAC,EACnC,SAAS,EACT,eAAe,EACf,cAAc,EACd,GAAG,OACN,iBACG,8OAAC;QACG,OAAM;QACN,QAAO;QACP,SAAQ;QACR,OAAM;QACN,WAAW;QACV,GAAG,KAAK;;0BAET,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,GAAE;;;;;;;;;;;;uCAKC", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/icons/header-level-icons/h4Icon.tsx"], "sourcesContent": ["import type React from 'react';\n\ninterface H4IconProps {\n    className?: string;\n    letterClassName?: string;\n    levelClassName?: string;\n    [key: string]: any;\n}\n\nconst H4Icon: React.FC<H4IconProps> = ({\n    className,\n    letterClassName,\n    levelClassName,\n    ...props\n}) => (\n    <svg\n        width=\"15\"\n        height=\"15\"\n        viewBox=\"0 0 15 15\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className={className}\n        {...props}\n    >\n        <path\n            className={letterClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M8.30469 2.50078C8.30469 2.25225 8.50616 2.05078 8.75469 2.05078H12.2547C12.5032 2.05078 12.7047 2.25225 12.7047 2.50078C12.7047 2.74931 12.5032 2.95078 12.2547 2.95078H11.0503V4.00039C11.0336 4.00013 11.0168 4 11 4C10.6368 4 10.2848 4.06052 9.95034 4.17393V2.95078H8.75469C8.50616 2.95078 8.30469 2.74931 8.30469 2.50078ZM7.31541 7.05062H5.05034V2.95078H6.25469C6.50322 2.95078 6.70469 2.74931 6.70469 2.50078C6.70469 2.25225 6.50322 2.05078 6.25469 2.05078H2.75469C2.50616 2.05078 2.30469 2.25225 2.30469 2.50078C2.30469 2.74931 2.50616 2.95078 2.75469 2.95078H3.95034V12.0508H2.75469C2.50616 12.0508 2.30469 12.2523 2.30469 12.5008C2.30469 12.7493 2.50616 12.9508 2.75469 12.9508H6.25469C6.50322 12.9508 6.70469 12.7493 6.70469 12.5008C6.70469 12.2523 6.50322 12.0508 6.25469 12.0508H5.05034V7.95062H7.08824C7.14151 7.63886 7.21802 7.33787 7.31541 7.05062Z\"\n        />\n        <path\n            className={levelClassName}\n            d=\"M11.75 12.4513V5.30132M8.75 11.0005H12.75M8.79927 10.902L11.7509 5.25\"\n            strokeLinecap=\"round\"\n        />\n    </svg>\n);\n\nexport default H4Icon;\n"], "names": [], "mappings": ";;;;;AASA,MAAM,SAAgC,CAAC,EACnC,SAAS,EACT,eAAe,EACf,cAAc,EACd,GAAG,OACN,iBACG,8OAAC;QACG,OAAM;QACN,QAAO;QACP,SAAQ;QACR,OAAM;QACN,WAAW;QACV,GAAG,KAAK;;0BAET,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,GAAE;gBACF,eAAc;;;;;;;;;;;;uCAKX", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/icons/header-level-icons/h5Icon.tsx"], "sourcesContent": ["import type React from 'react';\n\ninterface H5IconProps {\n    className?: string;\n    letterClassName?: string;\n    levelClassName?: string;\n    [key: string]: any;\n}\n\nconst H5Icon: React.FC<H5IconProps> = ({\n    className,\n    letterClassName,\n    levelClassName,\n    ...props\n}) => (\n    <svg\n        width=\"15\"\n        height=\"15\"\n        viewBox=\"0 0 15 15\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className={className}\n        {...props}\n    >\n        <path\n            className={letterClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M8.30469 2.50078C8.30469 2.25225 8.50616 2.05078 8.75469 2.05078H12.2547C12.5032 2.05078 12.7047 2.25225 12.7047 2.50078C12.7047 2.74931 12.5032 2.95078 12.2547 2.95078H11.0503V4.00039C11.0336 4.00013 11.0168 4 11 4C10.6368 4 10.2848 4.06052 9.95034 4.17393V2.95078H8.75469C8.50616 2.95078 8.30469 2.74931 8.30469 2.50078ZM7.31541 7.05062H5.05034V2.95078H6.25469C6.50322 2.95078 6.70469 2.74931 6.70469 2.50078C6.70469 2.25225 6.50322 2.05078 6.25469 2.05078H2.75469C2.50616 2.05078 2.30469 2.25225 2.30469 2.50078C2.30469 2.74931 2.50616 2.95078 2.75469 2.95078H3.95034V12.0508H2.75469C2.50616 12.0508 2.30469 12.2523 2.30469 12.5008C2.30469 12.7493 2.50616 12.9508 2.75469 12.9508H6.25469C6.50322 12.9508 6.70469 12.7493 6.70469 12.5008C6.70469 12.2523 6.50322 12.0508 6.25469 12.0508H5.05034V7.95062H7.08824C7.14151 7.63886 7.21802 7.33787 7.31541 7.05062Z\"\n        />\n        <path\n            className={levelClassName}\n            d=\"M9.42652 8.801C11 7.5 12.9258 8.32341 12.9258 10.5C12.9258 13.1431 9.5 13.5 9 11.1706M12.4258 5.5H10.0258C9.69441 5.5 9.42578 5.76863 9.42578 6.1V8.75156\"\n            strokeLinecap=\"round\"\n        />\n    </svg>\n);\n\nexport default H5Icon;\n"], "names": [], "mappings": ";;;;;AASA,MAAM,SAAgC,CAAC,EACnC,SAAS,EACT,eAAe,EACf,cAAc,EACd,GAAG,OACN,iBACG,8OAAC;QACG,OAAM;QACN,QAAO;QACP,SAAQ;QACR,OAAM;QACN,WAAW;QACV,GAAG,KAAK;;0BAET,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,GAAE;gBACF,eAAc;;;;;;;;;;;;uCAKX", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/icons/header-level-icons/h6Icon.tsx"], "sourcesContent": ["import type React from 'react';\n\ninterface H6IconProps {\n    className?: string;\n    letterClassName?: string;\n    levelClassName?: string;\n    [key: string]: any;\n}\n\nconst H6Icon: React.FC<H6IconProps> = ({\n    className,\n    letterClassName,\n    levelClassName,\n    ...props\n}) => (\n    <svg\n        width=\"15\"\n        height=\"15\"\n        viewBox=\"0 0 15 15\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className={className}\n        {...props}\n    >\n        <path\n            className={letterClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M8.30469 2.50078C8.30469 2.25225 8.50616 2.05078 8.75469 2.05078H12.2547C12.5032 2.05078 12.7047 2.25225 12.7047 2.50078C12.7047 2.74931 12.5032 2.95078 12.2547 2.95078H11.0503V4.00039C11.0336 4.00013 11.0168 4 11 4C10.6368 4 10.2848 4.06052 9.95034 4.17393V2.95078H8.75469C8.50616 2.95078 8.30469 2.74931 8.30469 2.50078ZM7.31541 7.05062H5.05034V2.95078H6.25469C6.50322 2.95078 6.70469 2.74931 6.70469 2.50078C6.70469 2.25225 6.50322 2.05078 6.25469 2.05078H2.75469C2.50616 2.05078 2.30469 2.25225 2.30469 2.50078C2.30469 2.74931 2.50616 2.95078 2.75469 2.95078H3.95034V12.0508H2.75469C2.50616 12.0508 2.30469 12.2523 2.30469 12.5008C2.30469 12.7493 2.50616 12.9508 2.75469 12.9508H6.25469C6.50322 12.9508 6.70469 12.7493 6.70469 12.5008C6.70469 12.2523 6.50322 12.0508 6.25469 12.0508H5.05034V7.95062H7.08824C7.14151 7.63886 7.21802 7.33787 7.31541 7.05062Z\"\n        />\n        <path\n            className={levelClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M11.441 5.84145C11.0744 5.77894 10.6333 5.90169 10.2171 6.31756C9.36711 7.16688 8.95691 8.53682 9.4887 10.956C9.54205 11.1987 9.38854 11.4387 9.1458 11.4921C8.90307 11.5455 8.66304 11.3919 8.60968 11.1492C8.04335 8.57287 8.42734 6.83361 9.58094 5.68092C10.1655 5.09678 10.889 4.83433 11.5923 4.95426C12.2948 5.07406 12.8917 5.56083 13.2545 6.30145C13.3638 6.52465 13.2715 6.7942 13.0483 6.90353C12.8251 7.01285 12.5556 6.92054 12.4462 6.69735C12.1867 6.16742 11.8083 5.90409 11.441 5.84145Z\"\n        />\n        <path\n            className={levelClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M10.221 6.31323C10.6399 5.89674 11.0602 5.78044 11.4102 5.84122C11.7676 5.90329 12.1536 6.16774 12.4557 6.71644C12.5756 6.93415 12.8492 7.01347 13.067 6.89361C13.2847 6.77374 13.364 6.50008 13.2441 6.28237C12.8467 5.56052 12.2574 5.07488 11.5642 4.9545C10.8649 4.83305 10.1614 5.10092 9.58094 5.68092L9.57185 5.6898C8.47458 6.8483 8.2505 8.43483 8.60259 11.1113C8.635 11.3577 8.86103 11.5312 9.10744 11.4987C9.35384 11.4663 9.52732 11.2403 9.4949 10.9939C9.14996 8.37182 9.42535 7.15657 10.221 6.31323Z\"\n        />\n        <path\n            className={levelClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M9.95551 9.27541C9.64589 9.61067 9.44992 10.0549 9.44992 10.501C9.44992 11.0918 9.63967 11.5249 9.9105 11.8073C10.1817 12.0901 10.5616 12.251 10.9999 12.251C11.4383 12.251 11.8181 12.0901 12.0893 11.8073C12.3602 11.5249 12.5499 11.0918 12.5499 10.501C12.5499 9.91019 12.3602 9.47734 12.0894 9.19514C11.8182 8.91249 11.4384 8.75175 10.9999 8.75175C10.6553 8.75175 10.2679 8.93716 9.95551 9.27541ZM9.29433 8.6648C9.73195 8.19095 10.3446 7.85175 10.9999 7.85175C11.666 7.85175 12.2862 8.10026 12.7388 8.57204C13.1919 9.04427 13.4499 9.71103 13.4499 10.501C13.4499 11.2909 13.192 11.9578 12.7389 12.4302C12.2863 12.9022 11.6662 13.151 10.9999 13.151C10.3337 13.151 9.71353 12.9022 9.26092 12.4302C8.80789 11.9578 8.54992 11.2909 8.54992 10.501C8.54992 9.79706 8.85395 9.14165 9.29433 8.6648Z\"\n        />\n        <path\n            className={levelClassName}\n            fillRule=\"evenodd\"\n            clipRule=\"evenodd\"\n            d=\"M10.9999 8.75175C10.2287 8.75175 9.49992 9.53811 9.49992 10.501C9.49992 11.095 9.68618 11.5295 9.94976 11.8114C10.213 12.0929 10.5791 12.251 10.9999 12.251C11.4207 12.251 11.7869 12.0929 12.0501 11.8114C12.3137 11.5295 12.4999 11.095 12.4999 10.501C12.4999 9.90695 12.3137 9.4727 12.0502 9.19101C11.787 8.90973 11.4208 8.75175 10.9999 8.75175ZM8.59992 10.501C8.59992 9.16382 9.61721 7.85175 10.9999 7.85175C11.656 7.85175 12.2648 8.10302 12.7074 8.57617C13.1496 9.04891 13.3999 9.71427 13.3999 10.501C13.3999 11.2877 13.1497 11.9532 12.7075 12.4261C12.2649 12.8994 11.6561 13.151 10.9999 13.151C10.3437 13.151 9.73493 12.8994 9.29236 12.4261C8.85019 11.9532 8.59992 11.2877 8.59992 10.501Z\"\n        />\n    </svg>\n);\n\nexport default H6Icon;\n"], "names": [], "mappings": ";;;;;AASA,MAAM,SAAgC,CAAC,EACnC,SAAS,EACT,eAAe,EACf,cAAc,EACd,GAAG,OACN,iBACG,8OAAC;QACG,OAAM;QACN,QAAO;QACP,SAAQ;QACR,OAAM;QACN,WAAW;QACV,GAAG,KAAK;;0BAET,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEN,8OAAC;gBACG,WAAW;gBACX,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;;;;;;;uCAKC", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/icons/index.tsx"], "sourcesContent": ["import {\n    AlignBottomIcon,\n    AlignCenterHorizontallyIcon,\n    AlignCenterVerticallyIcon,\n    AlignLeftIcon,\n    AlignRightIcon,\n    AlignTopIcon,\n    ArrowDownIcon,\n    ArrowLeftIcon,\n    ArrowRightIcon,\n    ArrowUpIcon,\n    BookmarkFilledIcon,\n    BookmarkIcon,\n    BorderAllIcon,\n    BorderBottomIcon,\n    BorderDashedIcon,\n    BorderDottedIcon,\n    BorderLeftIcon,\n    BorderRightIcon,\n    BorderSolidIcon,\n    BorderTopIcon,\n    BoxIcon,\n    ButtonIcon,\n    ChatBubbleIcon,\n    CheckCircledIcon,\n    CheckIcon,\n    CheckboxIcon,\n    ChevronDownIcon,\n    ChevronRightIcon,\n    ChevronUpIcon,\n    CircleBackslashIcon,\n    CircleIcon,\n    ClipboardCopyIcon,\n    ClipboardIcon,\n    CodeIcon,\n    ComponentInstanceIcon,\n    CopyIcon,\n    CornerTopLeftIcon,\n    CornersIcon,\n    CounterClockwiseClockIcon,\n    Cross1Icon,\n    Cross2Icon,\n    CrossCircledIcon,\n    CubeIcon,\n    CursorArrowIcon,\n    DesktopIcon,\n    DiscordLogoIcon,\n    DotsHorizontalIcon,\n    DotsVerticalIcon,\n    DownloadIcon,\n    DragHandleDots2Icon,\n    DropdownMenuIcon,\n    EnvelopeClosedIcon,\n    ExclamationTriangleIcon,\n    ExitIcon,\n    ExternalLinkIcon,\n    EyeClosedIcon,\n    EyeOpenIcon,\n    FileIcon,\n    FilePlusIcon,\n    FrameIcon,\n    GearIcon,\n    GitHubLogoIcon,\n    GlobeIcon,\n    GroupIcon,\n    HandIcon,\n    ImageIcon,\n    InfoCircledIcon,\n    InputIcon,\n    KeyboardIcon,\n    LaptopIcon,\n    Link2Icon,\n    LinkNone1Icon,\n    ListBulletIcon,\n    LockClosedIcon,\n    LockOpen1Icon,\n    MagicWandIcon,\n    MagnifyingGlassIcon,\n    MinusCircledIcon,\n    MinusIcon,\n    MixerHorizontalIcon,\n    MixerVerticalIcon,\n    MobileIcon,\n    MoonIcon,\n    Pencil1Icon,\n    Pencil2Icon,\n    PersonIcon,\n    PilcrowIcon,\n    PinLeftIcon,\n    PinRightIcon,\n    PlusCircledIcon,\n    PlusIcon,\n    QuestionMarkCircledIcon,\n    ReloadIcon,\n    ResetIcon,\n    RowSpacingIcon,\n    ScissorsIcon,\n    SectionIcon,\n    ShadowIcon,\n    Share2Icon,\n    SizeIcon,\n    SketchLogoIcon,\n    SpaceBetweenHorizontallyIcon,\n    SpaceBetweenVerticallyIcon,\n    SquareIcon,\n    SunIcon,\n    TextAlignCenterIcon,\n    TextAlignLeftIcon,\n    TextAlignRightIcon,\n    TextIcon,\n    TokensIcon,\n    TrashIcon,\n    UploadIcon,\n    VideoIcon,\n    ViewGridIcon,\n    ViewHorizontalIcon,\n    ViewVerticalIcon,\n} from '@radix-ui/react-icons';\nimport { MailXIcon } from 'lucide-react';\nimport { cn } from '../../utils';\nimport H1Icon from './header-level-icons/h1Icon';\nimport H2Icon from './header-level-icons/h2Icon';\nimport H3Icon from './header-level-icons/h3Icon';\nimport H4Icon from './header-level-icons/h4Icon';\nimport H5Icon from './header-level-icons/h5Icon';\nimport H6Icon from './header-level-icons/h6Icon';\n\nexport interface IconProps {\n    className?: string;\n    [key: string]: any;\n}\n\nexport const Icons = {\n    OnlookLogo: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            viewBox=\"0 0 22 22\"\n            fill=\"none\"\n            className={className}\n            {...props}\n        >\n            <g clipPath=\"url(#clip0_2707_69355)\">\n                <mask\n                    id=\"mask0_2707_69355\"\n                    style={{ maskType: 'alpha' }}\n                    maskUnits=\"userSpaceOnUse\"\n                    x=\"0\"\n                    y=\"0\"\n                    width=\"23\"\n                    height=\"22\"\n                >\n                    <circle\n                        cx=\"11.0078\"\n                        cy=\"11\"\n                        r=\"11\"\n                        fill=\"black\"\n                        style={{ fill: 'black', fillOpacity: 1 }}\n                    />\n                </mask>\n                <g mask=\"url(#mask0_2707_69355)\">\n                    <path\n                        fillRule=\"evenodd\"\n                        clipRule=\"evenodd\"\n                        d=\"M16.737 20.3969C19.9024 18.4654 22.0156 14.9795 22.0156 11C22.0156 4.92487 17.0908 0 11.0156 0C4.94049 0 0.015625 4.92487 0.015625 11C0.015625 13.6014 0.918657 15.9919 2.42835 17.8751L6.43945 14.6732V10.2135V8.20775L3.9857 5.75391H6.43945H15.6693C16.5441 5.75391 17.2533 6.46309 17.2533 7.33791V10.1708C16.4269 9.5987 15.8319 9.13852 15.8319 9.13852L13.0395 10.8308L17.8203 14.8924L16.737 20.3969ZM11.3203 6.98584H14.6616C14.6616 6.98584 14.7871 8.37687 12.9594 8.37687C11.3203 6.98574 11.3203 6.98584 11.3203 6.98584Z\"\n                        fill=\"#F7F7F7\"\n                        style={{ fill: '#F7F7F7', fillOpacity: 1 }}\n                    />\n                </g>\n                <path\n                    fillRule=\"evenodd\"\n                    clipRule=\"evenodd\"\n                    d=\"M2.42188 17.8751L6.43297 14.6732V10.2135V8.20775L3.97922 5.75391H6.43297H15.6628C16.5376 5.75391 17.2468 6.46309 17.2468 7.33791V10.1708C16.4204 9.5987 15.8254 9.13852 15.8254 9.13852L13.0331 10.8308L17.8138 14.8924L16.7305 20.3969C15.0635 21.414 13.1048 22 11.0091 22C7.53543 22 4.43779 20.3898 2.42188 17.8751ZM11.3138 6.98584H14.6552C14.6552 6.98584 14.7806 8.37687 12.9529 8.37687C11.3138 6.98574 11.3138 6.98584 11.3138 6.98584Z\"\n                    fill=\"#202123\"\n                    style={{ fill: '#202123', fillOpacity: 1 }}\n                />\n                <mask id=\"path-4-inside-1_2707_69355\" fill=\"white\">\n                    <path d=\"M22.0078 11C22.0078 17.0751 17.0829 22 11.0078 22C4.93268 22 0.0078125 17.0751 0.0078125 11C0.0078125 4.92487 4.93268 0 11.0078 0C17.0829 0 22.0078 4.92487 22.0078 11Z\" />\n                </mask>\n                <path\n                    d=\"M21.1484 11C21.1484 16.6005 16.6083 21.1406 11.0078 21.1406V22.8594C17.5576 22.8594 22.8672 17.5498 22.8672 11H21.1484ZM11.0078 21.1406C5.4073 21.1406 0.867188 16.6005 0.867188 11H-0.851562C-0.851562 17.5498 4.45806 22.8594 11.0078 22.8594V21.1406ZM0.867188 11C0.867188 5.39949 5.4073 0.859375 11.0078 0.859375V-0.859375C4.45806 -0.859375 -0.851562 4.45025 -0.851562 11H0.867188ZM11.0078 0.859375C16.6083 0.859375 21.1484 5.39949 21.1484 11H22.8672C22.8672 4.45025 17.5576 -0.859375 11.0078 -0.859375V0.859375Z\"\n                    fill=\"#F7F7F7\"\n                    style={{ fill: '#F7F7F7', fillOpacity: 1 }}\n                    mask=\"url(#path-4-inside-1_2707_69355)\"\n                />\n            </g>\n            <defs>\n                <clipPath id=\"clip0_2707_69355\">\n                    <rect\n                        width=\"22\"\n                        height=\"22\"\n                        fill=\"white\"\n                        style={{ fill: 'white', fillOpacity: 1 }}\n                    />\n                </clipPath>\n            </defs>\n        </svg>\n    ),\n    OnlookIcon: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width={22}\n            height={22}\n            fill=\"none\"\n            className={className}\n            {...props}\n        >\n            <g clipPath=\"url(#clip0_2707_69355)\">\n                <mask\n                    id=\"mask0_2707_69355\"\n                    style={{ maskType: 'alpha' }}\n                    maskUnits=\"userSpaceOnUse\"\n                    x=\"0\"\n                    y=\"0\"\n                    width=\"23\"\n                    height=\"22\"\n                >\n                    <circle\n                        cx=\"11.0078\"\n                        cy=\"11\"\n                        r=\"11\"\n                        fill=\"black\"\n                        style={{ fill: 'black', fillOpacity: 1 }}\n                    />\n                </mask>\n                <g mask=\"url(#mask0_2707_69355)\">\n                    <path\n                        fillRule=\"evenodd\"\n                        clipRule=\"evenodd\"\n                        d=\"M16.737 20.3969C19.9024 18.4654 22.0156 14.9795 22.0156 11C22.0156 4.92487 17.0908 0 11.0156 0C4.94049 0 0.015625 4.92487 0.015625 11C0.015625 13.6014 0.918657 15.9919 2.42835 17.8751L6.43945 14.6732V10.2135V8.20775L3.9857 5.75391H6.43945H15.6693C16.5441 5.75391 17.2533 6.46309 17.2533 7.33791V10.1708C16.4269 9.5987 15.8319 9.13852 15.8319 9.13852L13.0395 10.8308L17.8203 14.8924L16.737 20.3969ZM11.3203 6.98584H14.6616C14.6616 6.98584 14.7871 8.37687 12.9594 8.37687C11.3203 6.98574 11.3203 6.98584 11.3203 6.98584Z\"\n                        className={className}\n                        style={{ fillOpacity: 1 }}\n                    />\n                </g>\n                <mask id=\"path-4-inside-1_2707_69355\" fill=\"white\">\n                    <path d=\"M22.0078 11C22.0078 17.0751 17.0829 22 11.0078 22C4.93268 22 0.0078125 17.0751 0.0078125 11C0.0078125 4.92487 4.93268 0 11.0078 0C17.0829 0 22.0078 4.92487 22.0078 11Z\" />\n                </mask>\n            </g>\n            <defs>\n                <clipPath id=\"clip0_2707_69355\">\n                    <rect\n                        width=\"22\"\n                        height=\"22\"\n                        fill=\"white\"\n                        style={{ fill: 'white', fillOpacity: 1 }}\n                    />\n                </clipPath>\n            </defs>\n        </svg>\n    ),\n    OnlookTextLogo: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"139\"\n            height=\"17\"\n            viewBox=\"0 0 139 17\"\n            fill=\"none\"\n            className={cn('w-auto h-auto preserve-aspect-ratio dark:invert', className)}\n            {...props}\n        >\n            <path\n                d=\"M26.7578 16.502V4.40195H28.7485L43.3051 15.4019H44.7981V3.30195\"\n                stroke=\"black\"\n                style={{ stroke: 'black', strokeOpacity: 1 }}\n                strokeWidth=\"2.73715\"\n            />\n            <path\n                d=\"M50.7734 3.30237V15.4023L67.0719 15.4023\"\n                stroke=\"black\"\n                style={{ stroke: 'black', strokeOpacity: 1 }}\n                strokeWidth=\"2.73715\"\n            />\n            <rect\n                x=\"2\"\n                y=\"4.62305\"\n                width=\"19.4089\"\n                height=\"10.56\"\n                rx=\"5.27999\"\n                stroke=\"black\"\n                style={{ stroke: 'black', strokeOpacity: 1 }}\n                strokeWidth=\"2.73715\"\n            />\n            <rect\n                x=\"69.6797\"\n                y=\"4.62305\"\n                width=\"19.4089\"\n                height=\"10.56\"\n                rx=\"5.27999\"\n                stroke=\"black\"\n                style={{ stroke: 'black', strokeOpacity: 1 }}\n                strokeWidth=\"2.73715\"\n            />\n            <rect\n                x=\"94.0703\"\n                y=\"4.62305\"\n                width=\"19.4089\"\n                height=\"10.56\"\n                rx=\"5.27999\"\n                stroke=\"black\"\n                style={{ stroke: 'black', strokeOpacity: 1 }}\n                strokeWidth=\"2.73715\"\n            />\n            <path\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M120.823 10.3906V16.502H118.086V9.022V3.30204H120.823V7.65343H128.075L133.781 3.30213H138.295L130.657 9.126L138.583 16.502H134.565L127.999 10.3906H120.823ZM137.735 0.442137L137.66 0.34375L137.531 0.442137H137.735Z\"\n                fill=\"black\"\n                style={{ fill: 'black', fillOpacity: 1 }}\n            />\n        </svg>\n    ),\n    GoogleLogo: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                fill=\"#4285F4\"\n            />\n            <path\n                d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-2.86 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                fill=\"#34A853\"\n            />\n            <path\n                d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                fill=\"#FBBC05\"\n            />\n            <path\n                d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                fill=\"#EA4335\"\n            />\n            <path d=\"M1 1h22v22H1z\" fill=\"none\" />\n        </svg>\n    ),\n    ZedLogo: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"16\"\n            height=\"16\"\n            viewBox=\"0 0 16 16\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <defs>\n                <linearGradient\n                    gradientUnits=\"userSpaceOnUse\"\n                    x1=\"7.977\"\n                    y1=\"7.927\"\n                    x2=\"7.977\"\n                    y2=\"9.45\"\n                    id=\"gradient-0\"\n                    spreadMethod=\"pad\"\n                    gradientTransform=\"matrix(1.251512, 0.003243, -0.002837, 1.096434, -1.931147, 0.733313)\"\n                >\n                    <stop offset=\"0\" style={{ stopColor: 'rgb(42, 43, 43)' }}></stop>\n                    <stop offset=\"0.611\" style={{ stopColor: 'rgb(154, 155, 154)' }}></stop>\n                    <stop offset=\"1\" style={{ stopColor: 'rgb(154, 155, 154)' }}></stop>\n                </linearGradient>\n                <linearGradient\n                    gradientUnits=\"userSpaceOnUse\"\n                    x1=\"6.765\"\n                    y1=\"5\"\n                    x2=\"6.765\"\n                    y2=\"10.438\"\n                    id=\"gradient-1\"\n                    gradientTransform=\"matrix(1.250362, 0, 0, 1.250362, -0.625671, 0.869228)\"\n                >\n                    <stop offset=\"0\" style={{ stopColor: 'rgb(47, 46, 47)' }}></stop>\n                    <stop offset=\"0.848\" style={{ stopColor: 'rgb(154, 155, 154)' }}></stop>\n                    <stop offset=\"1\" style={{ stopColor: 'rgb(154, 155, 154)' }}></stop>\n                </linearGradient>\n                <linearGradient\n                    gradientUnits=\"userSpaceOnUse\"\n                    x1=\"6.914\"\n                    y1=\"3.106\"\n                    x2=\"6.914\"\n                    y2=\"12.481\"\n                    id=\"gradient-2\"\n                    gradientTransform=\"matrix(1.249201, 0, 0, 1.24825, -0.615701, 0.901788)\"\n                >\n                    <stop offset=\"0\" style={{ stopColor: 'rgb(46, 47, 47)' }}></stop>\n                    <stop offset=\"0.914\" style={{ stopColor: 'rgb(154, 155, 154)' }}></stop>\n                    <stop offset=\"1\" style={{ stopColor: 'rgb(154, 155, 154)' }}></stop>\n                </linearGradient>\n            </defs>\n            <path\n                d=\"M 1.425 2.419 C 1.167 2.419 0.957 2.629 0.957 2.888 L 0.957 13.2 L 0.019 13.2 L 0.019 2.888 C 0.019 2.111 0.649 1.481 1.425 1.481 L 13.985 1.481 C 14.611 1.481 14.925 2.239 14.482 2.682 L 6.747 10.417 L 5.809 11.354 L 4.198 12.966 L 3.26 13.903 L 1.62 15.544 L 1.054 16.481 C 0.427 16.481 0.114 15.724 0.557 15.281 L 8.263 7.575 L 6.113 7.575 L 6.113 8.513 L 5.175 8.513 L 5.175 7.341 C 5.175 6.952 5.49 6.638 5.879 6.638 L 9.2 6.638 L 10.841 4.997 L 3.535 4.997 L 3.535 10.856 L 2.597 10.856 L 2.597 4.997 C 2.597 4.479 3.017 4.06 3.535 4.06 L 11.778 4.06 L 13.419 2.419 L 1.425 2.419 Z\"\n                style={{ fillOpacity: 1, fill: 'rgb(155, 154, 154)' }}\n            ></path>\n            <path\n                d=\"M 5.78 11.381 L 6.719 10.442 L 8.9 10.442 L 8.9 9.475 L 9.839 9.475 L 9.839 10.678 C 9.839 11.067 9.524 11.381 9.136 11.381 L 5.78 11.381 Z\"\n                style={{ fill: 'url(#gradient-0)', fillOpacity: 1 }}\n            ></path>\n            <path\n                d=\"M 3.242 13.92 L 4.18 12.983 L 11.488 12.983 L 11.488 7.121 L 12.426 7.121 L 12.426 12.983 C 12.426 13.5 12.006 13.92 11.488 13.92 L 3.242 13.92 Z\"\n                style={{ fillOpacity: 1, fill: 'url(#gradient-1)' }}\n            ></path>\n            <path\n                d=\"M 1.043 16.481 C 1.611 15.56 1.611 15.548 1.611 15.548 L 13.594 15.548 C 13.853 15.548 14.062 15.336 14.062 15.079 L 14.062 4.78 L 15 4.78 L 15 15.079 C 15 15.854 14.372 16.481 13.594 16.481 L 1.043 16.481 Z\"\n                style={{ fillOpacity: 1, fill: 'url(#gradient-2)' }}\n            ></path>\n        </svg>\n    ),\n    VSCodeLogo: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"16\"\n            height=\"16\"\n            viewBox=\"0 0 16 16\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <g clipPath=\"url(#clip0_3223_92782)\">\n                <mask\n                    id=\"mask0_3223_92782\"\n                    style={{ maskType: 'alpha' }}\n                    maskUnits=\"userSpaceOnUse\"\n                    x=\"0\"\n                    y=\"0\"\n                    width=\"15\"\n                    height=\"16\"\n                >\n                    <path\n                        fillRule=\"evenodd\"\n                        clipRule=\"evenodd\"\n                        d=\"M10.6368 15.397C10.873 15.489 11.1307 15.4831 11.3692 15.3684L14.4574 13.8824C14.7819 13.7262 14.9883 13.3978 14.9883 13.0375V2.96141C14.9883 2.60109 14.7819 2.27267 14.4574 2.11652L11.3692 0.630468C11.0562 0.479886 10.69 0.516769 10.4153 0.716446C10.3761 0.744972 10.3387 0.77682 10.3036 0.811917L4.39154 6.20562L1.81636 4.25085C1.57664 4.06887 1.24133 4.0838 1.01868 4.28631L0.192736 5.03763C-0.0796016 5.28537 -0.0799137 5.71382 0.192061 5.96195L2.42534 7.99941L0.192061 10.0369C-0.0799137 10.285 -0.0796016 10.7135 0.192736 10.9612L1.01868 11.7125C1.24133 11.915 1.57664 11.93 1.81636 11.748L4.39154 9.7932L10.3036 15.1869C10.3972 15.2805 10.507 15.351 10.6251 15.397ZM11.2406 4.59425L6.75464 7.99941L11.2406 11.4046V4.59425Z\"\n                        fill=\"white\"\n                        style={{ fill: 'white', fillOpacity: 1 }}\n                    />\n                </mask>\n                <g mask=\"url(#mask0_3223_92782)\">\n                    <path\n                        d=\"M14.4666 2.11871L11.3759 0.630613C11.0182 0.458365 10.5907 0.531023 10.3099 0.811781L0.192185 10.0368C-0.0799555 10.2849 -0.0796426 10.7133 0.19286 10.9611L1.01931 11.7124C1.24209 11.9149 1.5776 11.9298 1.81747 11.7479L14.0015 2.50477C14.4103 2.19467 14.9974 2.48621 14.9974 2.99929V2.96341C14.9974 2.60326 14.7911 2.27495 14.4666 2.11871Z\"\n                        fill=\"#787878\"\n                        style={{ fill: 'color(display-p3 0.4706 0.4706 0.4706)', fillOpacity: 1 }}\n                    />\n                    <g filter=\"url(#filter0_d_3223_92782)\">\n                        <path\n                            d=\"M14.4666 13.8802L11.3759 15.3683C11.0182 15.5406 10.5907 15.4679 10.3099 15.1872L0.192185 5.9622C-0.0799555 5.71407 -0.0796426 5.28561 0.19286 5.03789L1.01931 4.28657C1.24209 4.08404 1.5776 4.06913 1.81747 4.25109L14.0015 13.4942C14.4103 13.8043 14.9974 13.5127 14.9974 12.9997V13.0356C14.9974 13.3957 14.7911 13.724 14.4666 13.8802Z\"\n                            fill=\"#929292\"\n                            style={{\n                                fill: 'color(display-p3 0.5725 0.5725 0.5725)',\n                                fillOpacity: 1,\n                            }}\n                        />\n                    </g>\n                    <g filter=\"url(#filter1_d_3223_92782)\">\n                        <path\n                            d=\"M11.3787 15.3684C11.0208 15.5405 10.5933 15.4677 10.3125 15.1869C10.6585 15.5329 11.25 15.2878 11.25 14.7986V1.20024C11.25 0.710982 10.6585 0.465957 10.3125 0.811917C10.5933 0.531134 11.0208 0.458373 11.3787 0.630468L14.4688 2.11653C14.7935 2.27268 15 2.6011 15 2.9614V13.0375C15 13.3978 14.7935 13.7262 14.4688 13.8824L11.3787 15.3684Z\"\n                            fill=\"#C7C7C7\"\n                            style={{\n                                fill: 'color(display-p3 0.7804 0.7804 0.7804)',\n                                fillOpacity: 1,\n                            }}\n                        />\n                    </g>\n                    <g style={{ mixBlendMode: 'overlay' }} opacity=\"0.25\">\n                        <path\n                            fillRule=\"evenodd\"\n                            clipRule=\"evenodd\"\n                            d=\"M10.6251 15.397C10.8613 15.489 11.1307 15.4831 11.3692 15.3684L14.4574 13.8824C14.7819 13.7262 14.9883 13.3978 14.9883 13.0375V2.96141C14.9883 2.60109 14.7819 2.27267 14.4574 2.11652L11.3692 0.630468C11.0562 0.479886 10.69 0.516769 10.4153 0.716446C10.3761 0.744972 10.3387 0.77682 10.3036 0.811917L4.39154 6.20562L1.81636 4.25085C1.57664 4.06887 1.24133 4.0838 1.01868 4.28631L0.192736 5.03763C-0.0796016 5.28537 -0.0799137 5.71382 0.192061 5.96195L2.42534 7.99941L0.192061 10.0369C-0.0799137 10.285 -0.0796016 10.7135 0.192736 10.9612L1.01868 11.7125C1.24133 11.915 1.57664 11.93 1.81636 11.748L4.39154 9.7932L10.3036 15.1869C10.3972 15.2805 10.507 15.351 10.6251 15.397ZM11.2406 4.59425L6.75464 7.99941L11.2406 11.4046V4.59425Z\"\n                            fill=\"url(#paint0_linear_3223_92782)\"\n                        />\n                    </g>\n                </g>\n            </g>\n            <defs>\n                <filter\n                    id=\"filter0_d_3223_92782\"\n                    x=\"-1.26172\"\n                    y=\"2.87402\"\n                    width=\"17.5078\"\n                    height=\"13.8369\"\n                    filterUnits=\"userSpaceOnUse\"\n                    colorInterpolationFilters=\"sRGB\"\n                >\n                    <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\n                    <feColorMatrix\n                        in=\"SourceAlpha\"\n                        type=\"matrix\"\n                        values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\n                        result=\"hardAlpha\"\n                    />\n                    <feOffset />\n                    <feGaussianBlur stdDeviation=\"0.625\" />\n                    <feColorMatrix\n                        type=\"matrix\"\n                        values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0\"\n                    />\n                    <feBlend\n                        mode=\"overlay\"\n                        in2=\"BackgroundImageFix\"\n                        result=\"effect1_dropShadow_3223_92782\"\n                    />\n                    <feBlend\n                        mode=\"normal\"\n                        in=\"SourceGraphic\"\n                        in2=\"effect1_dropShadow_3223_92782\"\n                        result=\"shape\"\n                    />\n                </filter>\n                <filter\n                    id=\"filter1_d_3223_92782\"\n                    x=\"9.0625\"\n                    y=\"-0.711915\"\n                    width=\"7.1875\"\n                    height=\"17.4229\"\n                    filterUnits=\"userSpaceOnUse\"\n                    colorInterpolationFilters=\"sRGB\"\n                >\n                    <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\n                    <feColorMatrix\n                        in=\"SourceAlpha\"\n                        type=\"matrix\"\n                        values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\n                        result=\"hardAlpha\"\n                    />\n                    <feOffset />\n                    <feGaussianBlur stdDeviation=\"0.625\" />\n                    <feColorMatrix\n                        type=\"matrix\"\n                        values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0\"\n                    />\n                    <feBlend\n                        mode=\"overlay\"\n                        in2=\"BackgroundImageFix\"\n                        result=\"effect1_dropShadow_3223_92782\"\n                    />\n                    <feBlend\n                        mode=\"normal\"\n                        in=\"SourceGraphic\"\n                        in2=\"effect1_dropShadow_3223_92782\"\n                        result=\"shape\"\n                    />\n                </filter>\n                <linearGradient\n                    id=\"paint0_linear_3223_92782\"\n                    x1=\"7.48828\"\n                    y1=\"0.538086\"\n                    x2=\"7.48828\"\n                    y2=\"15.4608\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"white\" style={{ stopColor: 'white', stopOpacity: 1 }} />\n                    <stop\n                        offset=\"1\"\n                        stopColor=\"white\"\n                        stopOpacity=\"0\"\n                        style={{ stopColor: 'none', stopOpacity: 0 }}\n                    />\n                </linearGradient>\n                <clipPath id=\"clip0_3223_92782\">\n                    <rect\n                        width=\"15\"\n                        height=\"15\"\n                        fill=\"white\"\n                        style={{ fill: 'white', fillOpacity: 1 }}\n                        transform=\"translate(0 0.5)\"\n                    />\n                </clipPath>\n            </defs>\n        </svg>\n    ),\n    CursorLogo: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"16\"\n            height=\"16\"\n            viewBox=\"0 0 16 16\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path d=\"M0 4.5375L7.56101 0.199219V4.5375H0Z\" fill=\"url(#paint0_linear_3223_92799)\" />\n            <path\n                d=\"M0 13.3378L7.49903 8.9375V13.3378V17.738L0 13.3378Z\"\n                fill=\"url(#paint1_linear_3223_92799)\"\n            />\n            <path\n                d=\"M15 13.3378L7.50097 8.9375V13.3378V17.738L15 13.3378Z\"\n                fill=\"url(#paint2_linear_3223_92799)\"\n            />\n            <path\n                d=\"M7.5 8.93737L14.999 4.53711V8.93737V13.3376L7.5 8.93737Z\"\n                fill=\"url(#paint3_linear_3223_92799)\"\n            />\n            <path\n                d=\"M7.5 8.93737L0.000967979 4.53711V8.93737V13.3376L7.5 8.93737Z\"\n                fill=\"url(#paint4_linear_3223_92799)\"\n            />\n            <path\n                d=\"M15 4.5375L7.56294 0.199219V4.5375H15Z\"\n                fill=\"url(#paint5_linear_3223_92799)\"\n            />\n            <path d=\"M0 4.53724L7.49903 8.9375L14.9981 4.53724H0Z\" fill=\"white\" />\n            <path d=\"M7.5 8.93829V17.8008L14.999 4.53803L7.5 8.93829Z\" fill=\"#E4E4E4\" />\n            <defs>\n                <linearGradient\n                    id=\"paint0_linear_3223_92799\"\n                    x1=\"5.82828\"\n                    y1=\"3.40578\"\n                    x2=\"7.81095\"\n                    y2=\"4.54116\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"#9B9B9B\" />\n                    <stop offset=\"1\" stopColor=\"#3C3C3C\" />\n                </linearGradient>\n                <linearGradient\n                    id=\"paint1_linear_3223_92799\"\n                    x1=\"7.49903\"\n                    y1=\"10.5857\"\n                    x2=\"7.49903\"\n                    y2=\"17.2094\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"#6F6F6F\" />\n                    <stop offset=\"0.362601\" stopColor=\"#878787\" />\n                    <stop offset=\"0.425081\" stopColor=\"#989898\" />\n                    <stop offset=\"1\" stopColor=\"#AEAEAE\" />\n                </linearGradient>\n                <linearGradient\n                    id=\"paint2_linear_3223_92799\"\n                    x1=\"7.50097\"\n                    y1=\"10.5857\"\n                    x2=\"7.50096\"\n                    y2=\"17.2094\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"#6F6F6F\" />\n                    <stop offset=\"0.362601\" stopColor=\"#B0B0B0\" />\n                    <stop offset=\"0.425081\" stopColor=\"#CCCCCC\" />\n                    <stop offset=\"1\" stopColor=\"#D9D9D9\" />\n                </linearGradient>\n                <linearGradient\n                    id=\"paint3_linear_3223_92799\"\n                    x1=\"14.999\"\n                    y1=\"6.18526\"\n                    x2=\"14.999\"\n                    y2=\"12.809\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"#3C3C3C\" />\n                    <stop offset=\"1\" stopColor=\"#232323\" />\n                    <stop offset=\"1\" stopColor=\"#AEAEAE\" />\n                </linearGradient>\n                <linearGradient\n                    id=\"paint4_linear_3223_92799\"\n                    x1=\"0.000968457\"\n                    y1=\"9.0934\"\n                    x2=\"6.8823\"\n                    y2=\"8.88955\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"#4D4D4D\" />\n                    <stop offset=\"1\" stopColor=\"#383838\" />\n                </linearGradient>\n                <linearGradient\n                    id=\"paint5_linear_3223_92799\"\n                    x1=\"9.26727\"\n                    y1=\"3.40578\"\n                    x2=\"7.30131\"\n                    y2=\"4.51313\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"#414141\" />\n                    <stop offset=\"1\" stopColor=\"#0A0A0A\" />\n                </linearGradient>\n            </defs>\n        </svg>\n    ),\n\n    WindsurfLogo: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width={15}\n            height={15}\n            viewBox=\"0 0 69 119\"\n            fill=\"none\"\n            className={className}\n            {...props}\n        >\n            <path\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M0.627393 4.31968C-0.174635 2.2935 1.2378 0.0970819 3.41639 0.0482939C13.1956 -0.170706 36.8123 0.062288 49.4998 6.70279C70.4998 17.694 68.4998 52.1957 68.4998 52.1957C68.4998 52.1957 65.4998 38.4218 49.4998 31.2074C35.4998 24.8948 8.49979 24.2078 8.49979 24.2078L0.627393 4.31968ZM10.592 36.0501C10.0299 34.3331 11.1999 32.5724 13.0064 32.5468C20.0859 32.4467 36.6633 33.2471 51.4998 41.6936C71.9998 53.3644 68.6156 85.2391 68.6156 85.2391C68.6156 85.2391 64.4002 70.0333 50.5503 63.1971C38.4317 57.2154 17.7073 57.782 17.7073 57.782L10.592 36.0501ZM21.9398 65.9814C20.4243 66.0447 19.4843 67.5501 19.9955 68.9782L28.0902 91.5882C28.0902 91.5882 42.8182 90.6668 52.9979 95.6914C64.6318 101.434 67.9979 118.691 67.9979 118.691C67.9979 118.691 70.5051 85.1901 54.476 74.0739C42.9474 66.0789 28.1443 65.722 21.9398 65.9814Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    Component: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"15\"\n            height=\"15\"\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M5.59712 3.15074L7.05696 1.69087C7.30109 1.44679 7.69677 1.44679 7.9409 1.69087L9.40071 3.15074C9.64484 3.39482 9.64484 3.79054 9.40071 4.03462L7.9409 5.49449C7.69677 5.73857 7.30109 5.73857 7.05696 5.49449L5.59712 4.03462C5.35304 3.79054 5.35304 3.39482 5.59712 3.15074Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinejoin=\"round\"\n            />\n            <path\n                d=\"M5.59712 10.9632L7.05696 9.50341C7.30109 9.25928 7.69677 9.25928 7.9409 9.50341L9.40071 10.9632C9.64484 11.2073 9.64484 11.603 9.40071 11.8472L7.9409 13.307C7.69677 13.5511 7.30109 13.5511 7.05696 13.307L5.59712 11.8472C5.35304 11.603 5.35304 11.2073 5.59712 10.9632Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinejoin=\"round\"\n            />\n            <path\n                d=\"M1.69087 7.05696L3.15074 5.59712C3.39482 5.35304 3.79054 5.35304 4.03462 5.59712L5.49449 7.05696C5.73857 7.30109 5.73857 7.69677 5.49449 7.9409L4.03462 9.40071C3.79054 9.64484 3.39482 9.64484 3.15074 9.40071L1.69087 7.9409C1.44679 7.69677 1.44679 7.30109 1.69087 7.05696Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinejoin=\"round\"\n            />\n            <path\n                d=\"M9.50341 7.05696L10.9632 5.59712C11.2073 5.35304 11.603 5.35304 11.8472 5.59712L13.307 7.05696C13.5511 7.30109 13.5511 7.69677 13.307 7.9409L11.8472 9.40071C11.603 9.64484 11.2073 9.64484 10.9632 9.40071L9.50341 7.9409C9.25928 7.69677 9.25928 7.30109 9.50341 7.05696Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    Directory: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width={15}\n            height={15}\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M2 11.5C2 11.7761 2.22386 12 2.5 12H12.5C12.7761 12 13 11.7761 13 11.5V5C13 4.72386 12.7761 4.5 12.5 4.5H9.5H7.83333C7.50878 4.5 7.19298 4.39473 6.93333 4.2L5.33333 3H2.5C2.22386 3 2 3.22386 2 3.5L2 6.5L2 11.5ZM2.5 13C1.67157 13 1 12.3284 1 11.5L1 6.5L1 3.5C1 2.67157 1.67157 2 2.5 2H5.41667C5.57894 2 5.73684 2.05263 5.86667 2.15L7.53333 3.4C7.61988 3.46491 7.72515 3.5 7.83333 3.5H9.5H12.5C13.3284 3.5 14 4.17157 14 5V11.5C14 12.3284 13.3284 13 12.5 13H2.5Z\"\n                fill=\"currentColor\"\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n            />\n        </svg>\n    ),\n    DirectoryOpen: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width={15}\n            height={15}\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M2.13713 11.844C2.22824 11.9401 2.35712 12 2.5 12H11.6916C11.9274 12 12.1311 11.8353 12.1805 11.6048L13.2519 6.60477C13.3186 6.29351 13.0813 6 12.763 6H12.5L3.80842 6C3.57265 6 3.36892 6.1647 3.31951 6.39524L3.1139 7.35476L2.7389 9.10476L2.3639 10.8548L2.1764 11.7298C2.16774 11.7702 2.15442 11.8084 2.13713 11.844ZM2 7.78036L2.1361 7.14524L2.34171 6.18571C2.48991 5.4941 3.10111 5 3.80842 5L12 5C12 4.72386 11.7761 4.5 11.5 4.5H9.5H7.83333C7.50878 4.5 7.19298 4.39473 6.93333 4.2L5.33333 3H2.5C2.22386 3 2 3.22386 2 3.5L2 6.5L2 7.78036ZM13 5.01844V5C13 4.17157 12.3284 3.5 11.5 3.5H9.5H7.83333C7.72515 3.5 7.61988 3.46491 7.53333 3.4L5.86667 2.15C5.73684 2.05263 5.57894 2 5.41667 2H2.5C1.67157 2 1 2.67157 1 3.5L1 6.5L1 11.5C1 12.3284 1.67157 13 2.5 13H11.6916C12.3989 13 13.0101 12.5059 13.1583 11.8143L14.2297 6.81429C14.4129 5.95961 13.832 5.14952 13 5.01844Z\"\n                fill=\"currentColor\"\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n            />\n        </svg>\n    ),\n    DirectManipulation: ({ className, ...props }: IconProps) => (\n        <svg\n            width={15}\n            height={15}\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            className={className}\n            {...props}\n            xmlns=\"http://www.w3.org/2000/svg\"\n        >\n            <path\n                d=\"M10.5 4V4.83333C10.5 5.20152 10.7985 5.5 11.1667 5.5H12M10.5 4V3.16667C10.5 2.79848 10.7985 2.5 11.1667 2.5H12.8333C13.2015 2.5 13.5 2.79848 13.5 3.16667V4.83333C13.5 5.20152 13.2015 5.5 12.8333 5.5H12M10.5 4H5.5M12 5.5V6.83333M4 5.5V10.5M5.33333 12V11.1667C5.33333 10.7985 5.03485 10.5 4.66667 10.5H3.16667C2.79848 10.5 2.5 10.7985 2.5 11.1667V12.8333C2.5 13.2015 2.79848 13.5 3.16667 13.5H4.66667C5.03485 13.5 5.33333 13.2015 5.33333 12.8333V12ZM5.33333 12H6.83333M3.16667 5.5H4.83333C5.20152 5.5 5.5 5.20152 5.5 4.83333V3.16667C5.5 2.79848 5.20152 2.5 4.83333 2.5H3.16667C2.79848 2.5 2.5 2.79848 2.5 3.16667V4.83333C2.5 5.20152 2.79848 5.5 3.16667 5.5ZM8.61087 8.17453L13.1652 9.4758C13.4697 9.5628 13.5144 9.97573 13.2356 10.1259L11.3075 11.1641C11.2467 11.1968 11.1968 11.2467 11.1641 11.3075L10.1259 13.2356C9.97573 13.5144 9.5628 13.4697 9.4758 13.1652L8.17453 8.61087C8.09847 8.3446 8.3446 8.09847 8.61087 8.17453Z\"\n                stroke=\"currentColor\"\n                fill=\"stroke:currentColor; stroke-opacity:1;\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n\n    EyeDropper: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width={15}\n            height={15}\n            fill=\"none\"\n            className={className}\n            {...props}\n        >\n            <path\n                fill=\"currentColor\"\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M9.65098 3.01801C10.3799 2.26432 11.585 2.25428 12.3263 2.99571C13.0677 3.73715 13.0577 4.94231 12.304 5.67128L10.1041 7.79928C10.0222 7.8785 9.97552 7.98729 9.97457 8.10124C9.97362 8.21519 10.0185 8.32474 10.099 8.40531L10.6259 8.93224C10.7694 9.0757 10.7877 9.26188 10.7148 9.39002C10.5901 9.60936 10.452 9.80888 10.3287 9.92046C10.3126 9.93507 10.2989 9.94616 10.2876 9.95455C9.49985 9.34257 8.75216 8.59581 7.7444 7.58796C6.74234 6.58582 6.09783 5.92672 5.3679 5.03152C5.37539 5.02186 5.38474 5.01062 5.39637 4.9978C5.50841 4.87426 5.70999 4.73496 5.93151 4.60884C6.06074 4.53526 6.24789 4.55385 6.39152 4.6975L6.91718 5.2232C6.99774 5.30377 7.10729 5.34862 7.22122 5.34767C7.33516 5.34672 7.44394 5.30005 7.52316 5.21815L9.65098 3.01801ZM12.9273 2.39465C11.8501 1.31734 10.0992 1.33193 9.04003 2.42704L7.21263 4.31655L6.99254 4.09643C6.61768 3.72155 6.019 3.5809 5.511 3.87012C5.27335 4.00543 4.97343 4.19889 4.76679 4.42673C4.66384 4.54024 4.55311 4.69549 4.51041 4.88697C4.46226 5.10293 4.51054 5.32376 4.6602 5.50846C5.12817 6.086 5.55941 6.56637 6.05118 7.07991L2.04643 11.0827C1.4409 11.6879 1.44076 12.6695 2.04611 13.2749C2.65134 13.8802 3.6326 13.8802 4.23782 13.2749L8.23887 9.27353C8.78734 9.80411 9.29355 10.2634 9.82262 10.6694C10.01 10.8132 10.2294 10.8558 10.4415 10.8069C10.6311 10.7631 10.7853 10.6537 10.8991 10.5507C11.1269 10.3445 11.3192 10.0466 11.4536 9.81026C11.7424 9.30248 11.6007 8.70501 11.2269 8.33118L11.0056 8.10983L12.895 6.28228C13.99 5.22307 14.0046 3.47196 12.9273 2.39465ZM2.64727 11.6839C2.37384 11.9572 2.37378 12.4005 2.64713 12.6738C2.92042 12.9472 3.36352 12.9472 3.63681 12.6738L7.63192 8.6784L6.64339 7.68979L2.64727 11.6839Z\"\n            />\n        </svg>\n    ),\n    Return: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width={15}\n            height={15}\n            fill=\"none\"\n            className={className}\n            {...props}\n        >\n            <path\n                fill=\"currentColor\"\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M5.3312 11.3579C5.52669 11.1628 5.52705 10.8462 5.33201 10.6508L4.08798 9.40391H8.38281H12.4828C13.5322 9.40391 14.3828 8.55325 14.3828 7.50391V5.50391C14.3828 4.39934 13.4874 3.50391 12.3828 3.50391H8.88281C8.60667 3.50391 8.38281 3.72776 8.38281 4.00391C8.38281 4.28005 8.60667 4.50391 8.88281 4.50391H12.3828C12.9351 4.50391 13.3828 4.95162 13.3828 5.50391V7.50391C13.3828 8.00096 12.9799 8.40391 12.4828 8.40391H8.38281H4.08798L5.33201 7.15706C5.52705 6.96157 5.52669 6.64499 5.3312 6.44995C5.13572 6.25491 4.81914 6.25527 4.6241 6.45075L2.52886 8.55075C2.33413 8.74592 2.33413 9.06189 2.52886 9.25706L4.6241 11.3571C4.81914 11.5525 5.13572 11.5529 5.3312 11.3579Z\"\n            />\n        </svg>\n    ),\n    Tablet: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width={15}\n            height={15}\n            fill=\"none\"\n            className={className}\n            {...props}\n        >\n            <path\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M2.89885 1.78968C2.89885 1.57167 3.07558 1.39494 3.29359 1.39494H11.7146C11.9326 1.39494 12.1094 1.57167 12.1094 1.78968V13.2107C12.1094 13.4287 11.9326 13.6055 11.7146 13.6055H3.29359C3.07558 13.6055 2.89885 13.4287 2.89885 13.2107V1.78968ZM3.29359 0.605469C2.63956 0.605469 2.10938 1.13566 2.10938 1.78968V13.2107C2.10938 13.8648 2.63956 14.3949 3.29359 14.3949H11.7146C12.3687 14.3949 12.8988 13.8648 12.8988 13.2107V1.78968C12.8988 1.13566 12.3687 0.605469 11.7146 0.605469H3.29359ZM6.00041 12.2241C5.84781 12.2241 5.7241 12.3478 5.7241 12.5004C5.7241 12.653 5.84781 12.7767 6.00041 12.7767H9.00041C9.15302 12.7767 9.27673 12.653 9.27673 12.5004C9.27673 12.3478 9.15302 12.2241 9.00041 12.2241H6.00041Z\"\n                fill=\"black\"\n                style={{ fill: 'black', fillOpacity: 1 }}\n            />\n        </svg>\n    ),\n    Terminal: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"15\"\n            height=\"15\"\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <rect\n                x=\"1.47\"\n                y=\"1.97\"\n                width=\"12.06\"\n                height=\"11.06\"\n                rx=\"1.03\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.94\"\n            />\n            <path\n                d=\"M4 9.5L6 7.5L4 5.5\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.84\"\n                strokeLinecap=\"round\"\n            />\n            <path\n                d=\"M7.5 9.5L10.9989 9.49303\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9\"\n                strokeLinecap=\"round\"\n            />\n        </svg>\n    ),\n    H1: ({ className, ...props }: IconProps) => (\n        <H1Icon\n            className={className}\n            letterClassName={cn('letter', {\n                'fill-foreground/50 dark:fill-foreground/50': !className?.includes(\n                    'fill-white dark:fill-primary',\n                ),\n            })}\n            levelClassName={cn('level', {\n                'fill-foreground dark:fill-foreground': !className?.includes(\n                    'fill-white dark:fill-primary',\n                ),\n            })}\n            {...props}\n        />\n    ),\n    H2: ({ className, ...props }: IconProps) => (\n        <H2Icon\n            className={className}\n            letterClassName={cn('letter', {\n                'fill-foreground/50 dark:fill-foreground/50': !className?.includes(\n                    'fill-white dark:fill-primary',\n                ),\n            })}\n            levelClassName={cn('level', {\n                'fill-foreground dark:fill-foreground': !className?.includes(\n                    'fill-white dark:fill-primary',\n                ),\n            })}\n            {...props}\n        />\n    ),\n    H3: ({ className, ...props }: IconProps) => (\n        <H3Icon\n            className={className}\n            letterClassName={cn('letter', {\n                'fill-foreground/50 dark:fill-foreground/50': !className?.includes(\n                    'fill-white dark:fill-primary',\n                ),\n            })}\n            levelClassName={cn('level', {\n                'fill-foreground dark:fill-foreground': !className?.includes(\n                    'fill-white dark:fill-primary',\n                ),\n            })}\n            {...props}\n        />\n    ),\n    H4: ({ className, ...props }: IconProps) => (\n        <H4Icon\n            className={className}\n            letterClassName={cn(\n                {\n                    'fill-foreground/50 dark:fill-foreground/50': !className?.includes(\n                        'fill-white dark:fill-primary',\n                    ),\n                },\n                className,\n            )}\n            levelClassName={cn(\n                {\n                    'stroke-[#313131] dark:stroke-[#CECECE] fill-none': !className?.includes(\n                        'fill-white dark:fill-primary',\n                    ),\n                },\n                {\n                    'stroke-white dark:stroke-primary fill-none': className?.includes(\n                        'fill-white dark:fill-primary',\n                    ),\n                },\n            )}\n            {...props}\n        />\n    ),\n    H5: ({ className, ...props }: IconProps) => (\n        <H5Icon\n            className={className}\n            letterClassName={cn(\n                {\n                    'fill-foreground/50 dark:fill-foreground/50': !className?.includes(\n                        'fill-white dark:fill-primary',\n                    ),\n                },\n                className,\n            )}\n            levelClassName={cn(\n                {\n                    'stroke-[#313131] dark:stroke-[#CECECE] fill-none': !className?.includes(\n                        'fill-white dark:fill-primary',\n                    ),\n                },\n                {\n                    'stroke-white dark:stroke-primary fill-none': className?.includes(\n                        'fill-white dark:fill-primary',\n                    ),\n                },\n            )}\n            {...props}\n        />\n    ),\n    H6: ({ className, ...props }: IconProps) => (\n        <H6Icon\n            className={className}\n            letterClassName={cn(\n                {\n                    'fill-foreground/50 dark:fill-foreground/50': !className?.includes(\n                        'fill-white dark:fill-primary',\n                    ),\n                },\n                className,\n            )}\n            levelClassName={cn(\n                {\n                    'fill-[#313131] dark:fill-[#CECECE]': !className?.includes(\n                        'fill-white dark:fill-primary',\n                    ),\n                },\n                className,\n            )}\n            {...props}\n        />\n    ),\n    Landscape: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            className={className}\n            {...props}\n        >\n            <rect width=\"20\" height=\"12\" x=\"2\" y=\"6\" rx=\"2\" />\n        </svg>\n    ),\n    Layers: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"16\"\n            height=\"16\"\n            viewBox=\"0 0 16 16\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M8.05648 3.36597C8.02029 3.35197 7.9801 3.35197 7.94385 3.36597L2.80306 5.35219C2.66981 5.40368 2.66981 5.59221 2.80306 5.64369L7.94385 7.62993C7.9801 7.64393 8.02029 7.64393 8.05648 7.62993L13.1973 5.64369C13.3305 5.59221 13.3305 5.40368 13.1973 5.35219L8.05648 3.36597ZM7.60597 2.49147C7.85966 2.39347 8.14072 2.39347 8.39435 2.49147L13.5352 4.4777C14.4679 4.83807 14.4679 6.15781 13.5352 6.51819L8.39435 8.50443C8.14072 8.60243 7.85966 8.60243 7.60597 8.50443L2.46519 6.51819C1.53244 6.15781 1.53244 4.83808 2.46519 4.4777L7.60597 2.49147Z\"\n                fill=\"currentColor\"\n            />\n            <path\n                d=\"M13.5352 9.02203L8.39435 11.0083C8.14072 11.1063 7.85966 11.1063 7.60597 11.0083L2.46519 9.02203C1.90934 8.80728 1.68473 8.25184 1.79137 7.76172L7.91747 10.1287C7.94091 10.1377 7.96541 10.1428 7.9901 10.144C7.99572 10.1443 8.00141 10.1443 8.00704 10.1441C8.03329 10.1434 8.05947 10.1383 8.08441 10.1287L14.2091 7.76228C14.3155 8.25222 14.0909 8.80734 13.5352 9.02203Z\"\n                fill=\"currentColor\"\n            />\n            <path\n                d=\"M13.5352 11.522L8.39436 13.5082C8.14073 13.6062 7.85967 13.6062 7.60598 13.5082L2.46519 11.522C1.90934 11.3072 1.68473 10.7518 1.79137 10.2617L7.91748 12.6286C7.94092 12.6377 7.96542 12.6428 7.99011 12.6439C7.99573 12.6443 8.00142 12.6443 8.00704 12.6441C8.03329 12.6434 8.05948 12.6382 8.08442 12.6286L14.2091 10.2622C14.3155 10.7522 14.0909 11.3073 13.5352 11.522Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    Opacity: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"18\"\n            height=\"18\"\n            viewBox=\"0 0 18 18\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M14.625 5.2125V12.7875H15.75V5.2125H14.625ZM12.7875 14.625H5.2125V15.75H12.7875V14.625ZM3.375 12.7875V5.2125H2.25V12.7875H3.375ZM5.2125 3.375H12.7875V2.25H5.2125V3.375ZM5.2125 14.625C4.78318 14.625 4.49502 14.6246 4.27311 14.6064C4.05776 14.5889 3.9548 14.5574 3.88688 14.5229L3.37615 15.5252C3.62909 15.6541 3.89659 15.7044 4.1815 15.7277C4.45985 15.7505 4.80174 15.75 5.2125 15.75V14.625ZM2.25 12.7875C2.25 13.1983 2.24957 13.5401 2.27231 13.8185C2.29559 14.1035 2.34592 14.3709 2.4748 14.6239L3.47718 14.1131C3.44258 14.0452 3.41117 13.9422 3.39357 13.7269C3.37544 13.505 3.375 13.2168 3.375 12.7875H2.25ZM3.88688 14.5229C3.71048 14.4329 3.56706 14.2895 3.47718 14.1131L2.4748 14.6239C2.67254 15.0119 2.98806 15.3275 3.37615 15.5252L3.88688 14.5229ZM14.625 12.7875C14.625 13.2168 14.6246 13.505 14.6064 13.7269C14.5889 13.9422 14.5574 14.0452 14.5229 14.1131L15.5252 14.6239C15.6541 14.3709 15.7044 14.1035 15.7277 13.8185C15.7505 13.5401 15.75 13.1983 15.75 12.7875H14.625ZM12.7875 15.75C13.1983 15.75 13.5401 15.7505 13.8185 15.7277C14.1035 15.7044 14.3709 15.6541 14.6239 15.5252L14.1131 14.5229C14.0452 14.5574 13.9422 14.5889 13.7269 14.6064C13.505 14.6246 13.2168 14.625 12.7875 14.625V15.75ZM14.5229 14.1131C14.4329 14.2895 14.2895 14.4329 14.1131 14.5229L14.6239 15.5252C15.0119 15.3275 15.3275 15.0119 15.5252 14.6239L14.5229 14.1131ZM15.75 5.2125C15.75 4.80174 15.7505 4.45985 15.7277 4.1815C15.7044 3.89659 15.6541 3.62909 15.5252 3.37615L14.5229 3.88688C14.5574 3.9548 14.5889 4.05776 14.6064 4.27311C14.6246 4.49502 14.625 4.78318 14.625 5.2125H15.75ZM12.7875 3.375C13.2168 3.375 13.505 3.37544 13.7269 3.39357C13.9422 3.41117 14.0452 3.44258 14.1131 3.47718L14.6239 2.4748C14.3709 2.34592 14.1035 2.29559 13.8185 2.27231C13.5401 2.24957 13.1983 2.25 12.7875 2.25V3.375ZM15.5252 3.37615C15.3275 2.98806 15.0119 2.67254 14.6239 2.4748L14.1131 3.47718C14.2895 3.56706 14.4329 3.71048 14.5229 3.88688L15.5252 3.37615ZM3.375 5.2125C3.375 4.78318 3.37544 4.49502 3.39357 4.27311C3.41117 4.05776 3.44258 3.9548 3.47718 3.88688L2.4748 3.37615C2.34592 3.62909 2.29559 3.89659 2.27231 4.1815C2.24957 4.45985 2.25 4.80174 2.25 5.2125H3.375ZM5.2125 2.25C4.80174 2.25 4.45985 2.24957 4.1815 2.27231C3.89659 2.29559 3.62909 2.34592 3.37615 2.4748L3.88688 3.47718C3.9548 3.44258 4.05776 3.41117 4.27311 3.39357C4.49502 3.37544 4.78318 3.375 5.2125 3.375V2.25ZM3.47718 3.88688C3.56706 3.71048 3.71048 3.56706 3.88688 3.47718L3.37615 2.4748C2.98806 2.67254 2.67254 2.98806 2.4748 3.37615L3.47718 3.88688Z\"\n                fill=\"currentColor\"\n            />\n            <path\n                d=\"M6.6748 12C7.04753 12 7.3495 12.3021 7.34961 12.6748C7.34961 13.0476 7.0476 13.3496 6.6748 13.3496C6.3021 13.3495 6 13.0475 6 12.6748C6.00011 12.3022 6.30217 12.0001 6.6748 12ZM8.6748 12C9.04753 12 9.3495 12.3021 9.34961 12.6748C9.34961 13.0476 9.0476 13.3496 8.6748 13.3496C8.3021 13.3495 8 13.0475 8 12.6748C8.00011 12.3022 8.30217 12.0001 8.6748 12ZM10.6748 12C11.0475 12 11.3495 12.3021 11.3496 12.6748C11.3496 13.0476 11.0476 13.3496 10.6748 13.3496C10.3021 13.3495 10 13.0475 10 12.6748C10.0001 12.3022 10.3022 12.0001 10.6748 12ZM12.6748 12C13.0475 12 13.3495 12.3021 13.3496 12.6748C13.3496 13.0476 13.0476 13.3496 12.6748 13.3496C12.3021 13.3495 12 13.0475 12 12.6748C12.0001 12.3022 12.3022 12.0001 12.6748 12ZM8.6748 10C9.04753 10 9.3495 10.3021 9.34961 10.6748C9.34961 11.0476 9.0476 11.3496 8.6748 11.3496C8.3021 11.3495 8 11.0475 8 10.6748C8.00011 10.3022 8.30217 10.0001 8.6748 10ZM10.6748 10C11.0475 10 11.3495 10.3021 11.3496 10.6748C11.3496 11.0476 11.0476 11.3496 10.6748 11.3496C10.3021 11.3495 10 11.0475 10 10.6748C10.0001 10.3022 10.3022 10.0001 10.6748 10ZM12.6748 10C13.0475 10 13.3495 10.3021 13.3496 10.6748C13.3496 11.0476 13.0476 11.3496 12.6748 11.3496C12.3021 11.3495 12 11.0475 12 10.6748C12.0001 10.3022 12.3022 10.0001 12.6748 10ZM10.6748 8C11.0475 8 11.3495 8.3021 11.3496 8.6748C11.3496 9.0476 11.0476 9.34961 10.6748 9.34961C10.3021 9.3495 10 9.04753 10 8.6748C10.0001 8.30217 10.3022 8.00011 10.6748 8ZM12.6748 8C13.0475 8 13.3495 8.3021 13.3496 8.6748C13.3496 9.0476 13.0476 9.34961 12.6748 9.34961C12.3021 9.3495 12 9.04753 12 8.6748C12.0001 8.30217 12.3022 8.00011 12.6748 8ZM12.6748 6C13.0475 6 13.3495 6.3021 13.3496 6.6748C13.3496 7.0476 13.0476 7.34961 12.6748 7.34961C12.3021 7.3495 12 7.04753 12 6.6748C12.0001 6.30217 12.3022 6.00011 12.6748 6Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    Play: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M12.25 3.75C7.55558 3.75 3.75 7.55558 3.75 12.25C3.75 16.9444 7.55558 20.75 12.25 20.75C16.9444 20.75 20.75 16.9444 20.75 12.25C20.75 7.55558 16.9444 3.75 12.25 3.75ZM2.25 12.25C2.25 6.72715 6.72715 2.25 12.25 2.25C17.7728 2.25 22.25 6.72715 22.25 12.25C22.25 17.7728 17.7728 22.25 12.25 22.25C6.72715 22.25 2.25 17.7728 2.25 12.25Z\"\n                fill=\"currentColor\"\n            />\n            <path\n                d=\"M10.25 15.054V9.44617C10.25 9.04446 10.6998 8.80676 11.0317 9.03305L15.1441 11.837C15.4352 12.0355 15.4352 12.4647 15.1441 12.6632L11.0317 15.4671C10.6998 15.6934 10.25 15.4557 10.25 15.054Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    Portrait: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            className={className}\n            {...props}\n        >\n            <rect width=\"12\" height=\"20\" x=\"6\" y=\"2\" rx=\"2\" />\n        </svg>\n    ),\n    Sparkles: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"15\"\n            height=\"15\"\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M12.0312 8.125C8.87219 8.39844 7.14844 10.1222 6.875 13.2812C6.59 10.0731 4.87526 8.48256 1.71875 8.125C4.92515 7.75519 6.50519 6.17515 6.875 2.96875C7.23256 6.12526 8.82306 7.84 12.0312 8.125Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n            <path\n                d=\"M12.3686 0.786775C12.3582 0.694694 12.2804 0.625094 12.1877 0.625C12.095 0.624906 12.017 0.69435 12.0064 0.786406C11.9374 1.38501 11.7584 1.80594 11.4697 2.09466C11.1809 2.38339 10.76 2.56237 10.1614 2.63141C10.0694 2.64203 9.99988 2.72002 10 2.81269C10.0001 2.90536 10.0697 2.98321 10.1618 2.99363C10.7505 3.06032 11.1804 3.23922 11.4759 3.52946C11.7704 3.81861 11.9531 4.23944 12.0059 4.83384C12.0143 4.92797 12.0932 5.00011 12.1877 5C12.2822 4.99989 12.3609 4.92758 12.3691 4.83343C12.4198 4.24881 12.6023 3.81912 12.8982 3.52319C13.1941 3.22726 13.6238 3.04472 14.2084 2.99411C14.3026 2.98596 14.3749 2.90721 14.375 2.81271C14.3751 2.7182 14.303 2.63929 14.2088 2.63093C13.6144 2.57813 13.1936 2.39541 12.9044 2.10094C12.6142 1.80536 12.4353 1.37549 12.3686 0.786775Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    Stop: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path d=\"M9.5 9.5H14.5V14.5H9.5V9.5Z\" fill=\"currentColor\" />\n            <path\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M12 3.5C7.30558 3.5 3.5 7.30558 3.5 12C3.5 16.6944 7.30558 20.5 12 20.5C16.6944 20.5 20.5 16.6944 20.5 12C20.5 7.30558 16.6944 3.5 12 3.5ZM2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    Styles: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"15\"\n            height=\"15\"\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M4.21875 4.375V5.15625C4.21875 5.84661 4.77839 6.40625 5.46875 6.40625H11.4062C12.0966 6.40625 12.6562 5.84661 12.6562 5.15625V3.59375C12.6562 2.90339 12.0966 2.34375 11.4062 2.34375H5.46875C4.77839 2.34375 4.21875 2.90339 4.21875 3.59375V4.375ZM4.21875 4.375H2.96875C2.62357 4.375 2.34375 4.65482 2.34375 5V7.03125C2.34375 7.72162 2.90339 8.28125 3.59375 8.28125H7.5C7.84519 8.28125 8.125 8.56106 8.125 8.90625V9.6875M9.53125 13.2812V11.4062C9.53125 10.6296 8.90162 10 8.125 10C7.34838 10 6.71875 10.6296 6.71875 11.4062V13.2812\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    Lightbulb: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"15\"\n            height=\"15\"\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M5.46661 11.0938V10.6878C5.46661 10.4515 5.33135 10.2385 5.13002 10.1149C4.99455 10.0316 4.8638 9.9415 4.73826 9.84494C3.66103 9.0165 2.9668 7.71456 2.9668 6.25056C2.9668 3.74771 4.99575 1.71875 7.49858 1.71875C10.0015 1.71875 12.0304 3.74771 12.0304 6.25056C12.0304 7.71456 11.3361 9.0165 10.259 9.84494C10.1334 9.9415 10.0026 10.0316 9.8672 10.1149C9.66583 10.2385 9.53058 10.4515 9.53058 10.6878V11.0938M5.46661 11.0938V11.8743C5.46661 12.9965 6.37639 13.9062 7.49858 13.9062C8.62083 13.9062 9.53058 12.9965 9.53058 11.8743V11.0938M5.46661 11.0938H9.53058\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinecap=\"square\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    LightbulbSlash: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"15\"\n            height=\"15\"\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M5.46661 11.0938V10.6878C5.46661 10.4515 5.33135 10.2385 5.13002 10.1149C4.99455 10.0316 4.8638 9.9415 4.73826 9.84494C3.66103 9.0165 2.9668 7.71456 2.9668 6.25056C2.9668 5.43145 3.18411 4.66309 3.56423 4M5.46661 11.0938V11.8743C5.46661 12.9965 6.37639 13.9062 7.49858 13.9062C8.62083 13.9062 9.53058 12.9965 9.53058 11.8743V11.0938M5.46661 11.0938H7.49859H9.53058M9.53058 11.0938V10.6878C9.54789 10.432 9.85184 10.1149 9.85184 10.1149\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinecap=\"square\"\n                strokeLinejoin=\"round\"\n            />\n            <path\n                d=\"M1.71875 1.71875L12.5 12.5\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n            <path\n                d=\"M11.3816 8.58925C11.7938 7.9065 12.0309 7.10621 12.0309 6.25056C12.0309 3.74771 10.002 1.71875 7.4991 1.71875C6.63093 1.71875 5.58622 1.90585 5.22461 2.2983\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    Brand: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"15\"\n            height=\"15\"\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M2.96875 2.1875H6.40625V1.25H2.96875V2.1875ZM7.1875 2.96875V10.3125H8.125V2.96875H7.1875ZM2.1875 10.3125V2.96875H1.25V10.3125H2.1875ZM4.6875 12.8125C3.30679 12.8125 2.1875 11.6932 2.1875 10.3125H1.25C1.25 12.211 2.78902 13.75 4.6875 13.75V12.8125ZM7.1875 10.3125C7.1875 11.6932 6.06821 12.8125 4.6875 12.8125V13.75C6.586 13.75 8.125 12.211 8.125 10.3125H7.1875ZM2.96875 1.25C2.01951 1.25 1.25 2.01951 1.25 2.96875H2.1875C2.1875 2.53727 2.53727 2.1875 2.96875 2.1875V1.25ZM7.26175 2.41667L10.2387 4.13543L10.7074 3.32352L7.7305 1.60478L7.26175 2.41667ZM10.5246 5.20263L6.85275 11.5625L7.66469 12.0312L11.3366 5.67138L10.5246 5.20263ZM10.8648 4.76152L12.5836 7.7385L13.3954 7.26975L11.6767 4.29277L10.8648 4.76152ZM12.2976 8.80569L5.93772 12.4776L6.4065 13.2894L12.7664 9.61756L12.2976 8.80569ZM12.5836 7.7385C12.7993 8.11213 12.6713 8.58994 12.2976 8.80569L12.7664 9.61756C13.5884 9.143 13.8701 8.09181 13.3954 7.26975L12.5836 7.7385ZM7.7305 1.60478C7.49619 1.4695 7.242 1.39533 6.98762 1.37818L6.92456 2.31356C7.03919 2.32128 7.15419 2.35457 7.26175 2.41667L7.7305 1.60478ZM6.40625 2.1875C6.53044 2.1875 6.64656 2.21612 6.74963 2.2667L7.16256 1.42504C6.93381 1.3128 6.67669 1.25 6.40625 1.25V2.1875ZM6.74963 2.2667C7.00994 2.39441 7.1875 2.66128 7.1875 2.96875H8.125C8.125 2.29011 7.73163 1.70423 7.16256 1.42504L6.74963 2.2667ZM11.6767 4.29277C11.5415 4.05854 11.3585 3.86727 11.1469 3.72526L10.6245 4.50372C10.7199 4.56771 10.8027 4.65399 10.8648 4.76152L11.6767 4.29277ZM10.2387 4.13543C10.3462 4.19751 10.4325 4.28036 10.4965 4.37571L11.2749 3.85327C11.1329 3.64168 10.9416 3.45874 10.7074 3.32352L10.2387 4.13543ZM10.4965 4.37571C10.6581 4.61645 10.6784 4.93636 10.5246 5.20263L11.3366 5.67138C11.6759 5.08366 11.6281 4.37957 11.2749 3.85327L10.4965 4.37571ZM5.3125 10.3125C5.3125 10.6577 5.03268 10.9375 4.6875 10.9375V11.875C5.55044 11.875 6.25 11.1754 6.25 10.3125H5.3125ZM4.6875 10.9375C4.34232 10.9375 4.0625 10.6577 4.0625 10.3125H3.125C3.125 11.1754 3.82456 11.875 4.6875 11.875V10.9375ZM4.0625 10.3125C4.0625 9.96731 4.34232 9.6875 4.6875 9.6875V8.75C3.82456 8.75 3.125 9.44956 3.125 10.3125H4.0625ZM4.6875 9.6875C5.03268 9.6875 5.3125 9.96731 5.3125 10.3125H6.25C6.25 9.44956 5.55044 8.75 4.6875 8.75V9.6875Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    Edit: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"15\"\n            height=\"15\"\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n        >\n            <path\n                d=\"M7.03125 2.8125C7.29013 2.8125 7.5 2.60263 7.5 2.34375C7.5 2.08487 7.29013 1.875 7.03125 1.875V2.8125ZM13.125 7.96875V7.5H12.1875V7.96875H13.125ZM10.6563 12.1875H4.34375V13.125H10.6563V12.1875ZM2.8125 10.6563V4.34375H1.875V10.6563H2.8125ZM4.34375 2.8125H7.03125V1.875H4.34375V2.8125ZM12.1875 7.96875V10.6563H13.125V7.96875H12.1875ZM4.34375 12.1875C3.98598 12.1875 3.74585 12.1871 3.56093 12.172C3.38147 12.1574 3.29567 12.1312 3.23907 12.1024L2.81346 12.9377C3.02424 13.0451 3.24716 13.087 3.48458 13.1064C3.71654 13.1254 4.00145 13.125 4.34375 13.125V12.1875ZM1.875 10.6563C1.875 10.9986 1.87464 11.2834 1.89359 11.5154C1.91299 11.7529 1.95493 11.9758 2.06233 12.1866L2.89765 11.7609C2.86881 11.7043 2.84264 11.6185 2.82798 11.4391C2.81286 11.2541 2.8125 11.014 2.8125 10.6563H1.875ZM3.23907 12.1024C3.09207 12.0274 2.97255 11.9079 2.89765 11.7609L2.06233 12.1866C2.22711 12.5099 2.49005 12.7729 2.81346 12.9377L3.23907 12.1024ZM10.6563 13.125C10.9986 13.125 11.2834 13.1254 11.5154 13.1064C11.7529 13.087 11.9758 13.0451 12.1866 12.9377L11.7609 12.1024C11.7043 12.1312 11.6185 12.1574 11.4391 12.172C11.2541 12.1871 11.014 12.1875 10.6563 12.1875V13.125ZM12.1875 10.6563C12.1875 11.014 12.1871 11.2541 12.172 11.4391C12.1574 11.6185 12.1312 11.7043 12.1024 11.7609L12.9377 12.1866C13.0451 11.9758 13.087 11.7529 13.1064 11.5154C13.1254 11.2834 13.125 10.9986 13.125 10.6563H12.1875ZM12.1866 12.9377C12.5099 12.7729 12.7729 12.5099 12.9377 12.1866L12.1024 11.7609C12.0274 11.9079 11.9079 12.0274 11.7609 12.1024L12.1866 12.9377ZM2.8125 4.34375C2.8125 3.98598 2.81286 3.74585 2.82798 3.56093C2.84264 3.38147 2.86881 3.29567 2.89765 3.23907L2.06233 2.81346C1.95493 3.02424 1.91299 3.24716 1.89359 3.48458C1.87464 3.71654 1.875 4.00145 1.875 4.34375H2.8125ZM4.34375 1.875C4.00145 1.875 3.71654 1.87464 3.48458 1.89359C3.24716 1.91299 3.02424 1.95493 2.81346 2.06233L3.23907 2.89765C3.29567 2.86881 3.38147 2.84264 3.56093 2.82798C3.74585 2.81286 3.98598 2.8125 4.34375 2.8125V1.875ZM2.89765 3.23907C2.97255 3.09207 3.09207 2.97255 3.23907 2.89765L2.81346 2.06233C2.49005 2.22711 2.22711 2.49005 2.06233 2.81346L2.89765 3.23907Z\"\n                fill=\"currentColor\"\n            />\n            <path\n                d=\"M5.46875 9.52908V7.91302C5.46875 7.7472 5.5346 7.58827 5.65181 7.47102L10.9911 2.13174C11.4793 1.64359 12.2707 1.64359 12.7589 2.13174L12.8661 2.23897C13.3543 2.72713 13.3543 3.51859 12.8661 4.00674L7.52681 9.34608C7.40963 9.46327 7.25063 9.52908 7.08488 9.52908H5.46875Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"0.9375\"\n                strokeLinecap=\"square\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n\n    ArrowDown: ArrowDownIcon,\n    ArrowLeft: ArrowLeftIcon,\n    ArrowRight: ArrowRightIcon,\n    ArrowUp: ArrowUpIcon,\n    AlignLeft: AlignLeftIcon,\n    AlignCenterHorizontally: AlignCenterHorizontallyIcon,\n    AlignRight: AlignRightIcon,\n    AlignTop: AlignTopIcon,\n    AlignCenterVertically: AlignCenterVerticallyIcon,\n    AlignBottom: AlignBottomIcon,\n\n    BorderAll: BorderAllIcon,\n    BorderBottom: BorderBottomIcon,\n    BorderDashed: BorderDashedIcon,\n    BorderDotted: BorderDottedIcon,\n    BorderLeft: BorderLeftIcon,\n    BorderRight: BorderRightIcon,\n    BorderSolid: BorderSolidIcon,\n    BorderTop: BorderTopIcon,\n    Box: BoxIcon,\n    Button: ButtonIcon,\n    Bookmark: BookmarkIcon,\n    BookmarkFilled: BookmarkFilledIcon,\n\n    ChatBubble: ChatBubbleIcon,\n    Check: CheckIcon,\n    CheckCircled: CheckCircledIcon,\n    Checkbox: CheckboxIcon,\n    ChevronDown: ChevronDownIcon,\n    ChevronRight: ChevronRightIcon,\n    ChevronUp: ChevronUpIcon,\n    CircleBackslash: CircleBackslashIcon,\n    Clipboard: ClipboardIcon,\n    ClipboardCopy: ClipboardCopyIcon,\n    Code: CodeIcon,\n    ComponentInstance: ComponentInstanceIcon,\n    Copy: CopyIcon,\n    CornerTopLeft: CornerTopLeftIcon,\n    Corners: CornersIcon,\n    CounterClockwiseClock: CounterClockwiseClockIcon,\n    CrossL: Cross1Icon,\n    CrossS: Cross2Icon,\n    CrossCircled: CrossCircledIcon,\n    Cube: CubeIcon,\n    CursorArrow: CursorArrowIcon,\n    Circle: CircleIcon,\n\n    Desktop: DesktopIcon,\n    DiscordLogo: DiscordLogoIcon,\n    DotsVertical: DotsVerticalIcon,\n    DotsHorizontal: DotsHorizontalIcon,\n    Download: DownloadIcon,\n    DropdownMenu: DropdownMenuIcon,\n    DragHandleDots: DragHandleDots2Icon,\n\n    ExclamationTriangle: ExclamationTriangleIcon,\n    Exit: ExitIcon,\n    ExternalLink: ExternalLinkIcon,\n    EyeOpen: EyeOpenIcon,\n    EyeClosed: EyeClosedIcon,\n    EnvelopeClosed: EnvelopeClosedIcon,\n\n    File: FileIcon,\n    FilePlus: FilePlusIcon,\n    Frame: FrameIcon,\n\n    Gear: GearIcon,\n    GitHubLogo: GitHubLogoIcon,\n    Globe: GlobeIcon,\n    Group: GroupIcon,\n\n    Hand: HandIcon,\n\n    Image: ImageIcon,\n    Input: InputIcon,\n    InfoCircled: InfoCircledIcon,\n\n    Keyboard: KeyboardIcon,\n\n    Laptop: LaptopIcon,\n    Link: Link2Icon,\n    LinkNone: LinkNone1Icon,\n    ListBullet: ListBulletIcon,\n    LockOpen: LockOpen1Icon,\n    LockClosed: LockClosedIcon,\n\n    MagnifyingGlass: MagnifyingGlassIcon,\n    MagicWand: MagicWandIcon,\n    Minus: MinusIcon,\n    MinusCircled: MinusCircledIcon,\n    Mobile: MobileIcon,\n    Moon: MoonIcon,\n    MixerHorizontal: MixerHorizontalIcon,\n    MixerVertical: MixerVerticalIcon,\n\n    Pencil: Pencil1Icon,\n    PencilPaper: Pencil2Icon,\n    Pilcrow: PilcrowIcon,\n    PinLeft: PinLeftIcon,\n    PinRight: PinRightIcon,\n    Plus: PlusIcon,\n    PlusCircled: PlusCircledIcon,\n    Person: PersonIcon,\n\n    QuestionMarkCircled: QuestionMarkCircledIcon,\n    Reload: ReloadIcon,\n    Reset: ResetIcon,\n    RowSpacing: RowSpacingIcon,\n\n    Scissors: ScissorsIcon,\n    Section: SectionIcon,\n    Shadow: ShadowIcon,\n    Share: Share2Icon,\n    Size: SizeIcon,\n    Sun: SunIcon,\n    SpaceBetweenHorizontally: SpaceBetweenHorizontallyIcon,\n    SpaceBetweenVertically: SpaceBetweenVerticallyIcon,\n    Square: SquareIcon,\n    SketchLogo: SketchLogoIcon,\n\n    MailX: MailXIcon,\n\n    Text: TextIcon,\n    TextAlignCenter: TextAlignCenterIcon,\n    TextAlignLeft: TextAlignLeftIcon,\n    TextAlignRight: TextAlignRightIcon,\n    TextAlignJustified: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M3.75 4.75H20.25M3.75 12H20.25M3.75 19.25H20.25\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    Trash: TrashIcon,\n    Tokens: TokensIcon,\n    Upload: UploadIcon,\n\n    Video: VideoIcon,\n    ViewGrid: ViewGridIcon,\n    ViewHorizontal: ViewHorizontalIcon,\n    ViewVertical: ViewVerticalIcon,\n    EmptyState: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"130\"\n            height=\"119\"\n            viewBox=\"0 0 130 119\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <g filter=\"url(#filter0_d_4954_166260)\">\n                <rect\n                    x=\"14.75\"\n                    y=\"10.5\"\n                    width=\"90\"\n                    height=\"90\"\n                    fill=\"url(#paint0_linear)\"\n                    fillOpacity=\"0.2\"\n                    shapeRendering=\"crispEdges\"\n                />\n                <rect\n                    x=\"15\"\n                    y=\"10.75\"\n                    width=\"89.5\"\n                    height=\"89.5\"\n                    className=\"stroke-gray-300 dark:stroke-gray-600\"\n                    strokeWidth=\"0.5\"\n                    strokeDasharray=\"4 4\"\n                    shapeRendering=\"crispEdges\"\n                />\n            </g>\n            <g filter=\"url(#filter1_d_4954_166260)\">\n                <path\n                    d=\"M112.018 81.4171L117.758 78.8025L111.789 65.6879L122.654 65.2087L98.5703 44.3438V76.1775L106.039 68.3025L112.018 81.4171Z\"\n                    className=\"fill-gray-200 stroke-gray-400 dark:fill-gray-900 dark:stroke-gray-500\"\n                    strokeLinejoin=\"round\"\n                />\n            </g>\n            <defs>\n                <linearGradient\n                    id=\"paint0_linear\"\n                    x1=\"59.75\"\n                    y1=\"10.5\"\n                    x2=\"59.75\"\n                    y2=\"100.5\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop className=\"[stop-color:var(--color-gray-50)] dark:[stop-color:var(--color-gray-700)]\" />\n                    <stop\n                        offset=\"1\"\n                        className=\"[stop-color:var(--color-gray-200)] dark:[stop-color:var(--color-gray-900)]\"\n                    />\n                </linearGradient>\n                <filter\n                    id=\"filter0_d_4954_166260\"\n                    x=\"0.75\"\n                    y=\"0.5\"\n                    width=\"118\"\n                    height=\"118\"\n                    filterUnits=\"userSpaceOnUse\"\n                    colorInterpolationFilters=\"sRGB\"\n                >\n                    <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\n                    <feColorMatrix\n                        in=\"SourceAlpha\"\n                        type=\"matrix\"\n                        values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\n                        result=\"hardAlpha\"\n                    />\n                    <feOffset dy=\"4\" />\n                    <feGaussianBlur stdDeviation=\"7\" />\n                    <feComposite in2=\"hardAlpha\" operator=\"out\" />\n                    <feColorMatrix\n                        type=\"matrix\"\n                        values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0\"\n                    />\n                    <feBlend\n                        mode=\"normal\"\n                        in2=\"BackgroundImageFix\"\n                        result=\"effect1_dropShadow_4954_166260\"\n                    />\n                    <feBlend\n                        mode=\"normal\"\n                        in=\"SourceGraphic\"\n                        in2=\"effect1_dropShadow_4954_166260\"\n                        result=\"shape\"\n                    />\n                </filter>\n                <filter\n                    id=\"filter1_d_4954_166260\"\n                    x=\"85.25\"\n                    y=\"43\"\n                    width=\"48\"\n                    height=\"48\"\n                    filterUnits=\"userSpaceOnUse\"\n                    colorInterpolationFilters=\"sRGB\"\n                >\n                    <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\n                    <feColorMatrix\n                        in=\"SourceAlpha\"\n                        type=\"matrix\"\n                        values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\n                        result=\"hardAlpha\"\n                    />\n                    <feOffset dy=\"4\" />\n                    <feGaussianBlur stdDeviation=\"2\" />\n                    <feComposite in2=\"hardAlpha\" operator=\"out\" />\n                    <feColorMatrix\n                        type=\"matrix\"\n                        values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0\"\n                    />\n                    <feBlend\n                        mode=\"normal\"\n                        in2=\"BackgroundImageFix\"\n                        result=\"effect1_dropShadow_4954_166260\"\n                    />\n                    <feBlend\n                        mode=\"normal\"\n                        in=\"SourceGraphic\"\n                        in2=\"effect1_dropShadow_4954_166260\"\n                        result=\"shape\"\n                    />\n                </filter>\n            </defs>\n        </svg>\n    ),\n    TextColorSymbol: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"14\"\n            height=\"14\"\n            viewBox=\"0 0 14 14\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M7.78056 0.966376C7.63595 0.666883 7.3327 0.476562 7.00012 0.476562C6.66754 0.476562 6.36429 0.666883 6.21968 0.966376L1.00048 11.7751C0.792354 12.2061 0.973047 12.7242 1.40407 12.9324C1.8351 13.1405 2.35324 12.9598 2.56137 12.5288L4.28366 8.96199H9.71658L11.4389 12.5288C11.647 12.9598 12.1651 13.1405 12.5962 12.9324C13.0272 12.7242 13.2079 12.2061 12.9998 11.7751L7.78056 0.966376ZM8.94399 7.36199L7.00012 3.33634L5.05626 7.36199H8.94399Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    Width: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M6 12H18M6 12L8 9M6 12L8 15M18 12L16 9M18 12L16 15M21 21V3M3 21V3\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    Height: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M12 18L12 6M12 18L9 16M12 18L15 16M12 6L9 8M12 6L15 8M21 3H3M21 21H3\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    Padding: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M20.25 17.05V6.95C20.25 5.8299 20.25 5.26984 20.032 4.84202C19.8403 4.46569 19.5343 4.15973 19.158 3.96799C18.7302 3.75 18.1701 3.75 17.05 3.75H6.95C5.8299 3.75 5.26984 3.75 4.84202 3.96799C4.46569 4.15973 4.15973 4.46569 3.96799 4.84202C3.75 5.26984 3.75 5.8299 3.75 6.95V17.05C3.75 18.1701 3.75 18.7302 3.96799 19.158C4.15973 19.5343 4.46569 19.8403 4.84202 20.032C5.26984 20.25 5.8299 20.25 6.95 20.25H17.05C18.1701 20.25 18.7302 20.25 19.158 20.032C19.5343 19.8403 19.8403 19.5343 20.032 19.158C20.25 18.7302 20.25 18.1701 20.25 17.05Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n            <path d=\"M9 6.75H15\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n            <path d=\"M9 17.25H15\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n            <path d=\"M17.25 9V15\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n            <path d=\"M6.75 9V15\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n        </svg>\n    ),\n    Margin: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M18 15.6727V8.32727C18 7.51265 18 7.10534 17.8415 6.7942C17.702 6.5205 17.4795 6.29799 17.2058 6.15854C16.8947 6 16.4873 6 15.6727 6H8.32727C7.51265 6 7.10534 6 6.7942 6.15854C6.5205 6.29799 6.29799 6.5205 6.15854 6.7942C6 7.10534 6 7.51265 6 8.32727V15.6727C6 16.4873 6 16.8947 6.15854 17.2058C6.29799 17.4795 6.5205 17.702 6.7942 17.8415C7.10534 18 7.51265 18 8.32727 18H15.6727C16.4873 18 16.8947 18 17.2058 17.8415C17.4795 17.702 17.702 17.4795 17.8415 17.2058C18 16.8947 18 16.4873 18 15.6727Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n            <path d=\"M7.5 3H16.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n            <path d=\"M7.5 21H16.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n            <path d=\"M21 7.5V16.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n            <path d=\"M3 7.5L3 16.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n        </svg>\n    ),\n    CornerRadius: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M20.25 3.75H13.35C9.98969 3.75 8.30953 3.75 7.02606 4.40396C5.89708 4.9792 4.9792 5.89708 4.40396 7.02606C3.75 8.30953 3.75 9.98969 3.75 13.35V20.25\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    Layout: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"15\"\n            height=\"15\"\n            viewBox=\"0 0 15 15\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M12.1875 4.34375V10.6563H13.125V4.34375H12.1875ZM10.6563 12.1875H4.34375V13.125H10.6563V12.1875ZM2.8125 10.6563V4.34375H1.875V10.6563H2.8125ZM4.34375 2.8125H10.6563V1.875H4.34375V2.8125ZM4.34375 12.1875C3.98598 12.1875 3.74585 12.1871 3.56093 12.172C3.38147 12.1574 3.29567 12.1312 3.23907 12.1024L2.81346 12.9377C3.02424 13.0451 3.24716 13.087 3.48458 13.1064C3.71654 13.1254 4.00145 13.125 4.34375 13.125V12.1875ZM1.875 10.6563C1.875 10.9986 1.87464 11.2834 1.89359 11.5154C1.91299 11.7529 1.95493 11.9758 2.06233 12.1866L2.89765 11.7609C2.86881 11.7043 2.84264 11.6185 2.82798 11.4391C2.81286 11.2541 2.8125 11.014 2.8125 10.6563H1.875ZM3.23907 12.1024C3.09207 12.0274 2.97255 11.9079 2.89765 11.7609L2.06233 12.1866C2.22711 12.5099 2.49005 12.7729 2.81346 12.9377L3.23907 12.1024ZM12.1875 10.6563C12.1875 11.014 12.1871 11.2541 12.172 11.4391C12.1574 11.6185 12.1312 11.7043 12.1024 11.7609L12.9377 12.1866C13.0451 11.9758 13.087 11.7529 13.1064 11.5154C13.1254 11.2834 13.125 10.9986 13.125 10.6563H12.1875ZM10.6563 13.125C10.9986 13.125 11.2834 13.1254 11.5154 13.1064C11.7529 13.087 11.9758 13.0451 12.1866 12.9377L11.7609 12.1024C11.7043 12.1312 11.6185 12.1574 11.4391 12.172C11.2541 12.1871 11.014 12.1875 10.6563 12.1875V13.125ZM12.1024 11.7609C12.0274 11.9079 11.9079 12.0274 11.7609 12.1024L12.1866 12.9377C12.5099 12.7729 12.7729 12.5099 12.9377 12.1866L12.1024 11.7609ZM13.125 4.34375C13.125 4.00145 13.1254 3.71654 13.1064 3.48458C13.087 3.24716 13.0451 3.02424 12.9377 2.81346L12.1024 3.23907C12.1312 3.29567 12.1574 3.38147 12.172 3.56093C12.1871 3.74585 12.1875 3.98598 12.1875 4.34375H13.125ZM10.6563 2.8125C11.014 2.8125 11.2541 2.81286 11.4391 2.82798C11.6185 2.84264 11.7043 2.86881 11.7609 2.89765L12.1866 2.06233C11.9758 1.95493 11.7529 1.91299 11.5154 1.89359C11.2834 1.87464 10.9986 1.875 10.6563 1.875V2.8125ZM12.9377 2.81346C12.7729 2.49005 12.5099 2.22711 12.1866 2.06233L11.7609 2.89765C11.9079 2.97255 12.0274 3.09207 12.1024 3.23907L12.9377 2.81346ZM2.8125 4.34375C2.8125 3.98598 2.81286 3.74585 2.82798 3.56093C2.84264 3.38147 2.86881 3.29567 2.89765 3.23907L2.06233 2.81346C1.95493 3.02424 1.91299 3.24716 1.89359 3.48458C1.87464 3.71654 1.875 4.00145 1.875 4.34375H2.8125ZM4.34375 1.875C4.00145 1.875 3.71654 1.87464 3.48458 1.89359C3.24716 1.91299 3.02424 1.95493 2.81346 2.06233L3.23907 2.89765C3.29567 2.86881 3.38147 2.84264 3.56093 2.82798C3.74585 2.81286 3.98598 2.8125 4.34375 2.8125V1.875ZM2.89765 3.23907C2.97255 3.09207 3.09207 2.97255 3.23907 2.89765L2.81346 2.06233C2.49005 2.22711 2.22711 2.49005 2.06233 2.81346L2.89765 3.23907Z\"\n                fill=\"currentColor\"\n            />\n            <path d=\"M12.6562 6.09375H2.34375\" stroke=\"currentColor\" strokeLinecap=\"square\" />\n            <path\n                d=\"M7.03125 12.6562V13.125H7.96875V12.6562H7.03125ZM7.96875 6.09375V5.625H7.03125V6.09375H7.96875ZM7.96875 12.6562V6.09375H7.03125V12.6562H7.96875Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    StateCursor: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M11.875 16.375L15.464 12.786C15.594 12.656 15.544 12.435 15.3709 12.3736L4.24478 8.42557C4.04643 8.35519 3.85518 8.54643 3.92557 8.74478L7.87354 19.8709C7.93499 20.044 8.15599 20.094 8.28593 19.964L11.875 16.375ZM11.875 16.375L16.75 21.25\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"square\"\n            />\n            <path\n                d=\"M20.25 3.75H10.75\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"square\"\n            />\n            <path\n                d=\"M20.25 7.75H15.75\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"square\"\n            />\n        </svg>\n    ),\n    BorderEdit: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"16\"\n            height=\"12\"\n            viewBox=\"0 0 16 12\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <rect\n                x=\"1\"\n                y=\"1\"\n                width=\"14\"\n                height=\"1\"\n                fill=\"currentColor\"\n                style={{\n                    fillOpacity: 1,\n                }}\n            />\n            <rect\n                x=\"1\"\n                y=\"4\"\n                width=\"14\"\n                height=\"1.75\"\n                fill=\"currentColor\"\n                style={{\n                    fillOpacity: 1,\n                }}\n            />\n            <rect\n                x=\"1\"\n                y=\"7.75\"\n                width=\"14\"\n                height=\"3.25\"\n                fill=\"currentColor\"\n                style={{\n                    fillOpacity: 1,\n                }}\n            />\n        </svg>\n    ),\n\n    BackgroundImage: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M12.5 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21H17C17.93 21 18.395 21 18.7765 20.8978C19.8117 20.6204 20.6204 19.8117 20.8978 18.7765C21 18.395 21 17.93 21 17M19 8V2M16 5H22M10.5 8.5C10.5 9.60457 9.60457 10.5 8.5 10.5C7.39543 10.5 6.5 9.60457 6.5 8.5C6.5 7.39543 7.39543 6.5 8.5 6.5C9.60457 6.5 10.5 7.39543 10.5 8.5ZM14.99 11.9181L6.53115 19.608C6.05536 20.0406 5.81747 20.2568 5.79643 20.4442C5.77819 20.6066 5.84045 20.7676 5.96319 20.8755C6.10478 21 6.42628 21 7.06929 21H16.456C17.8951 21 18.6147 21 19.1799 20.7582C19.8894 20.4547 20.4547 19.8894 20.7582 19.1799C21 18.6147 21 17.8951 21 16.456C21 15.9717 21 15.7296 20.9471 15.5042C20.8805 15.2208 20.753 14.9554 20.5733 14.7264C20.4303 14.5442 20.2412 14.3929 19.8631 14.0905L17.0658 11.8527C16.6874 11.5499 16.4982 11.3985 16.2898 11.3451C16.1061 11.298 15.9129 11.3041 15.7325 11.3627C15.5279 11.4291 15.3486 11.5921 14.99 11.9181Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    LeftSide: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M7.5 3H7.51M7.5 12H7.51M7.5 21H7.51M16.5 3H16.51M16.5 12H16.51M16.5 21H16.51M12 3H12.01M12 12H12.01M12 21H12.01M12 16.5H12.01M12 7.5H12.01M21 3H21.01M21 12H21.01M21 21H21.01M21 16.5H21.01M21 7.5H21.01M3 21V3\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    RightSide: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M7.5 3H7.51M7.5 12H7.51M7.5 21H7.51M16.5 3H16.51M16.5 12H16.51M16.5 21H16.51M12 3H12.01M12 12H12.01M12 21H12.01M12 16.5H12.01M12 7.5H12.01M3 3H3.01M3 12H3.01M3 21H3.01M3 16.5H3.01M3 7.5H3.01M21 21V3\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    TopSide: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M3 21H3.01M3 12H3.01M3 16.5H3.01M3 7.5H3.01M7.5 21H7.51M7.5 12H7.51M16.5 21H16.51M16.5 12H16.51M12 21H12.01M12 12H12.01M12 16.5H12.01M12 7.5H12.01M21 21H21.01M21 12H21.01M21 16.5H21.01M21 7.5H21.01M21 3H3\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    BottomSide: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M3 3H3.01M3 12H3.01M3 7.5H3.01M3 16.5H3.01M7.5 3H7.51M7.5 12H7.51M16.5 3H16.51M16.5 12H16.51M12 3H12.01M12 12H12.01M12 7.5H12.01M12 16.5H12.01M21 3H21.01M21 12H21.01M21 7.5H21.01M21 16.5H21.01M21 21H3\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    CornerTopRight: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M3.75 3.75H10.65C14.0103 3.75 15.6905 3.75 16.9739 4.40396C18.1029 4.9792 19.0208 5.89708 19.596 7.02606C20.25 8.30953 20.25 9.98969 20.25 13.35V20.25\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    CornerBottomRight: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M3.75 20.25H10.65C14.0103 20.25 15.6905 20.25 16.9739 19.596C18.1029 19.0208 19.0208 18.1029 19.596 16.9739C20.25 15.6905 20.25 14.0103 20.25 10.65V3.75\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    CornerBottomLeft: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={className}\n            {...props}\n        >\n            <path\n                d=\"M20.25 20.25H13.35C9.98969 20.25 8.30953 20.25 7.02606 19.596C5.89708 19.0208 4.9792 18.1029 4.40396 16.9739C3.75 15.6905 3.75 14.0103 3.75 10.65V3.75\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    PaintBucket: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"18\"\n            height=\"18\"\n            viewBox=\"0 0 21 17\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n        >\n            <path\n                d=\"M16.1248 10.1174L16.6552 10.6477C16.7958 10.507 16.8749 10.3162 16.8748 10.1173C16.8748 9.9183 16.7957 9.72751 16.655 9.58687L16.1248 10.1174ZM7.5372 0.470882C7.24439 0.177904 6.76952 0.177775 6.47656 0.470595C6.18359 0.763414 6.18346 1.2383 6.47627 1.53127L7.5372 0.470882ZM4.44868 9.79755L5.16196 10.0293L5.16196 10.0293L4.44868 9.79755ZM4.44869 10.4415L5.16198 10.2098L5.16195 10.2097L4.44869 10.4415ZM9.72243 15.7155L9.95424 15.0022L9.95416 15.0021L9.72243 15.7155ZM10.3664 15.7155L10.1346 15.0021L10.1346 15.0022L10.3664 15.7155ZM11.7535 15.5495L16.6552 10.6477L15.5945 9.58704L10.6929 14.4888L11.7535 15.5495ZM16.655 9.58687L10.5748 3.51025L9.5145 4.57126L15.5947 10.6479L16.655 9.58687ZM10.5751 3.51056L7.5372 0.470882L6.47627 1.53127L9.51419 4.57095L10.5751 3.51056ZM10.0447 4.04075C9.51433 3.51041 9.51433 3.51042 9.51432 3.51042C9.51432 3.51043 9.51431 3.51044 9.5143 3.51045C9.51428 3.51046 9.51427 3.51048 9.51425 3.51049C9.51424 3.51051 9.51421 3.51053 9.51419 3.51055C9.51417 3.51058 9.51414 3.5106 9.51411 3.51063C9.51408 3.51066 9.51405 3.51069 9.51402 3.51073C9.51398 3.51076 9.51394 3.5108 9.5139 3.51084C9.51386 3.51088 9.51382 3.51093 9.51377 3.51097C9.51373 3.51102 9.51368 3.51107 9.51363 3.51112C9.51358 3.51117 9.51352 3.51123 9.51346 3.51128C9.5134 3.51134 9.51334 3.5114 9.51328 3.51147C9.51322 3.51153 9.51315 3.51159 9.51308 3.51166C9.51301 3.51173 9.51294 3.5118 9.51287 3.51188C9.51279 3.51195 9.51272 3.51203 9.51264 3.51211C9.51255 3.51219 9.51247 3.51227 9.51239 3.51236C9.5123 3.51245 9.51221 3.51253 9.51212 3.51263C9.51203 3.51272 9.51193 3.51281 9.51184 3.51291C9.51174 3.51301 9.51164 3.51311 9.51154 3.51321C9.51144 3.51331 9.51133 3.51342 9.51122 3.51352C9.51111 3.51363 9.511 3.51374 9.51089 3.51386C9.51078 3.51397 9.51066 3.51409 9.51054 3.51421C9.51042 3.51433 9.5103 3.51445 9.51017 3.51457C9.51005 3.5147 9.50992 3.51482 9.50979 3.51495C9.50966 3.51508 9.50953 3.51522 9.50939 3.51535C9.50926 3.51549 9.50912 3.51563 9.50898 3.51577C9.50884 3.51591 9.5087 3.51605 9.50855 3.5162C9.5084 3.51634 9.50825 3.51649 9.5081 3.51665C9.50795 3.5168 9.5078 3.51695 9.50764 3.51711C9.50748 3.51727 9.50732 3.51743 9.50716 3.51759C9.507 3.51775 9.50683 3.51792 9.50666 3.51808C9.50649 3.51825 9.50632 3.51842 9.50615 3.5186C9.50598 3.51877 9.5058 3.51895 9.50562 3.51912C9.50544 3.5193 9.50526 3.51948 9.50508 3.51967C9.5049 3.51985 9.50471 3.52004 9.50452 3.52023C9.50433 3.52042 9.50414 3.52061 9.50394 3.5208C9.50375 3.521 9.50355 3.52119 9.50335 3.52139C9.50315 3.52159 9.50295 3.5218 9.50275 3.522C9.50254 3.52221 9.50233 3.52241 9.50212 3.52262C9.50191 3.52283 9.5017 3.52305 9.50149 3.52326C9.50127 3.52348 9.50105 3.52369 9.50083 3.52392C9.50061 3.52414 9.50039 3.52436 9.50016 3.52458C9.49994 3.52481 9.49971 3.52504 9.49948 3.52527C9.49925 3.5255 9.49901 3.52573 9.49878 3.52597C9.49854 3.52621 9.4983 3.52644 9.49806 3.52669C9.49782 3.52693 9.49758 3.52717 9.49733 3.52742C9.49708 3.52766 9.49683 3.52791 9.49658 3.52816C9.49633 3.52841 9.49608 3.52867 9.49582 3.52892C9.49557 3.52918 9.49531 3.52944 9.49505 3.5297C9.49478 3.52996 9.49452 3.53023 9.49425 3.53049C9.49399 3.53076 9.49372 3.53103 9.49345 3.5313C9.49317 3.53157 9.4929 3.53185 9.49262 3.53212C9.49235 3.5324 9.49207 3.53268 9.49179 3.53296C9.49151 3.53324 9.49122 3.53353 9.49094 3.53381C9.49065 3.5341 9.49036 3.53439 9.49007 3.53468C9.48978 3.53497 9.48948 3.53526 9.48919 3.53556C9.48889 3.53586 9.48859 3.53616 9.48829 3.53646C9.48799 3.53676 9.48769 3.53706 9.48738 3.53737C9.48707 3.53767 9.48676 3.53798 9.48645 3.53829C9.48614 3.53861 9.48583 3.53892 9.48551 3.53923C9.4852 3.53955 9.48488 3.53987 9.48456 3.54019C9.48424 3.54051 9.48391 3.54083 9.48359 3.54116C9.48326 3.54149 9.48293 3.54181 9.4826 3.54214C9.48227 3.54247 9.48194 3.54281 9.4816 3.54314C9.48127 3.54348 9.48093 3.54382 9.48059 3.54416C9.48025 3.5445 9.47991 3.54484 9.47956 3.54518C9.47922 3.54553 9.47887 3.54587 9.47852 3.54622C9.47817 3.54657 9.47782 3.54693 9.47747 3.54728C9.47711 3.54763 9.47676 3.54799 9.4764 3.54835C9.47604 3.54871 9.47568 3.54907 9.47531 3.54943C9.47495 3.5498 9.47458 3.55016 9.47421 3.55053C9.47385 3.5509 9.47348 3.55127 9.4731 3.55165C9.47273 3.55202 9.47235 3.55239 9.47198 3.55277C9.4716 3.55315 9.47122 3.55353 9.47084 3.55391C9.47045 3.55429 9.47007 3.55468 9.46968 3.55507C9.46929 3.55545 9.46891 3.55584 9.46851 3.55623C9.46812 3.55663 9.46773 3.55702 9.46733 3.55742C9.46694 3.55781 9.46654 3.55821 9.46614 3.55861C9.46574 3.55901 9.46533 3.55942 9.46493 3.55982C9.46452 3.56023 9.46411 3.56063 9.4637 3.56104C9.46329 3.56145 9.46288 3.56187 9.46247 3.56228C9.46205 3.56269 9.46164 3.56311 9.46122 3.56353C9.4608 3.56395 9.46038 3.56437 9.45995 3.56479C9.45953 3.56522 9.45911 3.56564 9.45868 3.56607C9.45825 3.5665 9.45782 3.56693 9.45739 3.56736C9.45696 3.56779 9.45652 3.56823 9.45608 3.56866C9.45565 3.5691 9.45521 3.56954 9.45477 3.56998C9.45433 3.57042 9.45388 3.57087 9.45344 3.57131C9.45299 3.57176 9.45254 3.5722 9.45209 3.57265C9.45164 3.5731 9.45119 3.57356 9.45074 3.57401C9.45028 3.57447 9.44983 3.57492 9.44937 3.57538C9.44891 3.57584 9.44845 3.5763 9.44799 3.57676C9.44752 3.57723 9.44706 3.57769 9.44659 3.57816C9.44612 3.57863 9.44565 3.5791 9.44518 3.57957C9.44471 3.58004 9.44424 3.58051 9.44376 3.58099C9.44328 3.58146 9.44281 3.58194 9.44233 3.58242C9.44185 3.5829 9.44136 3.58339 9.44088 3.58387C9.44039 3.58435 9.43991 3.58484 9.43942 3.58533C9.43893 3.58582 9.43844 3.58631 9.43795 3.5868C9.43745 3.58729 9.43696 3.58779 9.43646 3.58829C9.43597 3.58878 9.43547 3.58928 9.43496 3.58978C9.43446 3.59029 9.43396 3.59079 9.43345 3.59129C9.43295 3.5918 9.43244 3.59231 9.43193 3.59282C9.43142 3.59333 9.43091 3.59384 9.4304 3.59435C9.42988 3.59487 9.42937 3.59538 9.42885 3.5959C9.42833 3.59642 9.42781 3.59694 9.42729 3.59746C9.42677 3.59798 9.42624 3.59851 9.42572 3.59903C9.42519 3.59956 9.42466 3.60009 9.42413 3.60062C9.4236 3.60115 9.42307 3.60168 9.42254 3.60221C9.422 3.60275 9.42147 3.60328 9.42093 3.60382C9.42039 3.60436 9.41985 3.6049 9.41931 3.60544C9.41877 3.60598 9.41822 3.60653 9.41767 3.60707C9.41713 3.60762 9.41658 3.60817 9.41603 3.60872C9.41548 3.60927 9.41493 3.60982 9.41437 3.61038C9.41382 3.61093 9.41326 3.61149 9.41271 3.61205C9.41215 3.6126 9.41159 3.61316 9.41102 3.61373C9.41046 3.61429 9.4099 3.61485 9.40933 3.61542C9.40877 3.61598 9.4082 3.61655 9.40763 3.61712C9.40706 3.61769 9.40649 3.61826 9.40591 3.61884C9.40534 3.61941 9.40476 3.61999 9.40419 3.62056C9.40361 3.62114 9.40303 3.62172 9.40245 3.6223C9.40187 3.62288 9.40128 3.62347 9.4007 3.62405C9.40011 3.62464 9.39952 3.62523 9.39894 3.62582C9.39835 3.6264 9.39775 3.627 9.39716 3.62759C9.39657 3.62818 9.39597 3.62878 9.39538 3.62937C9.39478 3.62997 9.39418 3.63057 9.39358 3.63117C9.39298 3.63177 9.39238 3.63237 9.39177 3.63298C9.39117 3.63358 9.39056 3.63419 9.38996 3.6348C9.38935 3.6354 9.38874 3.63601 9.38813 3.63662C9.38751 3.63724 9.3869 3.63785 9.38629 3.63847C9.38567 3.63908 9.38505 3.6397 9.38443 3.64032C9.38381 3.64094 9.38319 3.64156 9.38257 3.64218C9.38195 3.6428 9.38132 3.64343 9.3807 3.64405C9.38007 3.64468 9.37944 3.64531 9.37881 3.64594C9.37818 3.64657 9.37755 3.6472 9.37692 3.64784C9.37628 3.64847 9.37565 3.6491 9.37501 3.64974C9.37437 3.65038 9.37373 3.65102 9.37309 3.65166C9.37245 3.6523 9.37181 3.65294 9.37116 3.65359C9.37052 3.65423 9.36987 3.65488 9.36923 3.65553C9.36858 3.65617 9.36793 3.65682 9.36728 3.65748C9.36662 3.65813 9.36597 3.65878 9.36532 3.65944C9.36466 3.66009 9.364 3.66075 9.36335 3.66141C9.36269 3.66206 9.36203 3.66272 9.36137 3.66339C9.3607 3.66405 9.36004 3.66471 9.35937 3.66538C9.35871 3.66604 9.35804 3.66671 9.35737 3.66738C9.3567 3.66805 9.35603 3.66872 9.35536 3.66939C9.35469 3.67006 9.35401 3.67074 9.35334 3.67141C9.35266 3.67209 9.35198 3.67277 9.35131 3.67345C9.35063 3.67413 9.34994 3.67481 9.34926 3.67549C9.34858 3.67617 9.3479 3.67686 9.34721 3.67754C9.34652 3.67823 9.34584 3.67892 9.34515 3.67961C9.34446 3.6803 9.34377 3.68099 9.34307 3.68168C9.34238 3.68237 9.34169 3.68307 9.34099 3.68376C9.34029 3.68446 9.3396 3.68516 9.3389 3.68586C9.3382 3.68655 9.3375 3.68726 9.33679 3.68796C9.33609 3.68866 9.33539 3.68937 9.33468 3.69007C9.33398 3.69078 9.33327 3.69148 9.33256 3.69219C9.33185 3.6929 9.33114 3.69361 9.33043 3.69433C9.32972 3.69504 9.329 3.69575 9.32829 3.69647C9.32757 3.69718 9.32685 3.6979 9.32613 3.69862C9.32542 3.69934 9.3247 3.70006 9.32397 3.70078C9.32325 3.7015 9.32253 3.70223 9.3218 3.70295C9.32108 3.70368 9.32035 3.7044 9.31962 3.70513C9.31889 3.70586 9.31816 3.70659 9.31743 3.70732C9.3167 3.70805 9.31597 3.70878 9.31523 3.70952C9.3145 3.71025 9.31376 3.71099 9.31303 3.71173C9.31229 3.71247 9.31155 3.7132 9.31081 3.71395C9.31007 3.71469 9.30933 3.71543 9.30858 3.71617C9.30784 3.71692 9.30709 3.71766 9.30635 3.71841C9.3056 3.71915 9.30485 3.7199 9.3041 3.72065C9.30335 3.7214 9.3026 3.72215 9.30185 3.72291C9.30109 3.72366 9.30034 3.72441 9.29958 3.72517C9.29883 3.72593 9.29807 3.72668 9.29731 3.72744C9.29655 3.7282 9.29579 3.72896 9.29503 3.72972C9.29427 3.73049 9.29351 3.73125 9.29274 3.73201C9.29198 3.73278 9.29121 3.73354 9.29044 3.73431C9.28967 3.73508 9.28891 3.73585 9.28813 3.73662C9.28736 3.73739 9.28659 3.73816 9.28582 3.73894C9.28505 3.73971 9.28427 3.74048 9.28349 3.74126C9.28272 3.74204 9.28194 3.74282 9.28116 3.74359C9.28038 3.74437 9.2796 3.74515 9.27882 3.74594C9.27804 3.74672 9.27725 3.7475 9.27647 3.74829C9.27568 3.74907 9.2749 3.74986 9.27411 3.75065C9.27332 3.75143 9.27253 3.75222 9.27174 3.75301C9.27095 3.75381 9.27016 3.7546 9.26936 3.75539C9.26857 3.75618 9.26778 3.75698 9.26698 3.75777C9.26618 3.75857 9.26539 3.75937 9.26459 3.76017C9.26379 3.76097 9.26299 3.76177 9.26219 3.76257C9.26139 3.76337 9.26058 3.76417 9.25978 3.76498C9.25897 3.76578 9.25817 3.76659 9.25736 3.76739C9.25655 3.7682 9.25575 3.76901 9.25494 3.76982C9.25413 3.77063 9.25332 3.77144 9.2525 3.77225C9.25169 3.77307 9.25088 3.77388 9.25006 3.77469C9.24925 3.77551 9.24843 3.77633 9.24761 3.77714C9.2468 3.77796 9.24598 3.77878 9.24516 3.7796C9.24434 3.78042 9.24351 3.78124 9.24269 3.78207C9.24187 3.78289 9.24104 3.78371 9.24022 3.78454C9.23939 3.78536 9.23857 3.78619 9.23774 3.78702C9.23691 3.78785 9.23608 3.78868 9.23525 3.78951C9.23442 3.79034 9.23359 3.79117 9.23275 3.792C9.23192 3.79284 9.23109 3.79367 9.23025 3.79451C9.22942 3.79534 9.22858 3.79618 9.22774 3.79702C9.2269 3.79785 9.22606 3.79869 9.22522 3.79953C9.22438 3.80038 9.22354 3.80122 9.2227 3.80206C9.22185 3.8029 9.22101 3.80375 9.22016 3.80459C9.21932 3.80544 9.21847 3.80629 9.21762 3.80713C9.21678 3.80798 9.21593 3.80883 9.21508 3.80968C9.21423 3.81053 9.21337 3.81138 9.21252 3.81224C9.21167 3.81309 9.21081 3.81394 9.20996 3.8148C9.2091 3.81565 9.20825 3.81651 9.20739 3.81737C9.20653 3.81822 9.20567 3.81908 9.20481 3.81994C9.20395 3.8208 9.20309 3.82166 9.20223 3.82253C9.20137 3.82339 9.20051 3.82425 9.19964 3.82512C9.19878 3.82598 9.19791 3.82685 9.19704 3.82771C9.19618 3.82858 9.19531 3.82945 9.19444 3.83032C9.19357 3.83119 9.1927 3.83206 9.19183 3.83293C9.19096 3.8338 9.19009 3.83467 9.18921 3.83555C9.18834 3.83642 9.18746 3.8373 9.18659 3.83817C9.18571 3.83905 9.18483 3.83992 9.18396 3.8408C9.18308 3.84168 9.1822 3.84256 9.18132 3.84344C9.18044 3.84432 9.17956 3.8452 9.17867 3.84608C9.17779 3.84697 9.17691 3.84785 9.17602 3.84874C9.17514 3.84962 9.17425 3.85051 9.17337 3.85139C9.17248 3.85228 9.17159 3.85317 9.1707 3.85406C9.16981 3.85495 9.16892 3.85584 9.16803 3.85673C9.16714 3.85762 9.16625 3.85851 9.16536 3.8594C9.16446 3.8603 9.16357 3.86119 9.16267 3.86209C9.16178 3.86298 9.16088 3.86388 9.15999 3.86477C9.15909 3.86567 9.15819 3.86657 9.15729 3.86747C9.15639 3.86837 9.15549 3.86927 9.15459 3.87017C9.15369 3.87107 9.15279 3.87197 9.15188 3.87288C9.15098 3.87378 9.15007 3.87469 9.14917 3.87559C9.14826 3.8765 9.14736 3.8774 9.14645 3.87831C9.14554 3.87922 9.14463 3.88013 9.14372 3.88104C9.14282 3.88194 9.1419 3.88286 9.14099 3.88377C9.14008 3.88468 9.13917 3.88559 9.13826 3.8865C9.13734 3.88742 9.13643 3.88833 9.13551 3.88925C9.1346 3.89016 9.13368 3.89108 9.13277 3.89199C9.13185 3.89291 9.13093 3.89383 9.13001 3.89475C9.12909 3.89567 9.12817 3.89659 9.12725 3.89751C9.12633 3.89843 9.12541 3.89935 9.12449 3.90027C9.12356 3.9012 9.12264 3.90212 9.12172 3.90304C9.12079 3.90397 9.11987 3.90489 9.11894 3.90582C9.11801 3.90675 9.11709 3.90767 9.11616 3.9086C9.11523 3.90953 9.1143 3.91046 9.11337 3.91139C9.11244 3.91232 9.11151 3.91325 9.11058 3.91418C9.10965 3.91511 9.10872 3.91605 9.10778 3.91698C9.10685 3.91791 9.10591 3.91885 9.10498 3.91978C9.10404 3.92072 9.10311 3.92165 9.10217 3.92259C9.10123 3.92353 9.1003 3.92446 9.09936 3.9254C9.09842 3.92634 9.09748 3.92728 9.09654 3.92822C9.0956 3.92916 9.09466 3.9301 9.09372 3.93104C9.09278 3.93199 9.09183 3.93293 9.09089 3.93387C9.08995 3.93482 9.089 3.93576 9.08806 3.93671C9.08711 3.93765 9.08616 3.9386 9.08522 3.93954C9.08427 3.94049 9.08332 3.94144 9.08238 3.94239C9.08143 3.94334 9.08048 3.94428 9.07953 3.94523C9.07858 3.94618 9.07763 3.94714 9.07668 3.94809C9.07572 3.94904 9.07477 3.94999 9.07382 3.95094C9.07287 3.9519 9.07191 3.95285 9.07096 3.95381C9.07 3.95476 9.06905 3.95572 9.06809 3.95667C9.06713 3.95763 9.06618 3.95859 9.06522 3.95954C9.06426 3.9605 9.0633 3.96146 9.06235 3.96242C9.06139 3.96338 9.06043 3.96434 9.05947 3.9653C9.05851 3.96626 9.05754 3.96722 9.05658 3.96818C9.05562 3.96914 9.05466 3.97011 9.05369 3.97107C9.05273 3.97203 9.05177 3.973 9.0508 3.97396C9.04984 3.97493 9.04887 3.97589 9.0479 3.97686C9.04694 3.97783 9.04597 3.97879 9.045 3.97976C9.04404 3.98073 9.04307 3.9817 9.0421 3.98267C9.04113 3.98363 9.04016 3.9846 9.03919 3.98557C9.03822 3.98655 9.03725 3.98752 9.03628 3.98849C9.0353 3.98946 9.03433 3.99043 9.03336 3.99141C9.03239 3.99238 9.03141 3.99335 9.03044 3.99433C9.02946 3.9953 9.02849 3.99628 9.02751 3.99725C9.02654 3.99823 9.02556 3.9992 9.02458 4.00018C9.02361 4.00116 9.02263 4.00213 9.02165 4.00311C9.02067 4.00409 9.0197 4.00507 9.01872 4.00605C9.01774 4.00703 9.01676 4.00801 9.01578 4.00899C9.0148 4.00997 9.01381 4.01095 9.01283 4.01193C9.01185 4.01291 9.01087 4.0139 9.00989 4.01488C9.0089 4.01586 9.00792 4.01685 9.00694 4.01783C9.00595 4.01881 9.00497 4.0198 9.00398 4.02078C9.003 4.02177 9.00201 4.02276 9.00102 4.02374C9.00004 4.02473 8.99905 4.02572 8.99806 4.0267C8.99708 4.02769 8.99609 4.02868 8.9951 4.02967C8.99411 4.03066 8.99312 4.03164 8.99213 4.03263C8.99114 4.03362 8.99015 4.03461 8.98916 4.03561C8.98817 4.0366 8.98718 4.03759 8.98619 4.03858C8.9852 4.03957 8.9842 4.04056 8.98321 4.04156C8.98222 4.04255 8.98122 4.04354 8.98023 4.04454C8.97924 4.04553 8.97824 4.04652 8.97725 4.04752C8.97625 4.04851 8.97526 4.04951 8.97426 4.05051C8.97327 4.0515 8.97227 4.0525 8.97127 4.05349C8.97028 4.05449 8.96928 4.05549 8.96828 4.05649C8.96728 4.05748 8.96628 4.05848 8.96529 4.05948C8.96429 4.06048 8.96329 4.06148 8.96229 4.06248C8.96129 4.06348 8.96029 4.06448 8.95929 4.06548C8.95829 4.06648 8.95729 4.06748 8.95629 4.06848C8.95528 4.06948 8.95428 4.07048 8.95328 4.07149C8.95228 4.07249 8.95128 4.07349 8.95027 4.07449C8.94927 4.0755 8.94827 4.0765 8.94726 4.07751C8.94626 4.07851 8.94525 4.07951 8.94425 4.08052C8.94324 4.08152 8.94224 4.08253 8.94123 4.08353C8.94023 4.08454 8.93922 4.08555 8.93822 4.08655C8.93721 4.08756 8.9362 4.08856 8.9352 4.08957C8.93419 4.09058 8.93318 4.09159 8.93217 4.09259C8.93117 4.0936 8.93016 4.09461 8.92915 4.09562C8.92814 4.09663 8.92713 4.09764 8.92612 4.09865C8.92511 4.09965 8.9241 4.10066 8.92309 4.10167C8.92208 4.10268 8.92107 4.10369 8.92006 4.10471C8.91905 4.10572 8.91804 4.10673 8.91703 4.10774C8.91602 4.10875 8.91501 4.10976 8.914 4.11077C8.91298 4.11179 8.91197 4.1128 8.91096 4.11381C8.90995 4.11482 8.90893 4.11584 8.90792 4.11685C8.90691 4.11786 8.90589 4.11888 8.90488 4.11989C8.90387 4.1209 8.90285 4.12192 8.90184 4.12293C8.90082 4.12395 8.89981 4.12496 8.89879 4.12598C8.89778 4.12699 8.89676 4.12801 8.89575 4.12902C8.89473 4.13004 8.89372 4.13105 8.8927 4.13207C8.89168 4.13309 8.89067 4.1341 8.88965 4.13512C8.88864 4.13613 8.88762 4.13715 8.8866 4.13817C8.88558 4.13919 8.88457 4.1402 8.88355 4.14122C8.88253 4.14224 8.88151 4.14326 8.8805 4.14427C8.87948 4.14529 8.87846 4.14631 8.87744 4.14733C8.87642 4.14835 8.8754 4.14937 8.87439 4.15038C8.87337 4.1514 8.87235 4.15242 8.87133 4.15344C8.87031 4.15446 8.86929 4.15548 8.86827 4.1565C8.86725 4.15752 8.86623 4.15854 8.86521 4.15956C8.86419 4.16058 8.86317 4.1616 8.86215 4.16262C8.86113 4.16364 8.86011 4.16466 8.85909 4.16568C8.85807 4.1667 8.85705 4.16772 8.85603 4.16874C8.85501 4.16977 8.85398 4.17079 8.85296 4.17181C8.85194 4.17283 8.85092 4.17385 8.8499 4.17487C8.84888 4.1759 8.84785 4.17692 8.84683 4.17794C8.84581 4.17896 8.84479 4.17998 8.84377 4.18101C8.84274 4.18203 8.84172 4.18305 8.8407 4.18407C8.83968 4.18509 8.83865 4.18612 8.83763 4.18714C8.83661 4.18816 8.83559 4.18919 8.83456 4.19021C8.83354 4.19123 8.83252 4.19225 8.83149 4.19328C8.83047 4.1943 8.82945 4.19532 8.82842 4.19635C8.8274 4.19737 8.82638 4.19839 8.82535 4.19942C8.82433 4.20044 8.82331 4.20146 8.82228 4.20249C8.82126 4.20351 8.82024 4.20454 8.81921 4.20556C8.81819 4.20658 8.81717 4.20761 8.81614 4.20863C8.81512 4.20966 8.81409 4.21068 8.81307 4.2117C8.81205 4.21273 8.81102 4.21375 8.81 4.21478C8.80897 4.2158 8.80795 4.21682 8.80693 4.21785C8.8059 4.21887 8.80488 4.2199 8.80385 4.22092C8.80283 4.22194 8.8018 4.22297 8.80078 4.22399C8.79976 4.22502 8.79873 4.22604 8.79771 4.22706C8.79668 4.22809 8.79566 4.22911 8.79464 4.23014C8.79361 4.23116 8.79259 4.23219 8.79156 4.23321C8.79054 4.23423 8.78951 4.23526 8.78849 4.23628C8.78747 4.23731 8.78644 4.23833 8.78542 4.23936C8.78439 4.24038 8.78337 4.2414 8.78235 4.24243C8.78132 4.24345 8.7803 4.24448 8.77927 4.2455C8.77825 4.24652 8.77723 4.24755 8.7762 4.24857C8.77518 4.24959 8.77416 4.25062 8.77313 4.25164C8.77211 4.25267 8.77108 4.25369 8.77006 4.25471C8.76904 4.25574 8.76801 4.25676 8.76699 4.25778C8.76597 4.25881 8.76494 4.25983 8.76392 4.26085C8.7629 4.26188 8.76187 4.2629 8.76085 4.26392C8.75983 4.26495 8.75881 4.26597 8.75778 4.26699C8.75676 4.26801 8.75574 4.26904 8.75471 4.27006C8.75369 4.27108 8.75267 4.27211 8.75165 4.27313C8.75062 4.27415 8.7496 4.27517 8.74858 4.27619C8.74756 4.27722 8.74654 4.27824 8.74551 4.27926C8.74449 4.28028 8.74347 4.2813 8.74245 4.28233C8.74143 4.28335 8.74041 4.28437 8.73938 4.28539C8.73836 4.28641 8.73734 4.28743 8.73632 4.28845C8.7353 4.28947 8.73428 4.2905 8.73326 4.29152C8.73224 4.29254 8.73122 4.29356 8.7302 4.29458C8.72918 4.2956 8.72816 4.29662 8.72714 4.29764C8.72612 4.29866 8.7251 4.29968 8.72408 4.3007C8.72306 4.30172 8.72204 4.30274 8.72102 4.30375C8.72 4.30477 8.71898 4.30579 8.71796 4.30681C8.71695 4.30783 8.71593 4.30885 8.71491 4.30987C8.71389 4.31089 8.71287 4.3119 8.71186 4.31292C8.71084 4.31394 8.70982 4.31496 8.7088 4.31597C8.70779 4.31699 8.70677 4.31801 8.70575 4.31903C8.70473 4.32004 8.70372 4.32106 8.7027 4.32207C8.70169 4.32309 8.70067 4.32411 8.69965 4.32512C8.69864 4.32614 8.69762 4.32715 8.69661 4.32817C8.69559 4.32918 8.69458 4.3302 8.69356 4.33121C8.69255 4.33223 8.69153 4.33324 8.69052 4.33426C8.6895 4.33527 8.68849 4.33629 8.68748 4.3373C8.68646 4.33831 8.68545 4.33933 8.68444 4.34034C8.68342 4.34135 8.68241 4.34237 8.6814 4.34338C8.68039 4.34439 8.67938 4.3454 8.67836 4.34641C8.67735 4.34743 8.67634 4.34844 8.67533 4.34945C8.67432 4.35046 8.67331 4.35147 8.6723 4.35248C8.67129 4.35349 8.67028 4.3545 8.66927 4.35551C8.66826 4.35652 8.66725 4.35753 8.66624 4.35854C8.66523 4.35955 8.66422 4.36056 8.66321 4.36156C8.66221 4.36257 8.6612 4.36358 8.66019 4.36459C8.65918 4.36559 8.65818 4.3666 8.65717 4.36761C8.65616 4.36862 8.65516 4.36962 8.65415 4.37063C8.65314 4.37163 8.65214 4.37264 8.65113 4.37364C8.65013 4.37465 8.64912 4.37565 8.64812 4.37666C8.64712 4.37766 8.64611 4.37867 8.64511 4.37967C8.6441 4.38067 8.6431 4.38168 8.6421 4.38268C8.6411 4.38368 8.64009 4.38469 8.63909 4.38569C8.63809 4.38669 8.63709 4.38769 8.63609 4.38869C8.63509 4.38969 8.63409 4.39069 8.63309 4.39169C8.63209 4.39269 8.63109 4.39369 8.63009 4.39469C8.62909 4.39569 8.62809 4.39669 8.62709 4.39769C8.62609 4.39869 8.62509 4.39969 8.6241 4.40068C8.6231 4.40168 8.6221 4.40268 8.62111 4.40367C8.62011 4.40467 8.61911 4.40567 8.61812 4.40666C8.61712 4.40766 8.61613 4.40865 8.61513 4.40965C8.61414 4.41064 8.61315 4.41163 8.61215 4.41263C8.61116 4.41362 8.61017 4.41461 8.60917 4.41561C8.60818 4.4166 8.60719 4.41759 8.6062 4.41858C8.60521 4.41957 8.60422 4.42056 8.60323 4.42155C8.60224 4.42255 8.60125 4.42354 8.60026 4.42452C8.59927 4.42551 8.59828 4.4265 8.59729 4.42749C8.5963 4.42848 8.59531 4.42947 8.59433 4.43045C8.59334 4.43144 8.59235 4.43243 8.59137 4.43341C8.59038 4.4344 8.5894 4.43538 8.58841 4.43637C8.58743 4.43735 8.58644 4.43834 8.58546 4.43932C8.58448 4.44031 8.58349 4.44129 8.58251 4.44227C8.58153 4.44325 8.58055 4.44424 8.57956 4.44522C8.57858 4.4462 8.5776 4.44718 8.57662 4.44816C8.57564 4.44914 8.57466 4.45012 8.57368 4.4511C8.57271 4.45208 8.57173 4.45305 8.57075 4.45403C8.56977 4.45501 8.5688 4.45599 8.56782 4.45696C8.56684 4.45794 8.56587 4.45891 8.56489 4.45989C8.56392 4.46087 8.56294 4.46184 8.56197 4.46281C8.56099 4.46379 8.56002 4.46476 8.55905 4.46573C8.55808 4.46671 8.5571 4.46768 8.55613 4.46865C8.55516 4.46962 8.55419 4.47059 8.55322 4.47156C8.55225 4.47253 8.55128 4.4735 8.55032 4.47447C8.54935 4.47544 8.54838 4.4764 8.54741 4.47737C8.54644 4.47834 8.54548 4.4793 8.54451 4.48027C8.54355 4.48124 8.54258 4.4822 8.54162 4.48316C8.54065 4.48413 8.53969 4.48509 8.53873 4.48606C8.53776 4.48702 8.5368 4.48798 8.53584 4.48894C8.53488 4.4899 8.53392 4.49086 8.53296 4.49182C8.532 4.49278 8.53104 4.49374 8.53008 4.4947C8.52912 4.49566 8.52816 4.49662 8.52721 4.49758C8.52625 4.49853 8.52529 4.49949 8.52434 4.50044C8.52338 4.5014 8.52243 4.50235 8.52147 4.50331C8.52052 4.50426 8.51957 4.50522 8.51862 4.50617C8.51766 4.50712 8.51671 4.50807 8.51576 4.50902C8.51481 4.50997 8.51386 4.51092 8.51291 4.51187C8.51196 4.51282 8.51101 4.51377 8.51006 4.51472C8.50912 4.51567 8.50817 4.51661 8.50722 4.51756C8.50628 4.51851 8.50533 4.51945 8.50439 4.5204C8.50344 4.52134 8.5025 4.52228 8.50156 4.52323C8.50061 4.52417 8.49967 4.52511 8.49873 4.52605C8.49779 4.52699 8.49685 4.52794 8.49591 4.52887C8.49497 4.52981 8.49403 4.53075 8.49309 4.53169C8.49216 4.53263 8.49122 4.53357 8.49028 4.5345C8.48935 4.53544 8.48841 4.53637 8.48748 4.53731C8.48654 4.53824 8.48561 4.53918 8.48468 4.54011C8.48374 4.54104 8.48281 4.54197 8.48188 4.5429C8.48095 4.54384 8.48002 4.54477 8.47909 4.54569C8.47816 4.54662 8.47723 4.54755 8.47631 4.54848C8.47538 4.54941 8.47445 4.55033 8.47353 4.55126C8.4726 4.55218 8.47168 4.55311 8.47075 4.55403C8.46983 4.55496 8.46891 4.55588 8.46798 4.5568C8.46706 4.55772 8.46614 4.55864 8.46522 4.55956C8.4643 4.56048 8.46338 4.5614 8.46246 4.56232C8.46155 4.56324 8.46063 4.56416 8.45971 4.56507C8.4588 4.56599 8.45788 4.5669 8.45697 4.56782C8.45605 4.56873 8.45514 4.56965 8.45423 4.57056C8.45331 4.57147 8.4524 4.57238 8.45149 4.57329C8.45058 4.5742 8.44967 4.57511 8.44876 4.57602C8.44786 4.57693 8.44695 4.57784 8.44604 4.57874C8.44513 4.57965 8.44423 4.58056 8.44332 4.58146C8.44242 4.58237 8.44152 4.58327 8.44061 4.58417C8.43971 4.58508 8.43881 4.58598 8.43791 4.58688C8.43701 4.58778 8.43611 4.58868 8.43521 4.58958C8.43431 4.59047 8.43341 4.59137 8.43252 4.59227C8.43162 4.59316 8.43073 4.59406 8.42983 4.59495C8.42894 4.59585 8.42804 4.59674 8.42715 4.59763C8.42626 4.59853 8.42537 4.59942 8.42448 4.60031C8.42359 4.6012 8.4227 4.60209 8.42181 4.60298C8.42092 4.60386 8.42004 4.60475 8.41915 4.60564C8.41826 4.60652 8.41738 4.60741 8.4165 4.60829C8.41561 4.60918 8.41473 4.61006 8.41385 4.61094C8.41297 4.61182 8.41209 4.6127 8.41121 4.61358C8.41033 4.61446 8.40945 4.61534 8.40857 4.61622C8.40769 4.61709 8.40682 4.61797 8.40594 4.61884C8.40507 4.61972 8.40419 4.62059 8.40332 4.62147C8.40245 4.62234 8.40158 4.62321 8.40071 4.62408C8.39984 4.62495 8.39897 4.62582 8.3981 4.62669C8.39723 4.62756 8.39636 4.62842 8.3955 4.62929C8.39463 4.63016 8.39377 4.63102 8.3929 4.63188C8.39204 4.63275 8.39118 4.63361 8.39032 4.63447C8.38946 4.63533 8.3886 4.63619 8.38774 4.63705C8.38688 4.63791 8.38602 4.63877 8.38516 4.63963C8.38431 4.64048 8.38345 4.64134 8.3826 4.64219C8.38174 4.64305 8.38089 4.6439 8.38004 4.64475C8.37919 4.6456 8.37834 4.64645 8.37749 4.6473C8.37664 4.64815 8.37579 4.649 8.37494 4.64985C8.37409 4.65069 8.37325 4.65154 8.3724 4.65238C8.37156 4.65323 8.37072 4.65407 8.36987 4.65491C8.36903 4.65576 8.36819 4.6566 8.36735 4.65744C8.36651 4.65828 8.36567 4.65911 8.36484 4.65995C8.364 4.66079 8.36316 4.66163 8.36233 4.66246C8.3615 4.66329 8.36066 4.66413 8.35983 4.66496C8.359 4.66579 8.35817 4.66662 8.35734 4.66745C8.35651 4.66828 8.35568 4.66911 8.35485 4.66994C8.35403 4.67076 8.3532 4.67159 8.35238 4.67241C8.35155 4.67324 8.35073 4.67406 8.34991 4.67488C8.34908 4.67571 8.34826 4.67653 8.34744 4.67735C8.34663 4.67816 8.34581 4.67898 8.34499 4.6798C8.34417 4.68062 8.34336 4.68143 8.34255 4.68224C8.34173 4.68306 8.34092 4.68387 8.34011 4.68468C8.3393 4.68549 8.33849 4.6863 8.33768 4.68711C8.33687 4.68792 8.33606 4.68873 8.33526 4.68953L9.3959 5.75021C9.3967 5.74941 9.39751 5.7486 9.39832 5.74779C9.39913 5.74698 9.39994 5.74617 9.40075 5.74536C9.40156 5.74455 9.40237 5.74374 9.40319 5.74292C9.404 5.74211 9.40481 5.7413 9.40563 5.74048C9.40645 5.73966 9.40727 5.73884 9.40809 5.73803C9.4089 5.73721 9.40972 5.73639 9.41055 5.73556C9.41137 5.73474 9.41219 5.73392 9.41302 5.73309C9.41384 5.73227 9.41467 5.73144 9.41549 5.73062C9.41632 5.72979 9.41715 5.72896 9.41798 5.72813C9.41881 5.7273 9.41964 5.72647 9.42047 5.72564C9.4213 5.72481 9.42214 5.72397 9.42297 5.72314C9.4238 5.72231 9.42464 5.72147 9.42548 5.72063C9.42631 5.71979 9.42715 5.71896 9.42799 5.71812C9.42883 5.71728 9.42967 5.71644 9.43052 5.71559C9.43136 5.71475 9.4322 5.71391 9.43304 5.71306C9.43389 5.71222 9.43474 5.71137 9.43558 5.71053C9.43643 5.70968 9.43728 5.70883 9.43813 5.70798C9.43898 5.70713 9.43983 5.70628 9.44068 5.70543C9.44153 5.70458 9.44238 5.70373 9.44324 5.70287C9.44409 5.70202 9.44495 5.70116 9.4458 5.70031C9.44666 5.69945 9.44752 5.69859 9.44838 5.69773C9.44924 5.69687 9.4501 5.69601 9.45096 5.69515C9.45182 5.69429 9.45268 5.69343 9.45354 5.69256C9.45441 5.6917 9.45527 5.69084 9.45614 5.68997C9.457 5.6891 9.45787 5.68824 9.45874 5.68737C9.45961 5.6865 9.46048 5.68563 9.46135 5.68476C9.46222 5.68389 9.46309 5.68302 9.46396 5.68215C9.46484 5.68127 9.46571 5.6804 9.46658 5.67952C9.46746 5.67865 9.46833 5.67777 9.46921 5.6769C9.47009 5.67602 9.47097 5.67514 9.47185 5.67426C9.47273 5.67338 9.47361 5.6725 9.47449 5.67162C9.47537 5.67074 9.47625 5.66986 9.47714 5.66897C9.47802 5.66809 9.4789 5.6672 9.47979 5.66632C9.48068 5.66543 9.48156 5.66454 9.48245 5.66366C9.48334 5.66277 9.48423 5.66188 9.48512 5.66099C9.48601 5.6601 9.4869 5.65921 9.48779 5.65831C9.48868 5.65742 9.48958 5.65653 9.49047 5.65563C9.49137 5.65474 9.49226 5.65384 9.49316 5.65295C9.49406 5.65205 9.49495 5.65115 9.49585 5.65026C9.49675 5.64936 9.49765 5.64846 9.49855 5.64756C9.49945 5.64666 9.50035 5.64576 9.50125 5.64485C9.50216 5.64395 9.50306 5.64305 9.50396 5.64214C9.50487 5.64124 9.50578 5.64033 9.50668 5.63942C9.50759 5.63852 9.5085 5.63761 9.5094 5.6367C9.51031 5.63579 9.51122 5.63488 9.51213 5.63397C9.51304 5.63306 9.51395 5.63215 9.51487 5.63124C9.51578 5.63033 9.51669 5.62941 9.51761 5.6285C9.51852 5.62758 9.51944 5.62667 9.52035 5.62575C9.52127 5.62484 9.52219 5.62392 9.5231 5.623C9.52402 5.62208 9.52494 5.62116 9.52586 5.62024C9.52678 5.61932 9.5277 5.6184 9.52862 5.61748C9.52955 5.61656 9.53047 5.61564 9.53139 5.61471C9.53232 5.61379 9.53324 5.61286 9.53417 5.61194C9.53509 5.61101 9.53602 5.61009 9.53695 5.60916C9.53787 5.60823 9.5388 5.6073 9.53973 5.60637C9.54066 5.60545 9.54159 5.60452 9.54252 5.60358C9.54345 5.60265 9.54438 5.60172 9.54532 5.60079C9.54625 5.59986 9.54718 5.59892 9.54812 5.59799C9.54905 5.59705 9.54999 5.59612 9.55092 5.59518C9.55186 5.59425 9.5528 5.59331 9.55373 5.59237C9.55467 5.59143 9.55561 5.59049 9.55655 5.58955C9.55749 5.58862 9.55843 5.58767 9.55937 5.58673C9.56031 5.58579 9.56125 5.58485 9.5622 5.58391C9.56314 5.58296 9.56408 5.58202 9.56503 5.58108C9.56597 5.58013 9.56692 5.57919 9.56786 5.57824C9.56881 5.57729 9.56976 5.57635 9.5707 5.5754C9.57165 5.57445 9.5726 5.5735 9.57355 5.57255C9.5745 5.5716 9.57545 5.57065 9.5764 5.5697C9.57735 5.56875 9.5783 5.5678 9.57926 5.56685C9.58021 5.5659 9.58116 5.56494 9.58211 5.56399C9.58307 5.56303 9.58402 5.56208 9.58498 5.56112C9.58593 5.56017 9.58689 5.55921 9.58785 5.55826C9.5888 5.5573 9.58976 5.55634 9.59072 5.55538C9.59168 5.55442 9.59264 5.55346 9.5936 5.5525C9.59456 5.55154 9.59552 5.55058 9.59648 5.54962C9.59744 5.54866 9.5984 5.5477 9.59937 5.54674C9.60033 5.54577 9.60129 5.54481 9.60226 5.54384C9.60322 5.54288 9.60419 5.54192 9.60515 5.54095C9.60612 5.53998 9.60709 5.53902 9.60805 5.53805C9.60902 5.53708 9.60999 5.53612 9.61096 5.53515C9.61192 5.53418 9.61289 5.53321 9.61386 5.53224C9.61483 5.53127 9.6158 5.5303 9.61677 5.52933C9.61775 5.52836 9.61872 5.52739 9.61969 5.52641C9.62066 5.52544 9.62164 5.52447 9.62261 5.52349C9.62358 5.52252 9.62456 5.52155 9.62553 5.52057C9.62651 5.51959 9.62748 5.51862 9.62846 5.51764C9.62944 5.51667 9.63041 5.51569 9.63139 5.51471C9.63237 5.51373 9.63335 5.51276 9.63432 5.51178C9.6353 5.5108 9.63628 5.50982 9.63726 5.50884C9.63824 5.50786 9.63922 5.50688 9.64021 5.5059C9.64119 5.50492 9.64217 5.50393 9.64315 5.50295C9.64413 5.50197 9.64512 5.50099 9.6461 5.5C9.64708 5.49902 9.64807 5.49803 9.64905 5.49705C9.65004 5.49606 9.65102 5.49508 9.65201 5.49409C9.65299 5.49311 9.65398 5.49212 9.65497 5.49113C9.65595 5.49015 9.65694 5.48916 9.65793 5.48817C9.65892 5.48718 9.65991 5.48619 9.6609 5.4852C9.66189 5.48422 9.66288 5.48323 9.66387 5.48223C9.66486 5.48124 9.66585 5.48025 9.66684 5.47926C9.66783 5.47827 9.66882 5.47728 9.66981 5.47629C9.67081 5.47529 9.6718 5.4743 9.67279 5.47331C9.67379 5.47231 9.67478 5.47132 9.67577 5.47033C9.67677 5.46933 9.67776 5.46834 9.67876 5.46734C9.67975 5.46635 9.68075 5.46535 9.68175 5.46435C9.68274 5.46336 9.68374 5.46236 9.68474 5.46136C9.68573 5.46037 9.68673 5.45937 9.68773 5.45837C9.68873 5.45737 9.68973 5.45637 9.69073 5.45537C9.69173 5.45437 9.69273 5.45337 9.69373 5.45237C9.69473 5.45137 9.69573 5.45037 9.69673 5.44937C9.69773 5.44837 9.69873 5.44737 9.69973 5.44637C9.70073 5.44537 9.70174 5.44436 9.70274 5.44336C9.70374 5.44236 9.70474 5.44135 9.70575 5.44035C9.70675 5.43935 9.70776 5.43834 9.70876 5.43734C9.70976 5.43633 9.71077 5.43533 9.71177 5.43432C9.71278 5.43332 9.71378 5.43231 9.71479 5.43131C9.7158 5.4303 9.7168 5.4293 9.71781 5.42829C9.71882 5.42728 9.71982 5.42627 9.72083 5.42527C9.72184 5.42426 9.72285 5.42325 9.72385 5.42224C9.72486 5.42124 9.72587 5.42023 9.72688 5.41922C9.72789 5.41821 9.7289 5.4172 9.72991 5.41619C9.73092 5.41518 9.73193 5.41417 9.73294 5.41316C9.73395 5.41215 9.73496 5.41114 9.73597 5.41013C9.73698 5.40912 9.73799 5.40811 9.739 5.40709C9.74002 5.40608 9.74103 5.40507 9.74204 5.40406C9.74305 5.40305 9.74407 5.40203 9.74508 5.40102C9.74609 5.40001 9.7471 5.39899 9.74812 5.39798C9.74913 5.39697 9.75015 5.39595 9.75116 5.39494C9.75217 5.39392 9.75319 5.39291 9.7542 5.39189C9.75522 5.39088 9.75623 5.38986 9.75725 5.38885C9.75826 5.38783 9.75928 5.38682 9.76029 5.3858C9.76131 5.38479 9.76233 5.38377 9.76334 5.38275C9.76436 5.38174 9.76537 5.38072 9.76639 5.37971C9.76741 5.37869 9.76843 5.37767 9.76944 5.37665C9.77046 5.37564 9.77148 5.37462 9.7725 5.3736C9.77351 5.37258 9.77453 5.37157 9.77555 5.37055C9.77657 5.36953 9.77759 5.36851 9.7786 5.36749C9.77962 5.36647 9.78064 5.36545 9.78166 5.36443C9.78268 5.36342 9.7837 5.3624 9.78472 5.36138C9.78574 5.36036 9.78676 5.35934 9.78778 5.35832C9.7888 5.3573 9.78982 5.35628 9.79084 5.35526C9.79186 5.35424 9.79288 5.35322 9.7939 5.3522C9.79492 5.35118 9.79594 5.35015 9.79696 5.34913C9.79798 5.34811 9.799 5.34709 9.80003 5.34607C9.80105 5.34505 9.80207 5.34403 9.80309 5.34301C9.80411 5.34198 9.80513 5.34096 9.80615 5.33994C9.80718 5.33892 9.8082 5.3379 9.80922 5.33687C9.81024 5.33585 9.81126 5.33483 9.81229 5.33381C9.81331 5.33279 9.81433 5.33176 9.81535 5.33074C9.81638 5.32972 9.8174 5.32869 9.81842 5.32767C9.81945 5.32665 9.82047 5.32563 9.82149 5.3246C9.82251 5.32358 9.82354 5.32256 9.82456 5.32153C9.82558 5.32051 9.82661 5.31949 9.82763 5.31846C9.82865 5.31744 9.82968 5.31642 9.8307 5.31539C9.83172 5.31437 9.83275 5.31335 9.83377 5.31232C9.8348 5.3113 9.83582 5.31027 9.83684 5.30925C9.83787 5.30823 9.83889 5.3072 9.83991 5.30618C9.84094 5.30516 9.84196 5.30413 9.84299 5.30311C9.84401 5.30208 9.84503 5.30106 9.84606 5.30004C9.84708 5.29901 9.84811 5.29799 9.84913 5.29696C9.85016 5.29594 9.85118 5.29491 9.8522 5.29389C9.85323 5.29287 9.85425 5.29184 9.85528 5.29082C9.8563 5.28979 9.85732 5.28877 9.85835 5.28774C9.85937 5.28672 9.8604 5.2857 9.86142 5.28467C9.86245 5.28365 9.86347 5.28262 9.86449 5.2816C9.86552 5.28058 9.86654 5.27955 9.86757 5.27853C9.86859 5.2775 9.86961 5.27648 9.87064 5.27546C9.87166 5.27443 9.87269 5.27341 9.87371 5.27238C9.87473 5.27136 9.87576 5.27034 9.87678 5.26931C9.87781 5.26829 9.87883 5.26726 9.87985 5.26624C9.88088 5.26522 9.8819 5.26419 9.88292 5.26317C9.88395 5.26214 9.88497 5.26112 9.88599 5.2601C9.88702 5.25907 9.88804 5.25805 9.88906 5.25703C9.89009 5.256 9.89111 5.25498 9.89213 5.25396C9.89316 5.25293 9.89418 5.25191 9.8952 5.25089C9.89623 5.24987 9.89725 5.24884 9.89827 5.24782C9.89929 5.2468 9.90032 5.24577 9.90134 5.24475C9.90236 5.24373 9.90338 5.24271 9.90441 5.24169C9.90543 5.24066 9.90645 5.23964 9.90747 5.23862C9.90849 5.2376 9.90952 5.23658 9.91054 5.23555C9.91156 5.23453 9.91258 5.23351 9.9136 5.23249C9.91462 5.23147 9.91565 5.23045 9.91667 5.22942C9.91769 5.2284 9.91871 5.22738 9.91973 5.22636C9.92075 5.22534 9.92177 5.22432 9.92279 5.2233C9.92381 5.22228 9.92483 5.22126 9.92585 5.22024C9.92687 5.21922 9.92789 5.2182 9.92891 5.21718C9.92993 5.21616 9.93095 5.21514 9.93197 5.21412C9.93299 5.2131 9.93401 5.21208 9.93503 5.21106C9.93605 5.21005 9.93706 5.20903 9.93808 5.20801C9.9391 5.20699 9.94012 5.20597 9.94114 5.20495C9.94215 5.20394 9.94317 5.20292 9.94419 5.2019C9.94521 5.20088 9.94622 5.19987 9.94724 5.19885C9.94826 5.19783 9.94928 5.19681 9.95029 5.1958C9.95131 5.19478 9.95232 5.19377 9.95334 5.19275C9.95436 5.19173 9.95537 5.19072 9.95639 5.1897C9.9574 5.18869 9.95842 5.18767 9.95943 5.18666C9.96045 5.18564 9.96146 5.18463 9.96248 5.18361C9.96349 5.1826 9.96451 5.18158 9.96552 5.18057C9.96653 5.17956 9.96755 5.17854 9.96856 5.17753C9.96957 5.17652 9.97059 5.1755 9.9716 5.17449C9.97261 5.17348 9.97362 5.17247 9.97464 5.17145C9.97565 5.17044 9.97666 5.16943 9.97767 5.16842C9.97868 5.16741 9.97969 5.1664 9.9807 5.16539C9.98171 5.16437 9.98272 5.16336 9.98373 5.16235C9.98474 5.16134 9.98575 5.16033 9.98676 5.15933C9.98777 5.15832 9.98878 5.15731 9.98979 5.1563C9.9908 5.15529 9.99181 5.15428 9.99281 5.15327C9.99382 5.15227 9.99483 5.15126 9.99584 5.15025C9.99684 5.14924 9.99785 5.14824 9.99886 5.14723C9.99986 5.14623 10.0009 5.14522 10.0019 5.14421C10.0029 5.14321 10.0039 5.1422 10.0049 5.1412C10.0059 5.14019 10.0069 5.13919 10.0079 5.13819C10.0089 5.13718 10.0099 5.13618 10.0109 5.13517C10.0119 5.13417 10.0129 5.13317 10.0139 5.13217C10.0149 5.13116 10.0159 5.13016 10.0169 5.12916C10.0179 5.12816 10.0189 5.12716 10.0199 5.12616C10.0209 5.12516 10.0219 5.12416 10.0229 5.12316C10.0239 5.12216 10.0249 5.12116 10.0259 5.12016C10.0269 5.11916 10.0279 5.11816 10.0289 5.11717C10.0299 5.11617 10.0309 5.11517 10.0319 5.11417C10.0329 5.11318 10.0339 5.11218 10.0349 5.11119C10.0359 5.11019 10.0369 5.10919 10.0379 5.1082C10.0389 5.1072 10.0399 5.10621 10.0409 5.10522C10.0419 5.10422 10.0429 5.10323 10.0439 5.10224C10.0448 5.10124 10.0458 5.10025 10.0468 5.09926C10.0478 5.09827 10.0488 5.09728 10.0498 5.09629C10.0508 5.09529 10.0518 5.0943 10.0528 5.09331C10.0538 5.09232 10.0548 5.09134 10.0557 5.09035C10.0567 5.08936 10.0577 5.08837 10.0587 5.08738C10.0597 5.0864 10.0607 5.08541 10.0617 5.08442C10.0627 5.08344 10.0636 5.08245 10.0646 5.08146C10.0656 5.08048 10.0666 5.07949 10.0676 5.07851C10.0686 5.07753 10.0695 5.07654 10.0705 5.07556C10.0715 5.07458 10.0725 5.07359 10.0735 5.07261C10.0745 5.07163 10.0754 5.07065 10.0764 5.06967C10.0774 5.06869 10.0784 5.06771 10.0794 5.06673C10.0803 5.06575 10.0813 5.06477 10.0823 5.06379C10.0833 5.06281 10.0842 5.06184 10.0852 5.06086C10.0862 5.05988 10.0872 5.05891 10.0882 5.05793C10.0891 5.05696 10.0901 5.05598 10.0911 5.05501C10.0921 5.05403 10.093 5.05306 10.094 5.05209C10.095 5.05111 10.0959 5.05014 10.0969 5.04917C10.0979 5.0482 10.0989 5.04723 10.0998 5.04625C10.1008 5.04528 10.1018 5.04431 10.1027 5.04335C10.1037 5.04238 10.1047 5.04141 10.1056 5.04044C10.1066 5.03947 10.1076 5.03851 10.1085 5.03754C10.1095 5.03657 10.1105 5.03561 10.1114 5.03464C10.1124 5.03368 10.1134 5.03271 10.1143 5.03175C10.1153 5.03079 10.1163 5.02982 10.1172 5.02886C10.1182 5.0279 10.1191 5.02694 10.1201 5.02598C10.1211 5.02502 10.122 5.02406 10.123 5.0231C10.1239 5.02214 10.1249 5.02118 10.1259 5.02022C10.1268 5.01927 10.1278 5.01831 10.1287 5.01735C10.1297 5.0164 10.1306 5.01544 10.1316 5.01449C10.1326 5.01353 10.1335 5.01258 10.1345 5.01162C10.1354 5.01067 10.1364 5.00972 10.1373 5.00877C10.1383 5.00782 10.1392 5.00686 10.1402 5.00591C10.1411 5.00496 10.1421 5.00402 10.143 5.00307C10.144 5.00212 10.1449 5.00117 10.1459 5.00022C10.1468 4.99928 10.1478 4.99833 10.1487 4.99739C10.1496 4.99644 10.1506 4.9955 10.1515 4.99455C10.1525 4.99361 10.1534 4.99267 10.1544 4.99172C10.1553 4.99078 10.1562 4.98984 10.1572 4.9889C10.1581 4.98796 10.1591 4.98702 10.16 4.98608C10.1609 4.98514 10.1619 4.98421 10.1628 4.98327C10.1637 4.98233 10.1647 4.9814 10.1656 4.98046C10.1666 4.97953 10.1675 4.97859 10.1684 4.97766C10.1694 4.97673 10.1703 4.97579 10.1712 4.97486C10.1722 4.97393 10.1731 4.973 10.174 4.97207C10.1749 4.97114 10.1759 4.97021 10.1768 4.96928C10.1777 4.96835 10.1787 4.96743 10.1796 4.9665C10.1805 4.96557 10.1814 4.96465 10.1824 4.96372C10.1833 4.9628 10.1842 4.96188 10.1851 4.96095C10.1861 4.96003 10.187 4.95911 10.1879 4.95819C10.1888 4.95727 10.1897 4.95635 10.1907 4.95543C10.1916 4.95451 10.1925 4.95359 10.1934 4.95267C10.1943 4.95176 10.1952 4.95084 10.1962 4.94993C10.1971 4.94901 10.198 4.9481 10.1989 4.94718C10.1998 4.94627 10.2007 4.94536 10.2016 4.94445C10.2025 4.94354 10.2035 4.94262 10.2044 4.94172C10.2053 4.94081 10.2062 4.9399 10.2071 4.93899C10.208 4.93808 10.2089 4.93718 10.2098 4.93627C10.2107 4.93537 10.2116 4.93446 10.2125 4.93356C10.2134 4.93265 10.2143 4.93175 10.2152 4.93085C10.2161 4.92995 10.217 4.92905 10.2179 4.92815C10.2188 4.92725 10.2197 4.92635 10.2206 4.92545C10.2215 4.92456 10.2224 4.92366 10.2233 4.92277C10.2242 4.92187 10.2251 4.92098 10.226 4.92008C10.2269 4.91919 10.2278 4.9183 10.2287 4.91741C10.2296 4.91652 10.2305 4.91563 10.2313 4.91474C10.2322 4.91385 10.2331 4.91296 10.234 4.91207C10.2349 4.91119 10.2358 4.9103 10.2367 4.90942C10.2375 4.90853 10.2384 4.90765 10.2393 4.90676C10.2402 4.90588 10.2411 4.905 10.242 4.90412C10.2428 4.90324 10.2437 4.90236 10.2446 4.90148C10.2455 4.9006 10.2464 4.89973 10.2472 4.89885C10.2481 4.89798 10.249 4.8971 10.2499 4.89623C10.2507 4.89535 10.2516 4.89448 10.2525 4.89361C10.2533 4.89274 10.2542 4.89187 10.2551 4.891C10.2559 4.89013 10.2568 4.88926 10.2577 4.88839C10.2586 4.88753 10.2594 4.88666 10.2603 4.8858C10.2611 4.88493 10.262 4.88407 10.2629 4.88321C10.2637 4.88234 10.2646 4.88148 10.2655 4.88062C10.2663 4.87976 10.2672 4.8789 10.268 4.87805C10.2689 4.87719 10.2697 4.87633 10.2706 4.87548C10.2715 4.87462 10.2723 4.87377 10.2732 4.87292C10.274 4.87206 10.2749 4.87121 10.2757 4.87036C10.2766 4.86951 10.2774 4.86866 10.2783 4.86781C10.2791 4.86697 10.28 4.86612 10.2808 4.86527C10.2817 4.86443 10.2825 4.86358 10.2833 4.86274C10.2842 4.8619 10.285 4.86106 10.2859 4.86021C10.2867 4.85937 10.2875 4.85853 10.2884 4.8577C10.2892 4.85686 10.2901 4.85602 10.2909 4.85519C10.2917 4.85435 10.2926 4.85352 10.2934 4.85268C10.2942 4.85185 10.2951 4.85102 10.2959 4.85019C10.2967 4.84936 10.2976 4.84853 10.2984 4.8477C10.2992 4.84687 10.3 4.84604 10.3009 4.84522C10.3017 4.84439 10.3025 4.84357 10.3033 4.84275C10.3042 4.84192 10.305 4.8411 10.3058 4.84028C10.3066 4.83946 10.3074 4.83864 10.3083 4.83782C10.3091 4.83701 10.3099 4.83619 10.3107 4.83537C10.3115 4.83456 10.3123 4.83375 10.3131 4.83293C10.314 4.83212 10.3148 4.83131 10.3156 4.8305C10.3164 4.82969 10.3172 4.82888 10.318 4.82807C10.3188 4.82727 10.3196 4.82646 10.3204 4.82566C10.3212 4.82485 10.322 4.82405 10.3228 4.82325C10.3236 4.82245 10.3244 4.82165 10.3252 4.82085C10.326 4.82005 10.3268 4.81925 10.3276 4.81845C10.3284 4.81766 10.3292 4.81686 10.33 4.81607C10.3308 4.81528 10.3316 4.81449 10.3324 4.81369C10.3332 4.8129 10.334 4.81211 10.3347 4.81133C10.3355 4.81054 10.3363 4.80975 10.3371 4.80897C10.3379 4.80818 10.3387 4.8074 10.3395 4.80662C10.3402 4.80583 10.341 4.80505 10.3418 4.80427C10.3426 4.8035 10.3434 4.80272 10.3441 4.80194C10.3449 4.80116 10.3457 4.80039 10.3465 4.79962C10.3472 4.79884 10.348 4.79807 10.3488 4.7973C10.3495 4.79653 10.3503 4.79576 10.3511 4.79499C10.3519 4.79422 10.3526 4.79346 10.3534 4.79269C10.3541 4.79193 10.3549 4.79117 10.3557 4.7904C10.3564 4.78964 10.3572 4.78888 10.358 4.78812C10.3587 4.78736 10.3595 4.78661 10.3602 4.78585C10.361 4.78509 10.3617 4.78434 10.3625 4.78359C10.3632 4.78283 10.364 4.78208 10.3647 4.78133C10.3655 4.78058 10.3662 4.77983 10.367 4.77909C10.3677 4.77834 10.3685 4.7776 10.3692 4.77685C10.37 4.77611 10.3707 4.77537 10.3714 4.77463C10.3722 4.77388 10.3729 4.77315 10.3737 4.77241C10.3744 4.77167 10.3751 4.77093 10.3759 4.7702C10.3766 4.76946 10.3773 4.76873 10.3781 4.768C10.3788 4.76727 10.3795 4.76654 10.3803 4.76581C10.381 4.76508 10.3817 4.76436 10.3824 4.76363C10.3832 4.76291 10.3839 4.76218 10.3846 4.76146C10.3853 4.76074 10.3861 4.76002 10.3868 4.7593C10.3875 4.75858 10.3882 4.75786 10.3889 4.75715C10.3896 4.75643 10.3904 4.75572 10.3911 4.75501C10.3918 4.75429 10.3925 4.75358 10.3932 4.75287C10.3939 4.75216 10.3946 4.75146 10.3953 4.75075C10.396 4.75005 10.3967 4.74934 10.3974 4.74864C10.3981 4.74794 10.3988 4.74723 10.3995 4.74654C10.4002 4.74584 10.4009 4.74514 10.4016 4.74444C10.4023 4.74375 10.403 4.74305 10.4037 4.74236C10.4044 4.74167 10.4051 4.74098 10.4058 4.74029C10.4065 4.7396 10.4072 4.73891 10.4078 4.73822C10.4085 4.73754 10.4092 4.73685 10.4099 4.73617C10.4106 4.73549 10.4113 4.73481 10.4119 4.73413C10.4126 4.73345 10.4133 4.73277 10.414 4.73209C10.4147 4.73142 10.4153 4.73074 10.416 4.73007C10.4167 4.7294 10.4173 4.72873 10.418 4.72806C10.4187 4.72739 10.4193 4.72672 10.42 4.72606C10.4207 4.72539 10.4213 4.72473 10.422 4.72407C10.4227 4.7234 10.4233 4.72274 10.424 4.72209C10.4246 4.72143 10.4253 4.72077 10.426 4.72012C10.4266 4.71946 10.4273 4.71881 10.4279 4.71816C10.4286 4.7175 10.4292 4.71685 10.4299 4.71621C10.4305 4.71556 10.4312 4.71491 10.4318 4.71427C10.4324 4.71362 10.4331 4.71298 10.4337 4.71234C10.4344 4.7117 10.435 4.71106 10.4357 4.71042C10.4363 4.70978 10.4369 4.70915 10.4376 4.70852C10.4382 4.70788 10.4388 4.70725 10.4395 4.70662C10.4401 4.70599 10.4407 4.70536 10.4413 4.70473C10.442 4.70411 10.4426 4.70348 10.4432 4.70286C10.4438 4.70224 10.4445 4.70162 10.4451 4.701C10.4457 4.70038 10.4463 4.69976 10.4469 4.69915C10.4475 4.69853 10.4482 4.69792 10.4488 4.6973C10.4494 4.69669 10.45 4.69608 10.4506 4.69548C10.4512 4.69487 10.4518 4.69426 10.4524 4.69366C10.453 4.69305 10.4536 4.69245 10.4542 4.69185C10.4548 4.69125 10.4554 4.69065 10.456 4.69005C10.4566 4.68946 10.4572 4.68886 10.4578 4.68827C10.4584 4.68768 10.459 4.68708 10.4596 4.6865C10.4602 4.68591 10.4608 4.68532 10.4613 4.68473C10.4619 4.68415 10.4625 4.68356 10.4631 4.68298C10.4637 4.6824 10.4642 4.68182 10.4648 4.68124C10.4654 4.68067 10.466 4.68009 10.4666 4.67952C10.4671 4.67894 10.4677 4.67837 10.4683 4.6778C10.4688 4.67723 10.4694 4.67666 10.47 4.6761C10.4705 4.67553 10.4711 4.67497 10.4717 4.67441C10.4722 4.67384 10.4728 4.67328 10.4733 4.67273C10.4739 4.67217 10.4745 4.67161 10.475 4.67106C10.4756 4.6705 10.4761 4.66995 10.4767 4.6694C10.4772 4.66885 10.4778 4.6683 10.4783 4.66775C10.4789 4.66721 10.4794 4.66666 10.4799 4.66612C10.4805 4.66558 10.481 4.66504 10.4816 4.6645C10.4821 4.66396 10.4826 4.66343 10.4832 4.66289C10.4837 4.66236 10.4842 4.66183 10.4848 4.6613C10.4853 4.66077 10.4858 4.66024 10.4864 4.65971C10.4869 4.65919 10.4874 4.65866 10.4879 4.65814C10.4885 4.65762 10.489 4.6571 10.4895 4.65658C10.49 4.65606 10.4905 4.65555 10.491 4.65503C10.4916 4.65452 10.4921 4.65401 10.4926 4.6535C10.4931 4.65299 10.4936 4.65248 10.4941 4.65197C10.4946 4.65147 10.4951 4.65097 10.4956 4.65046C10.4961 4.64996 10.4966 4.64946 10.4971 4.64897C10.4976 4.64847 10.4981 4.64797 10.4986 4.64748C10.4991 4.64699 10.4996 4.6465 10.5001 4.64601C10.5005 4.64552 10.501 4.64503 10.5015 4.64455C10.502 4.64407 10.5025 4.64358 10.503 4.6431C10.5034 4.64262 10.5039 4.64214 10.5044 4.64167C10.5049 4.64119 10.5054 4.64072 10.5058 4.64025C10.5063 4.63978 10.5068 4.63931 10.5072 4.63884C10.5077 4.63837 10.5082 4.63791 10.5086 4.63744C10.5091 4.63698 10.5095 4.63652 10.51 4.63606C10.5105 4.6356 10.5109 4.63515 10.5114 4.63469C10.5118 4.63424 10.5123 4.63378 10.5127 4.63333C10.5132 4.63288 10.5136 4.63244 10.5141 4.63199C10.5145 4.63155 10.515 4.6311 10.5154 4.63066C10.5158 4.63022 10.5163 4.62978 10.5167 4.62934C10.5172 4.62891 10.5176 4.62847 10.518 4.62804C10.5185 4.62761 10.5189 4.62718 10.5193 4.62675C10.5197 4.62632 10.5202 4.6259 10.5206 4.62547C10.521 4.62505 10.5214 4.62463 10.5219 4.62421C10.5223 4.62379 10.5227 4.62337 10.5231 4.62296C10.5235 4.62255 10.5239 4.62213 10.5243 4.62172C10.5248 4.62131 10.5252 4.62091 10.5256 4.6205C10.526 4.6201 10.5264 4.61969 10.5268 4.61929C10.5272 4.61889 10.5276 4.61849 10.528 4.6181C10.5284 4.6177 10.5288 4.61731 10.5292 4.61691C10.5295 4.61652 10.5299 4.61613 10.5303 4.61575C10.5307 4.61536 10.5311 4.61497 10.5315 4.61459C10.5319 4.61421 10.5322 4.61383 10.5326 4.61345C10.533 4.61307 10.5334 4.6127 10.5337 4.61233C10.5341 4.61195 10.5345 4.61158 10.5349 4.61121C10.5352 4.61084 10.5356 4.61048 10.536 4.61011C10.5363 4.60975 10.5367 4.60939 10.537 4.60903C10.5374 4.60867 10.5378 4.60831 10.5381 4.60796C10.5385 4.60761 10.5388 4.60725 10.5392 4.6069C10.5395 4.60655 10.5399 4.60621 10.5402 4.60586C10.5405 4.60552 10.5409 4.60518 10.5412 4.60484C10.5416 4.6045 10.5419 4.60416 10.5422 4.60382C10.5426 4.60349 10.5429 4.60315 10.5432 4.60282C10.5436 4.60249 10.5439 4.60217 10.5442 4.60184C10.5446 4.60151 10.5449 4.60119 10.5452 4.60087C10.5455 4.60055 10.5458 4.60023 10.5462 4.59991C10.5465 4.5996 10.5468 4.59929 10.5471 4.59897C10.5474 4.59866 10.5477 4.59835 10.548 4.59805C10.5483 4.59774 10.5486 4.59744 10.5489 4.59714C10.5492 4.59684 10.5495 4.59654 10.5498 4.59624C10.5501 4.59594 10.5504 4.59565 10.5507 4.59536C10.551 4.59507 10.5513 4.59478 10.5516 4.59449C10.5519 4.59421 10.5521 4.59392 10.5524 4.59364C10.5527 4.59336 10.553 4.59308 10.5533 4.5928C10.5535 4.59253 10.5538 4.59225 10.5541 4.59198C10.5544 4.59171 10.5546 4.59144 10.5549 4.59117C10.5552 4.59091 10.5554 4.59064 10.5557 4.59038C10.5559 4.59012 10.5562 4.58986 10.5565 4.5896C10.5567 4.58935 10.557 4.58909 10.5572 4.58884C10.5575 4.58859 10.5577 4.58834 10.558 4.5881C10.5582 4.58785 10.5585 4.58761 10.5587 4.58737C10.5589 4.58712 10.5592 4.58689 10.5594 4.58665C10.5597 4.58641 10.5599 4.58618 10.5601 4.58595C10.5603 4.58572 10.5606 4.58549 10.5608 4.58526C10.561 4.58504 10.5613 4.58482 10.5615 4.5846C10.5617 4.58437 10.5619 4.58416 10.5621 4.58394C10.5623 4.58373 10.5626 4.58351 10.5628 4.5833C10.563 4.58309 10.5632 4.58289 10.5634 4.58268C10.5636 4.58248 10.5638 4.58227 10.564 4.58207C10.5642 4.58187 10.5644 4.58168 10.5646 4.58148C10.5648 4.58129 10.565 4.5811 10.5652 4.58091C10.5653 4.58072 10.5655 4.58053 10.5657 4.58035C10.5659 4.58016 10.5661 4.57998 10.5663 4.5798C10.5664 4.57963 10.5666 4.57945 10.5668 4.57928C10.567 4.5791 10.5671 4.57893 10.5673 4.57876C10.5675 4.5786 10.5676 4.57843 10.5678 4.57827C10.568 4.57811 10.5681 4.57795 10.5683 4.57779C10.5684 4.57763 10.5686 4.57748 10.5687 4.57733C10.5689 4.57717 10.569 4.57702 10.5692 4.57688C10.5693 4.57673 10.5695 4.57659 10.5696 4.57645C10.5698 4.57631 10.5699 4.57617 10.57 4.57603C10.5702 4.5759 10.5703 4.57576 10.5704 4.57563C10.5706 4.5755 10.5707 4.57538 10.5708 4.57525C10.5709 4.57513 10.5711 4.57501 10.5712 4.57489C10.5713 4.57477 10.5714 4.57465 10.5715 4.57454C10.5716 4.57442 10.5718 4.57431 10.5719 4.5742C10.572 4.5741 10.5721 4.57399 10.5722 4.57389C10.5723 4.57379 10.5724 4.57369 10.5725 4.57359C10.5726 4.57349 10.5727 4.5734 10.5728 4.57331C10.5729 4.57321 10.5729 4.57313 10.573 4.57304C10.5731 4.57295 10.5732 4.57287 10.5733 4.57279C10.5734 4.57271 10.5734 4.57263 10.5735 4.57256C10.5736 4.57248 10.5737 4.57241 10.5737 4.57234C10.5738 4.57227 10.5739 4.57221 10.5739 4.57215C10.574 4.57208 10.574 4.57202 10.5741 4.57196C10.5742 4.57191 10.5742 4.57185 10.5743 4.5718C10.5743 4.57175 10.5744 4.5717 10.5744 4.57165C10.5745 4.57161 10.5745 4.57156 10.5745 4.57152C10.5746 4.57148 10.5746 4.57144 10.5747 4.57141C10.5747 4.57137 10.5747 4.57134 10.5748 4.57131C10.5748 4.57128 10.5748 4.57126 10.5748 4.57123C10.5749 4.57121 10.5749 4.57119 10.5749 4.57117C10.5749 4.57116 10.5749 4.57114 10.5749 4.57113C10.5749 4.57112 10.575 4.57111 10.575 4.5711C10.575 4.5711 10.575 4.57109 10.0447 4.04075ZM8.33526 4.68953L4.6146 8.41033L5.67524 9.47101L9.3959 5.75021L8.33526 4.68953ZM4.6146 8.41033C4.41704 8.60789 4.23656 8.78756 4.09931 8.94924C3.95638 9.11761 3.81682 9.31516 3.73539 9.5658L5.16196 10.0293C5.15783 10.042 5.16008 10.0175 5.24279 9.92003C5.33119 9.8159 5.46018 9.68608 5.67524 9.47101L4.6146 8.41033ZM3.7354 9.56579C3.61848 9.92565 3.61844 10.3134 3.73544 10.6734L5.16195 10.2097C5.14292 10.1511 5.1429 10.088 5.16196 10.0293L3.7354 9.56579ZM3.73541 10.6733C3.81683 10.9239 3.95638 11.1214 4.09931 11.2898C4.23656 11.4515 4.41704 11.6312 4.6146 11.8287L5.67524 10.7681C5.46018 10.553 5.33119 10.4232 5.2428 10.319C5.1601 10.2216 5.15784 10.197 5.16198 10.2098L3.73541 10.6733ZM4.6146 11.8287L8.33526 15.5495L9.3959 14.4888L5.67524 10.7681L4.6146 11.8287ZM8.33526 15.5495C8.53282 15.7471 8.71248 15.9276 8.87415 16.0648C9.04252 16.2078 9.24007 16.3473 9.49071 16.4288L9.95416 15.0021C9.96689 15.0063 9.94233 15.004 9.8449 14.9213C9.74078 14.8329 9.61096 14.7039 9.3959 14.4888L8.33526 15.5495ZM9.49062 16.4287C9.85052 16.5457 10.2383 16.5457 10.5982 16.4287L10.1346 15.0022C10.076 15.0212 10.0128 15.0212 9.95424 15.0022L9.49062 16.4287ZM10.5981 16.4288C10.8487 16.3473 11.0463 16.2078 11.2146 16.0648C11.3763 15.9276 11.556 15.7471 11.7535 15.5495L10.6929 14.4888C10.4778 14.7039 10.348 14.8329 10.2439 14.9213C10.1465 15.004 10.1219 15.0063 10.1346 15.0021L10.5981 16.4288Z\"\n                fill=\"currentColor\"\n            />\n            <path\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M16.9781 13.2075C17.4062 12.5139 18.4713 11.4453 18.5244 11.4453C18.5774 11.4453 19.6425 12.5139 20.0707 13.2075C20.4194 13.7723 20.6074 14.2099 20.6074 14.8347C20.6074 16.0359 19.6697 17.0001 18.5244 17.0001C17.379 17.0001 16.4414 16.0359 16.4414 14.8347C16.4414 14.2099 16.6294 13.7723 16.9781 13.2075Z\"\n                fill=\"currentColor\"\n            />\n            <path\n                d=\"M4.17969 9.92188H16.2602L10.2199 15.9623L4.17969 9.92188Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    TextUnderline: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"17\"\n            height=\"16\"\n            viewBox=\"0 0 17 16\"\n            fill=\"none\"\n            className={cn(className)}\n            {...props}\n        >\n            <path\n                d=\"M12.1263 2.66406V7.33073C12.1263 9.53987 10.3354 11.3307 8.1263 11.3307C5.91716 11.3307 4.1263 9.53987 4.1263 7.33073V2.66406M2.79297 13.9974H13.4596\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n\n    TextOverline: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"17\"\n            height=\"16\"\n            viewBox=\"0 0 17 16\"\n            fill=\"none\"\n            className={cn(className)}\n            {...props}\n        >\n            <path\n                d=\"M12.375 4.66406V9.33073C12.375 11.5399 10.5841 13.3307 8.375 13.3307C6.16586 13.3307 4.375 11.5399 4.375 9.33073V4.66406\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n            <path\n                d=\"M3.72656 2.25H12.7266\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n            />\n        </svg>\n    ),\n\n    TextStrikeThrough: ({ className, ...props }: IconProps) => (\n        <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"17\"\n            height=\"16\"\n            viewBox=\"0 0 17 16\"\n            fill=\"none\"\n            className={cn(className)}\n            {...props}\n        >\n            <path\n                d=\"M4.625 10.6641C4.625 12.1368 5.81891 13.3307 7.29167 13.3307H9.95833C11.4311 13.3307 12.625 12.1368 12.625 10.6641C12.625 9.1913 11.4311 7.9974 9.95833 7.9974M12.625 5.33073C12.625 3.85797 11.4311 2.66406 9.95833 2.66406H7.29167C5.81891 2.66406 4.625 3.85797 4.625 5.33073M2.625 7.9974H14.625\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.5\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    AdvancedTypography: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"16\"\n            height=\"16\"\n            viewBox=\"0 0 16 16\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={cn(className)}\n            {...props}\n        >\n            <path\n                d=\"M5.3505 3C5.59778 3.00012 5.82039 3.15139 5.91007 3.38184L9.41007 12.3818L9.4423 12.499C9.48931 12.774 9.33831 13.0539 9.06827 13.1592C8.79823 13.2642 8.49794 13.16 8.34659 12.9258L8.29093 12.8174L7.14835 9.87793H3.55265L2.41007 12.8174C2.28995 13.126 1.94146 13.2791 1.63273 13.1592C1.32398 13.039 1.17085 12.6906 1.29093 12.3818L4.79093 3.38184L4.83097 3.2998C4.93693 3.11618 5.13395 3 5.3505 3ZM12.9501 7.34668C13.9445 7.34673 14.7509 8.15211 14.7509 9.14648C14.7509 10.1409 13.9445 10.9462 12.9501 10.9463C12.1296 10.9461 11.4393 10.3971 11.2226 9.64648H9.75089C9.47475 9.64648 9.25089 9.42263 9.25089 9.14648C9.25089 8.87034 9.47475 8.64648 9.75089 8.64648H11.2226C11.4393 7.89585 12.1296 7.34683 12.9501 7.34668ZM12.9501 8.34668C12.5082 8.34686 12.1503 8.70449 12.1503 9.14648C12.1503 9.58848 12.5082 9.94611 12.9501 9.94629C13.3922 9.94624 13.7509 9.58856 13.7509 9.14648C13.7509 8.70441 13.3922 8.34673 12.9501 8.34668ZM3.90324 8.97754H6.79777L5.3505 5.25586L3.90324 8.97754ZM10.0497 3.49805C10.8699 3.49811 11.5602 4.04677 11.7773 4.79688L14.2499 4.79492L14.3505 4.80469C14.5783 4.85098 14.7504 5.05246 14.7509 5.29395C14.7512 5.57006 14.527 5.79458 14.2509 5.79492L11.7773 5.79688C11.5609 6.54809 10.8707 7.09759 10.0497 7.09766C9.22899 7.09766 8.5389 6.54862 8.32218 5.79785H8.04191C7.76577 5.79785 7.54191 5.57399 7.54191 5.29785C7.54201 5.0218 7.76583 4.79785 8.04191 4.79785H8.32218C8.53902 4.04727 9.22913 3.49805 10.0497 3.49805ZM10.0497 4.49805C9.60768 4.49805 9.25002 4.85584 9.24992 5.29785C9.24992 5.73995 9.60762 6.09766 10.0497 6.09766C10.4918 6.09758 10.8495 5.7399 10.8495 5.29785C10.8494 4.85589 10.4917 4.49812 10.0497 4.49805Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    PencilIcon: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"14\"\n            height=\"14\"\n            viewBox=\"0 0 14 14\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={cn(className)}\n            {...props}\n        >\n            <path\n                d=\"M8.16667 12.2485H12.25M12.0084 2.96523L11.0333 1.99015C10.5777 1.53453 9.83897 1.53453 9.38338 1.99015L2.09171 9.28184C1.87291 9.50059 1.75 9.79733 1.75 10.1068V12.2485H3.89175C4.20117 12.2485 4.49791 12.1256 4.71671 11.9068L12.0084 4.61515C12.464 4.15953 12.464 3.42084 12.0084 2.96523Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"1.16667\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    ),\n    CollapseSidebar: ({ className, ...props }: IconProps) => (\n        <svg\n            width=\"14\"\n            height=\"14\"\n            viewBox=\"0 0 64 64\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className={cn(className)}\n            {...props}\n        >\n            <path\n                d=\"M49.984,56l-35.989,0c-3.309,0 -5.995,-2.686 -5.995,-5.995l0,-36.011c0,-3.308 2.686,-5.995 5.995,-5.995l35.989,0c3.309,0 5.995,2.687 5.995,5.995l0,36.011c0,3.309 -2.686,5.995 -5.995,5.995Zm-25.984,-4.001l0,-39.999l-9.012,0c-1.65,0 -2.989,1.339 -2.989,2.989l0,34.021c0,1.65 1.339,2.989 2.989,2.989l9.012,0Zm24.991,-39.999l-20.991,0l0,39.999l20.991,0c1.65,0 2.989,-1.339 2.989,-2.989l0,-34.021c0,-1.65 -1.339,-2.989 -2.989,-2.989Z\"\n                fill=\"currentColor\"\n            />\n            <path\n                d=\"M19.999,38.774l-6.828,-6.828l6.828,-6.829l2.829,2.829l-4,4l4,4l-2.829,2.828Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    ),\n    FloppyDisk: ({ className, ...props }: IconProps) => (\n        <svg\n            viewBox=\"0 0 16 16\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth=\"1.3\"\n            className={cn(className)}\n            {...props}\n        >\n            <polygon points=\"2.75 2.75,2.75 13.25,13.25 13.25,13.25 5.75,10.25 2.75\" />\n            <polyline points=\"5.75 13.25,5.75 9.75,10.25 9.75,10.25 13.25\" />\n        </svg>\n    ),\n    DirectoryPlus: ({ className, ...props }: IconProps) => (\n        <svg\n            viewBox=\"0 0 23 23\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth=\"1.3\"\n            className={cn(className)}\n            {...props}\n        >\n            <path d=\"M3 5a2 2 0 012-2h5.5a2 2 0 011.5 1.5l1.5 1.5a2 2 0 001.5.5H19a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5z\" />\n            <path d=\"M12 10v4\" />\n            <path d=\"M10 12h4\" />\n        </svg>\n    ),\n} satisfies { [key: string]: React.FC<IconProps> };\n"], "names": [], "mappings": ";;;;AAAA;AAsHA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAOO,MAAM,QAAQ;IACjB,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,OAAM;YACN,SAAQ;YACR,MAAK;YACL,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBAAE,UAAS;;sCACR,8OAAC;4BACG,IAAG;4BACH,OAAO;gCAAE,UAAU;4BAAQ;4BAC3B,WAAU;4BACV,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;sCAEP,cAAA,8OAAC;gCACG,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,MAAK;gCACL,OAAO;oCAAE,MAAM;oCAAS,aAAa;gCAAE;;;;;;;;;;;sCAG/C,8OAAC;4BAAE,MAAK;sCACJ,cAAA,8OAAC;gCACG,UAAS;gCACT,UAAS;gCACT,GAAE;gCACF,MAAK;gCACL,OAAO;oCAAE,MAAM;oCAAW,aAAa;gCAAE;;;;;;;;;;;sCAGjD,8OAAC;4BACG,UAAS;4BACT,UAAS;4BACT,GAAE;4BACF,MAAK;4BACL,OAAO;gCAAE,MAAM;gCAAW,aAAa;4BAAE;;;;;;sCAE7C,8OAAC;4BAAK,IAAG;4BAA6B,MAAK;sCACvC,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;sCAEZ,8OAAC;4BACG,GAAE;4BACF,MAAK;4BACL,OAAO;gCAAE,MAAM;gCAAW,aAAa;4BAAE;4BACzC,MAAK;;;;;;;;;;;;8BAGb,8OAAC;8BACG,cAAA,8OAAC;wBAAS,IAAG;kCACT,cAAA,8OAAC;4BACG,OAAM;4BACN,QAAO;4BACP,MAAK;4BACL,OAAO;gCAAE,MAAM;gCAAS,aAAa;4BAAE;;;;;;;;;;;;;;;;;;;;;;IAM3D,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,OAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAK;YACL,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBAAE,UAAS;;sCACR,8OAAC;4BACG,IAAG;4BACH,OAAO;gCAAE,UAAU;4BAAQ;4BAC3B,WAAU;4BACV,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;sCAEP,cAAA,8OAAC;gCACG,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,MAAK;gCACL,OAAO;oCAAE,MAAM;oCAAS,aAAa;gCAAE;;;;;;;;;;;sCAG/C,8OAAC;4BAAE,MAAK;sCACJ,cAAA,8OAAC;gCACG,UAAS;gCACT,UAAS;gCACT,GAAE;gCACF,WAAW;gCACX,OAAO;oCAAE,aAAa;gCAAE;;;;;;;;;;;sCAGhC,8OAAC;4BAAK,IAAG;4BAA6B,MAAK;sCACvC,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;;8BAGhB,8OAAC;8BACG,cAAA,8OAAC;wBAAS,IAAG;kCACT,cAAA,8OAAC;4BACG,OAAM;4BACN,QAAO;4BACP,MAAK;4BACL,OAAO;gCAAE,MAAM;gCAAS,aAAa;4BAAE;;;;;;;;;;;;;;;;;;;;;;IAM3D,gBAAgB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC/C,8OAAC;YACG,OAAM;YACN,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;YAChE,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,OAAO;wBAAE,QAAQ;wBAAS,eAAe;oBAAE;oBAC3C,aAAY;;;;;;8BAEhB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,OAAO;wBAAE,QAAQ;wBAAS,eAAe;oBAAE;oBAC3C,aAAY;;;;;;8BAEhB,8OAAC;oBACG,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,IAAG;oBACH,QAAO;oBACP,OAAO;wBAAE,QAAQ;wBAAS,eAAe;oBAAE;oBAC3C,aAAY;;;;;;8BAEhB,8OAAC;oBACG,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,IAAG;oBACH,QAAO;oBACP,OAAO;wBAAE,QAAQ;wBAAS,eAAe;oBAAE;oBAC3C,aAAY;;;;;;8BAEhB,8OAAC;oBACG,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,IAAG;oBACH,QAAO;oBACP,OAAO;wBAAE,QAAQ;wBAAS,eAAe;oBAAE;oBAC3C,aAAY;;;;;;8BAEhB,8OAAC;oBACG,UAAS;oBACT,UAAS;oBACT,GAAE;oBACF,MAAK;oBACL,OAAO;wBAAE,MAAM;wBAAS,aAAa;oBAAE;;;;;;;;;;;;IAInD,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBAAK,GAAE;oBAAgB,MAAK;;;;;;;;;;;;IAGrC,SAAS,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACxC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;;sCACG,8OAAC;4BACG,eAAc;4BACd,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,cAAa;4BACb,mBAAkB;;8CAElB,8OAAC;oCAAK,QAAO;oCAAI,OAAO;wCAAE,WAAW;oCAAkB;;;;;;8CACvD,8OAAC;oCAAK,QAAO;oCAAQ,OAAO;wCAAE,WAAW;oCAAqB;;;;;;8CAC9D,8OAAC;oCAAK,QAAO;oCAAI,OAAO;wCAAE,WAAW;oCAAqB;;;;;;;;;;;;sCAE9D,8OAAC;4BACG,eAAc;4BACd,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,mBAAkB;;8CAElB,8OAAC;oCAAK,QAAO;oCAAI,OAAO;wCAAE,WAAW;oCAAkB;;;;;;8CACvD,8OAAC;oCAAK,QAAO;oCAAQ,OAAO;wCAAE,WAAW;oCAAqB;;;;;;8CAC9D,8OAAC;oCAAK,QAAO;oCAAI,OAAO;wCAAE,WAAW;oCAAqB;;;;;;;;;;;;sCAE9D,8OAAC;4BACG,eAAc;4BACd,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,mBAAkB;;8CAElB,8OAAC;oCAAK,QAAO;oCAAI,OAAO;wCAAE,WAAW;oCAAkB;;;;;;8CACvD,8OAAC;oCAAK,QAAO;oCAAQ,OAAO;wCAAE,WAAW;oCAAqB;;;;;;8CAC9D,8OAAC;oCAAK,QAAO;oCAAI,OAAO;wCAAE,WAAW;oCAAqB;;;;;;;;;;;;;;;;;;8BAGlE,8OAAC;oBACG,GAAE;oBACF,OAAO;wBAAE,aAAa;wBAAG,MAAM;oBAAqB;;;;;;8BAExD,8OAAC;oBACG,GAAE;oBACF,OAAO;wBAAE,MAAM;wBAAoB,aAAa;oBAAE;;;;;;8BAEtD,8OAAC;oBACG,GAAE;oBACF,OAAO;wBAAE,aAAa;wBAAG,MAAM;oBAAmB;;;;;;8BAEtD,8OAAC;oBACG,GAAE;oBACF,OAAO;wBAAE,aAAa;wBAAG,MAAM;oBAAmB;;;;;;;;;;;;IAI9D,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBAAE,UAAS;;sCACR,8OAAC;4BACG,IAAG;4BACH,OAAO;gCAAE,UAAU;4BAAQ;4BAC3B,WAAU;4BACV,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;sCAEP,cAAA,8OAAC;gCACG,UAAS;gCACT,UAAS;gCACT,GAAE;gCACF,MAAK;gCACL,OAAO;oCAAE,MAAM;oCAAS,aAAa;gCAAE;;;;;;;;;;;sCAG/C,8OAAC;4BAAE,MAAK;;8CACJ,8OAAC;oCACG,GAAE;oCACF,MAAK;oCACL,OAAO;wCAAE,MAAM;wCAA0C,aAAa;oCAAE;;;;;;8CAE5E,8OAAC;oCAAE,QAAO;8CACN,cAAA,8OAAC;wCACG,GAAE;wCACF,MAAK;wCACL,OAAO;4CACH,MAAM;4CACN,aAAa;wCACjB;;;;;;;;;;;8CAGR,8OAAC;oCAAE,QAAO;8CACN,cAAA,8OAAC;wCACG,GAAE;wCACF,MAAK;wCACL,OAAO;4CACH,MAAM;4CACN,aAAa;wCACjB;;;;;;;;;;;8CAGR,8OAAC;oCAAE,OAAO;wCAAE,cAAc;oCAAU;oCAAG,SAAQ;8CAC3C,cAAA,8OAAC;wCACG,UAAS;wCACT,UAAS;wCACT,GAAE;wCACF,MAAK;;;;;;;;;;;;;;;;;;;;;;;8BAKrB,8OAAC;;sCACG,8OAAC;4BACG,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,8OAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,8OAAC;oCACG,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAEX,8OAAC;;;;;8CACD,8OAAC;oCAAe,cAAa;;;;;;8CAC7B,8OAAC;oCACG,MAAK;oCACL,QAAO;;;;;;8CAEX,8OAAC;oCACG,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAEX,8OAAC;oCACG,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGf,8OAAC;4BACG,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,8OAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,8OAAC;oCACG,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAEX,8OAAC;;;;;8CACD,8OAAC;oCAAe,cAAa;;;;;;8CAC7B,8OAAC;oCACG,MAAK;oCACL,QAAO;;;;;;8CAEX,8OAAC;oCACG,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAEX,8OAAC;oCACG,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGf,8OAAC;4BACG,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;oCAAQ,OAAO;wCAAE,WAAW;wCAAS,aAAa;oCAAE;;;;;;8CACpE,8OAAC;oCACG,QAAO;oCACP,WAAU;oCACV,aAAY;oCACZ,OAAO;wCAAE,WAAW;wCAAQ,aAAa;oCAAE;;;;;;;;;;;;sCAGnD,8OAAC;4BAAS,IAAG;sCACT,cAAA,8OAAC;gCACG,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,OAAO;oCAAE,MAAM;oCAAS,aAAa;gCAAE;gCACvC,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAM9B,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBAAK,GAAE;oBAAuC,MAAK;;;;;;8BACpD,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBAAK,GAAE;oBAA+C,MAAK;;;;;;8BAC5D,8OAAC;oBAAK,GAAE;oBAAmD,MAAK;;;;;;8BAChE,8OAAC;;sCACG,8OAAC;4BACG,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE/B,8OAAC;4BACG,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;8CAClC,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;8CAClC,8OAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE/B,8OAAC;4BACG,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;8CAClC,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;8CAClC,8OAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE/B,8OAAC;4BACG,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;8CAC3B,8OAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE/B,8OAAC;4BACG,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE/B,8OAAC;4BACG,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;IAM3C,cAAc,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC7C,8OAAC;YACG,OAAM;YACN,OAAO;YACP,QAAQ;YACR,SAAQ;YACR,MAAK;YACL,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;;;;;;IAIjB,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC1C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,gBAAe;;;;;;8BAEnB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,gBAAe;;;;;;8BAEnB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,gBAAe;;;;;;8BAEnB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,gBAAe;;;;;;;;;;;;IAI3B,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC1C,8OAAC;YACG,OAAM;YACN,OAAO;YACP,QAAQ;YACR,SAAQ;YACR,MAAK;YACL,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;;;;;;;;;;;IAIrB,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC9C,8OAAC;YACG,OAAM;YACN,OAAO;YACP,QAAQ;YACR,SAAQ;YACR,MAAK;YACL,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,MAAK;gBACL,UAAS;gBACT,UAAS;;;;;;;;;;;IAIrB,oBAAoB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACnD,8OAAC;YACG,OAAO;YACP,QAAQ;YACR,SAAQ;YACR,MAAK;YACL,WAAW;YACV,GAAG,KAAK;YACT,OAAM;sBAEN,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,MAAK;gBACL,eAAc;gBACd,gBAAe;;;;;;;;;;;IAK3B,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,OAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAK;YACL,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,MAAK;gBACL,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;;;;;;IAId,QAAQ,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACvC,8OAAC;YACG,OAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAK;YACL,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,MAAK;gBACL,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;;;;;;IAId,QAAQ,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACvC,8OAAC;YACG,OAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAK;YACL,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;gBACL,OAAO;oBAAE,MAAM;oBAAS,aAAa;gBAAE;;;;;;;;;;;IAInD,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACzC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,IAAG;oBACH,QAAO;oBACP,aAAY;;;;;;8BAEhB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;;;;;;8BAElB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;;;;;;;;;;;;IAI1B,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACnC,8OAAC,mLAAA,CAAA,UAAM;YACH,WAAW;YACX,iBAAiB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,UAAU;gBAC1B,8CAA8C,CAAC,WAAW,SACtD;YAER;YACA,gBAAgB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,SAAS;gBACxB,wCAAwC,CAAC,WAAW,SAChD;YAER;YACC,GAAG,KAAK;;;;;;IAGjB,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACnC,8OAAC,mLAAA,CAAA,UAAM;YACH,WAAW;YACX,iBAAiB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,UAAU;gBAC1B,8CAA8C,CAAC,WAAW,SACtD;YAER;YACA,gBAAgB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,SAAS;gBACxB,wCAAwC,CAAC,WAAW,SAChD;YAER;YACC,GAAG,KAAK;;;;;;IAGjB,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACnC,8OAAC,mLAAA,CAAA,UAAM;YACH,WAAW;YACX,iBAAiB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,UAAU;gBAC1B,8CAA8C,CAAC,WAAW,SACtD;YAER;YACA,gBAAgB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,SAAS;gBACxB,wCAAwC,CAAC,WAAW,SAChD;YAER;YACC,GAAG,KAAK;;;;;;IAGjB,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACnC,8OAAC,mLAAA,CAAA,UAAM;YACH,WAAW;YACX,iBAAiB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACd;gBACI,8CAA8C,CAAC,WAAW,SACtD;YAER,GACA;YAEJ,gBAAgB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACb;gBACI,oDAAoD,CAAC,WAAW,SAC5D;YAER,GACA;gBACI,8CAA8C,WAAW,SACrD;YAER;YAEH,GAAG,KAAK;;;;;;IAGjB,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACnC,8OAAC,mLAAA,CAAA,UAAM;YACH,WAAW;YACX,iBAAiB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACd;gBACI,8CAA8C,CAAC,WAAW,SACtD;YAER,GACA;YAEJ,gBAAgB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACb;gBACI,oDAAoD,CAAC,WAAW,SAC5D;YAER,GACA;gBACI,8CAA8C,WAAW,SACrD;YAER;YAEH,GAAG,KAAK;;;;;;IAGjB,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACnC,8OAAC,mLAAA,CAAA,UAAM;YACH,WAAW;YACX,iBAAiB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACd;gBACI,8CAA8C,CAAC,WAAW,SACtD;YAER,GACA;YAEJ,gBAAgB,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACb;gBACI,sCAAsC,CAAC,WAAW,SAC9C;YAER,GACA;YAEH,GAAG,KAAK;;;;;;IAGjB,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC1C,8OAAC;YACG,OAAM;YACN,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACf,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBAAK,OAAM;gBAAK,QAAO;gBAAK,GAAE;gBAAI,GAAE;gBAAI,IAAG;;;;;;;;;;;IAGpD,QAAQ,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACvC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,UAAS;oBACT,UAAS;oBACT,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;;;;;;;IAIjB,SAAS,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACxC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;;;;;;;IAIjB,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACrC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,UAAS;oBACT,UAAS;oBACT,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;;;;;;;IAIjB,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACzC,8OAAC;YACG,OAAM;YACN,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACf,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBAAK,OAAM;gBAAK,QAAO;gBAAK,GAAE;gBAAI,GAAE;gBAAI,IAAG;;;;;;;;;;;IAGpD,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACzC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;8BAEnB,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;;;;;;;IAIjB,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACrC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBAAK,GAAE;oBAA8B,MAAK;;;;;;8BAC3C,8OAAC;oBACG,UAAS;oBACT,UAAS;oBACT,GAAE;oBACF,MAAK;;;;;;;;;;;;IAIjB,QAAQ,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACvC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC1C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,gBAAgB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC/C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;8BAEnB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;8BAEnB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;;;;;;;IAI3B,OAAO,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACtC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,MAAK;;;;;;;;;;;IAIjB,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACrC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;;;;;;;IAK3B,WAAW,gLAAA,CAAA,gBAAa;IACxB,WAAW,gLAAA,CAAA,gBAAa;IACxB,YAAY,gLAAA,CAAA,iBAAc;IAC1B,SAAS,gLAAA,CAAA,cAAW;IACpB,WAAW,gLAAA,CAAA,gBAAa;IACxB,yBAAyB,gLAAA,CAAA,8BAA2B;IACpD,YAAY,gLAAA,CAAA,iBAAc;IAC1B,UAAU,gLAAA,CAAA,eAAY;IACtB,uBAAuB,gLAAA,CAAA,4BAAyB;IAChD,aAAa,gLAAA,CAAA,kBAAe;IAE5B,WAAW,gLAAA,CAAA,gBAAa;IACxB,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,YAAY,gLAAA,CAAA,iBAAc;IAC1B,aAAa,gLAAA,CAAA,kBAAe;IAC5B,aAAa,gLAAA,CAAA,kBAAe;IAC5B,WAAW,gLAAA,CAAA,gBAAa;IACxB,KAAK,gLAAA,CAAA,UAAO;IACZ,QAAQ,gLAAA,CAAA,aAAU;IAClB,UAAU,gLAAA,CAAA,eAAY;IACtB,gBAAgB,gLAAA,CAAA,qBAAkB;IAElC,YAAY,gLAAA,CAAA,iBAAc;IAC1B,OAAO,gLAAA,CAAA,YAAS;IAChB,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,UAAU,gLAAA,CAAA,eAAY;IACtB,aAAa,gLAAA,CAAA,kBAAe;IAC5B,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,WAAW,gLAAA,CAAA,gBAAa;IACxB,iBAAiB,gLAAA,CAAA,sBAAmB;IACpC,WAAW,gLAAA,CAAA,gBAAa;IACxB,eAAe,gLAAA,CAAA,oBAAiB;IAChC,MAAM,gLAAA,CAAA,WAAQ;IACd,mBAAmB,gLAAA,CAAA,wBAAqB;IACxC,MAAM,gLAAA,CAAA,WAAQ;IACd,eAAe,gLAAA,CAAA,oBAAiB;IAChC,SAAS,gLAAA,CAAA,cAAW;IACpB,uBAAuB,gLAAA,CAAA,4BAAyB;IAChD,QAAQ,gLAAA,CAAA,aAAU;IAClB,QAAQ,gLAAA,CAAA,aAAU;IAClB,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,MAAM,gLAAA,CAAA,WAAQ;IACd,aAAa,gLAAA,CAAA,kBAAe;IAC5B,QAAQ,gLAAA,CAAA,aAAU;IAElB,SAAS,gLAAA,CAAA,cAAW;IACpB,aAAa,gLAAA,CAAA,kBAAe;IAC5B,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,gBAAgB,gLAAA,CAAA,qBAAkB;IAClC,UAAU,gLAAA,CAAA,eAAY;IACtB,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,gBAAgB,gLAAA,CAAA,sBAAmB;IAEnC,qBAAqB,gLAAA,CAAA,0BAAuB;IAC5C,MAAM,gLAAA,CAAA,WAAQ;IACd,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,SAAS,gLAAA,CAAA,cAAW;IACpB,WAAW,gLAAA,CAAA,gBAAa;IACxB,gBAAgB,gLAAA,CAAA,qBAAkB;IAElC,MAAM,gLAAA,CAAA,WAAQ;IACd,UAAU,gLAAA,CAAA,eAAY;IACtB,OAAO,gLAAA,CAAA,YAAS;IAEhB,MAAM,gLAAA,CAAA,WAAQ;IACd,YAAY,gLAAA,CAAA,iBAAc;IAC1B,OAAO,gLAAA,CAAA,YAAS;IAChB,OAAO,gLAAA,CAAA,YAAS;IAEhB,MAAM,gLAAA,CAAA,WAAQ;IAEd,OAAO,gLAAA,CAAA,YAAS;IAChB,OAAO,gLAAA,CAAA,YAAS;IAChB,aAAa,gLAAA,CAAA,kBAAe;IAE5B,UAAU,gLAAA,CAAA,eAAY;IAEtB,QAAQ,gLAAA,CAAA,aAAU;IAClB,MAAM,gLAAA,CAAA,YAAS;IACf,UAAU,gLAAA,CAAA,gBAAa;IACvB,YAAY,gLAAA,CAAA,iBAAc;IAC1B,UAAU,gLAAA,CAAA,gBAAa;IACvB,YAAY,gLAAA,CAAA,iBAAc;IAE1B,iBAAiB,gLAAA,CAAA,sBAAmB;IACpC,WAAW,gLAAA,CAAA,gBAAa;IACxB,OAAO,gLAAA,CAAA,YAAS;IAChB,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,QAAQ,gLAAA,CAAA,aAAU;IAClB,MAAM,gLAAA,CAAA,WAAQ;IACd,iBAAiB,gLAAA,CAAA,sBAAmB;IACpC,eAAe,gLAAA,CAAA,oBAAiB;IAEhC,QAAQ,gLAAA,CAAA,cAAW;IACnB,aAAa,gLAAA,CAAA,cAAW;IACxB,SAAS,gLAAA,CAAA,cAAW;IACpB,SAAS,gLAAA,CAAA,cAAW;IACpB,UAAU,gLAAA,CAAA,eAAY;IACtB,MAAM,gLAAA,CAAA,WAAQ;IACd,aAAa,gLAAA,CAAA,kBAAe;IAC5B,QAAQ,gLAAA,CAAA,aAAU;IAElB,qBAAqB,gLAAA,CAAA,0BAAuB;IAC5C,QAAQ,gLAAA,CAAA,aAAU;IAClB,OAAO,gLAAA,CAAA,YAAS;IAChB,YAAY,gLAAA,CAAA,iBAAc;IAE1B,UAAU,gLAAA,CAAA,eAAY;IACtB,SAAS,gLAAA,CAAA,cAAW;IACpB,QAAQ,gLAAA,CAAA,aAAU;IAClB,OAAO,gLAAA,CAAA,aAAU;IACjB,MAAM,gLAAA,CAAA,WAAQ;IACd,KAAK,gLAAA,CAAA,UAAO;IACZ,0BAA0B,gLAAA,CAAA,+BAA4B;IACtD,wBAAwB,gLAAA,CAAA,6BAA0B;IAClD,QAAQ,gLAAA,CAAA,aAAU;IAClB,YAAY,gLAAA,CAAA,iBAAc;IAE1B,OAAO,4MAAA,CAAA,YAAS;IAEhB,MAAM,gLAAA,CAAA,WAAQ;IACd,iBAAiB,gLAAA,CAAA,sBAAmB;IACpC,eAAe,gLAAA,CAAA,oBAAiB;IAChC,gBAAgB,gLAAA,CAAA,qBAAkB;IAClC,oBAAoB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACnD,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,OAAO,gLAAA,CAAA,YAAS;IAChB,QAAQ,gLAAA,CAAA,aAAU;IAClB,QAAQ,gLAAA,CAAA,aAAU;IAElB,OAAO,gLAAA,CAAA,YAAS;IAChB,UAAU,gLAAA,CAAA,eAAY;IACtB,gBAAgB,gLAAA,CAAA,qBAAkB;IAClC,cAAc,gLAAA,CAAA,mBAAgB;IAC9B,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBAAE,QAAO;;sCACN,8OAAC;4BACG,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,MAAK;4BACL,aAAY;4BACZ,gBAAe;;;;;;sCAEnB,8OAAC;4BACG,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,WAAU;4BACV,aAAY;4BACZ,iBAAgB;4BAChB,gBAAe;;;;;;;;;;;;8BAGvB,8OAAC;oBAAE,QAAO;8BACN,cAAA,8OAAC;wBACG,GAAE;wBACF,WAAU;wBACV,gBAAe;;;;;;;;;;;8BAGvB,8OAAC;;sCACG,8OAAC;4BACG,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCACG,QAAO;oCACP,WAAU;;;;;;;;;;;;sCAGlB,8OAAC;4BACG,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,8OAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,8OAAC;oCACG,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAEX,8OAAC;oCAAS,IAAG;;;;;;8CACb,8OAAC;oCAAe,cAAa;;;;;;8CAC7B,8OAAC;oCAAY,KAAI;oCAAY,UAAS;;;;;;8CACtC,8OAAC;oCACG,MAAK;oCACL,QAAO;;;;;;8CAEX,8OAAC;oCACG,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAEX,8OAAC;oCACG,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGf,8OAAC;4BACG,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,8OAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,8OAAC;oCACG,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAEX,8OAAC;oCAAS,IAAG;;;;;;8CACb,8OAAC;oCAAe,cAAa;;;;;;8CAC7B,8OAAC;oCAAY,KAAI;oCAAY,UAAS;;;;;;8CACtC,8OAAC;oCACG,MAAK;oCACL,QAAO;;;;;;8CAEX,8OAAC;oCACG,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAEX,8OAAC;oCACG,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;;;;;;;;;;;;;IAM3B,iBAAiB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAChD,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;;;;;;IAIjB,OAAO,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACtC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,QAAQ,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACvC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,SAAS,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACxC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;8BAEnB,8OAAC;oBAAK,GAAE;oBAAa,QAAO;oBAAe,aAAY;oBAAM,eAAc;;;;;;8BAC3E,8OAAC;oBAAK,GAAE;oBAAc,QAAO;oBAAe,aAAY;oBAAM,eAAc;;;;;;8BAC5E,8OAAC;oBAAK,GAAE;oBAAc,QAAO;oBAAe,aAAY;oBAAM,eAAc;;;;;;8BAC5E,8OAAC;oBAAK,GAAE;oBAAa,QAAO;oBAAe,aAAY;oBAAM,eAAc;;;;;;;;;;;;IAGnF,QAAQ,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACvC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;8BAEnB,8OAAC;oBAAK,GAAE;oBAAc,QAAO;oBAAe,aAAY;oBAAM,eAAc;;;;;;8BAC5E,8OAAC;oBAAK,GAAE;oBAAe,QAAO;oBAAe,aAAY;oBAAM,eAAc;;;;;;8BAC7E,8OAAC;oBAAK,GAAE;oBAAe,QAAO;oBAAe,aAAY;oBAAM,eAAc;;;;;;8BAC7E,8OAAC;oBAAK,GAAE;oBAAgB,QAAO;oBAAe,aAAY;oBAAM,eAAc;;;;;;;;;;;;IAGtF,cAAc,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC7C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,QAAQ,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACvC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBAAK,GAAE;oBAA2B,QAAO;oBAAe,eAAc;;;;;;8BACvE,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;;;;;;;IAIjB,aAAa,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC5C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;;;;;;8BAElB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;;;;;;8BAElB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;;;;;;;;;;;;IAI1B,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,OAAO;wBACH,aAAa;oBACjB;;;;;;8BAEJ,8OAAC;oBACG,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,OAAO;wBACH,aAAa;oBACjB;;;;;;8BAEJ,8OAAC;oBACG,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,OAAO;wBACH,aAAa;oBACjB;;;;;;;;;;;;IAKZ,iBAAiB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAChD,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACzC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC1C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,SAAS,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACxC,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,gBAAgB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC/C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,mBAAmB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAClD,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,kBAAkB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACjD,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,aAAa,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC5C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,UAAS;oBACT,UAAS;oBACT,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;;;;;;;IAIjB,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC9C,8OAAC;YACG,OAAM;YACN,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE;YACb,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAK3B,cAAc,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC7C,8OAAC;YACG,OAAM;YACN,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE;YACb,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;8BAEnB,8OAAC;oBACG,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;;;;;;;;;;;;IAK1B,mBAAmB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAClD,8OAAC;YACG,OAAM;YACN,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE;YACb,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,oBAAoB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBACnD,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE;YACb,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,MAAK;;;;;;;;;;;IAIjB,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE;YACb,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACG,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;;;;;;;;;;IAI3B,iBAAiB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAChD,8OAAC;YACG,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE;YACb,GAAG,KAAK;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;8BAET,8OAAC;oBACG,GAAE;oBACF,MAAK;;;;;;;;;;;;IAIjB,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC3C,8OAAC;YACG,SAAQ;YACR,OAAM;YACN,MAAK;YACL,QAAO;YACP,eAAc;YACd,gBAAe;YACf,aAAY;YACZ,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE;YACb,GAAG,KAAK;;8BAET,8OAAC;oBAAQ,QAAO;;;;;;8BAChB,8OAAC;oBAAS,QAAO;;;;;;;;;;;;IAGzB,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAAkB,iBAC9C,8OAAC;YACG,SAAQ;YACR,OAAM;YACN,MAAK;YACL,QAAO;YACP,eAAc;YACd,gBAAe;YACf,aAAY;YACZ,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE;YACb,GAAG,KAAK;;8BAET,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAK,GAAE;;;;;;;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 3652, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/tooltip.tsx"], "sourcesContent": ["import * as TooltipPrimitive from '@radix-ui/react-tooltip';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction TooltipProvider({\n    delayDuration = 0,\n    ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n    return (\n        <TooltipPrimitive.Provider\n            data-slot=\"tooltip-provider\"\n            delayDuration={delayDuration}\n            {...props}\n        />\n    );\n}\n\nfunction Tooltip({ ...props }: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n    return (\n        <TooltipProvider>\n            <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n        </TooltipProvider>\n    );\n}\n\nfunction TooltipTrigger({ ...props }: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n    return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />;\n}\n\nfunction TooltipContent({\n    className,\n    sideOffset = 0,\n    children,\n    hideArrow = false,\n    ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content> & { hideArrow?: boolean }) {\n    return (\n        <TooltipPrimitive.Portal>\n            <TooltipPrimitive.Content\n                data-slot=\"tooltip-content\"\n                sideOffset={sideOffset}\n                className={cn(\n                    'bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance',\n                    className,\n                )}\n                {...props}\n            >\n                {children}\n                {!hideArrow && (\n                    <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n                )}\n            </TooltipPrimitive.Content>\n        </TooltipPrimitive.Portal>\n    );\n}\n\nconst TooltipPortal = TooltipPrimitive.Portal;\n\nexport { Tooltip, TooltipContent, TooltipPortal, TooltipProvider, TooltipTrigger };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAGA;AAAA;;;;AAEA,SAAS,gBAAgB,EACrB,gBAAgB,CAAC,EACjB,GAAG,OACkD;IACrD,qBACI,8OAAC,mKAAA,CAAA,WAAyB;QACtB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC7E,qBACI,8OAAC;kBACG,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAGhE;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACvF,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AAC1E;AAEA,SAAS,eAAe,EACpB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,YAAY,KAAK,EACjB,GAAG,OAC2E;IAC9E,qBACI,8OAAC,mKAAA,CAAA,SAAuB;kBACpB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACrB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,0aACA;YAEH,GAAG,KAAK;;gBAER;gBACA,CAAC,2BACE,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKtD;AAEA,MAAM,gBAAgB,mKAAA,CAAA,SAAuB", "debugId": null}}, {"offset": {"line": 3739, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/kbd.tsx"], "sourcesContent": ["import type React from 'react';\nimport { cn } from '../utils';\n\nexport function Kbd({ children, className }: { children: React.ReactNode; className?: string }) {\n    return (\n        <kbd\n            className={cn(\n                'pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded-sm border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100',\n                className,\n            )}\n        >\n            {children}\n        </kbd>\n    );\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;;;AAEO,SAAS,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAqD;IAC1F,qBACI,8OAAC;QACG,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,4KACA;kBAGH;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 3763, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/hotkey-label.tsx"], "sourcesContent": ["import { cn } from '../utils';\nimport { Kbd } from './kbd';\n\nexport type Hotkey = {\n    command: string;\n    description: string;\n    readableCommand: string;\n};\n\nexport function HotkeyLabel({ hotkey, className }: { hotkey: Hotkey; className?: string }) {\n    return (\n        <span className={cn('flex items-center space-x-2', className)}>\n            <span>{hotkey.description}</span>\n\n            <Kbd>\n                <span\n                    className=\"inline-grid grid-flow-col auto-cols-max gap-1.5 items-center text-xs [&_kbd]:text-[1.1em]\"\n                    dangerouslySetInnerHTML={{ __html: hotkey.readableCommand }}\n                />\n            </Kbd>\n        </span>\n    );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;;AAQO,SAAS,YAAY,EAAE,MAAM,EAAE,SAAS,EAA0C;IACrF,qBACI,8OAAC;QAAK,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;0BAC/C,8OAAC;0BAAM,OAAO,WAAW;;;;;;0BAEzB,8OAAC,2IAAA,CAAA,MAAG;0BACA,cAAA,8OAAC;oBACG,WAAU;oBACV,yBAAyB;wBAAE,QAAQ,OAAO,eAAe;oBAAC;;;;;;;;;;;;;;;;;AAK9E", "debugId": null}}, {"offset": {"line": 3813, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/toggle.tsx"], "sourcesContent": ["import * as TogglePrimitive from '@radix-ui/react-toggle';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nconst toggleVariants = cva(\n    \"inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap\",\n    {\n        variants: {\n            variant: {\n                default: 'bg-transparent',\n                outline:\n                    'border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground',\n            },\n            size: {\n                default: 'h-9 px-2 min-w-9',\n                sm: 'h-8 px-1.5 min-w-8',\n                lg: 'h-10 px-2.5 min-w-10',\n            },\n        },\n        defaultVariants: {\n            variant: 'default',\n            size: 'default',\n        },\n    },\n);\n\nfunction Toggle({\n    className,\n    variant,\n    size,\n    ...props\n}: React.ComponentProps<typeof TogglePrimitive.Root> & VariantProps<typeof toggleVariants>) {\n    return (\n        <TogglePrimitive.Root\n            data-slot=\"toggle\"\n            className={cn(toggleVariants({ variant, size, className }))}\n            {...props}\n        />\n    );\n}\n\nexport { Toggle, toggleVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;AAAA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,ijBACA;IACI,UAAU;QACN,SAAS;YACL,SAAS;YACT,SACI;QACR;QACA,MAAM;YACF,SAAS;YACT,IAAI;YACJ,IAAI;QACR;IACJ;IACA,iBAAiB;QACb,SAAS;QACT,MAAM;IACV;AACJ;AAGJ,SAAS,OAAO,EACZ,SAAS,EACT,OAAO,EACP,IAAI,EACJ,GAAG,OACmF;IACtF,qBACI,8OAAC,kKAAA,CAAA,OAAoB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 3865, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/toggle-group.tsx"], "sourcesContent": ["'use client';\n\nimport * as ToggleGroupPrimitive from '@radix-ui/react-toggle-group';\nimport { type VariantProps } from 'class-variance-authority';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\nimport { toggleVariants } from './toggle';\n\nconst ToggleGroupContext = React.createContext<VariantProps<typeof toggleVariants>>({\n    size: 'default',\n    variant: 'default',\n});\n\nfunction ToggleGroup({\n    className,\n    variant,\n    size,\n    children,\n    ...props\n}: React.ComponentProps<typeof ToggleGroupPrimitive.Root> & VariantProps<typeof toggleVariants>) {\n    return (\n        <ToggleGroupPrimitive.Root\n            data-slot=\"toggle-group\"\n            data-variant={variant}\n            data-size={size}\n            className={cn(\n                'group/toggle-group flex w-fit items-center rounded-md data-[variant=outline]:shadow-xs',\n                className,\n            )}\n            {...props}\n        >\n            <ToggleGroupContext.Provider value={{ variant, size }}>\n                {children}\n            </ToggleGroupContext.Provider>\n        </ToggleGroupPrimitive.Root>\n    );\n}\n\nfunction ToggleGroupItem({\n    className,\n    children,\n    variant,\n    size,\n    ...props\n}: React.ComponentProps<typeof ToggleGroupPrimitive.Item> & VariantProps<typeof toggleVariants>) {\n    const context = React.useContext(ToggleGroupContext);\n\n    return (\n        <ToggleGroupPrimitive.Item\n            data-slot=\"toggle-group-item\"\n            data-variant={context.variant || variant}\n            data-size={context.size || size}\n            className={cn(\n                toggleVariants({\n                    variant: context.variant || variant,\n                    size: context.size || size,\n                }),\n                'min-w-0 flex-1 shrink-0 rounded-none shadow-none first:rounded-l-md last:rounded-r-md focus:z-10 focus-visible:z-10 data-[variant=outline]:border-l-0 data-[variant=outline]:first:border-l',\n                className,\n            )}\n            {...props}\n        >\n            {children}\n        </ToggleGroupPrimitive.Item>\n    );\n}\n\nexport { ToggleGroup, ToggleGroupItem };\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAEA;AAAA;AACA;AAPA;;;;;;AASA,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAuC;IAChF,MAAM;IACN,SAAS;AACb;AAEA,SAAS,YAAY,EACjB,SAAS,EACT,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,GAAG,OACwF;IAC3F,qBACI,8OAAC,2KAAA,CAAA,OAAyB;QACtB,aAAU;QACV,gBAAc;QACd,aAAW;QACX,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,0FACA;QAEH,GAAG,KAAK;kBAET,cAAA,8OAAC,mBAAmB,QAAQ;YAAC,OAAO;gBAAE;gBAAS;YAAK;sBAC/C;;;;;;;;;;;AAIjB;AAEA,SAAS,gBAAgB,EACrB,SAAS,EACT,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,GAAG,OACwF;IAC3F,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,qBACI,8OAAC,2KAAA,CAAA,OAAyB;QACtB,aAAU;QACV,gBAAc,QAAQ,OAAO,IAAI;QACjC,aAAW,QAAQ,IAAI,IAAI;QAC3B,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD,EAAE;YACX,SAAS,QAAQ,OAAO,IAAI;YAC5B,MAAM,QAAQ,IAAI,IAAI;QAC1B,IACA,+LACA;QAEH,GAAG,KAAK;kBAER;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 3934, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/tabs.tsx"], "sourcesContent": ["'use client';\n\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction Tabs({ className, ...props }: React.ComponentProps<typeof TabsPrimitive.Root>) {\n    return (\n        <TabsPrimitive.Root\n            data-slot=\"tabs\"\n            className={cn('flex flex-col gap-2', className)}\n            {...props}\n        />\n    );\n}\n\nfunction TabsList({ className, ...props }: React.ComponentProps<typeof TabsPrimitive.List>) {\n    return (\n        <TabsPrimitive.List\n            data-slot=\"tabs-list\"\n            className={cn(\n                'bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-tl-lg p-[3px]',\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nfunction TabsTrigger({ className, ...props }: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n    return (\n        <TabsPrimitive.Trigger\n            data-slot=\"tabs-trigger\"\n            className={cn(\n                \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:bg-none text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nfunction TabsContent({ className, ...props }: React.ComponentProps<typeof TabsPrimitive.Content>) {\n    return (\n        <TabsPrimitive.Content\n            data-slot=\"tabs-content\"\n            className={cn('flex-1 outline-none', className)}\n            {...props}\n        />\n    );\n}\n\nexport { Tabs, TabsContent, TabsList, TabsTrigger };\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAAA;AALA;;;;AAOA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAwD;IAClF,qBACI,8OAAC,gKAAA,CAAA,OAAkB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAwD;IACtF,qBACI,8OAAC,gKAAA,CAAA,OAAkB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,0GACA;QAEH,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC5F,qBACI,8OAAC,gKAAA,CAAA,UAAqB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,ynBACA;QAEH,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC5F,qBACI,8OAAC,gKAAA,CAAA,UAAqB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 3999, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/context-menu.tsx"], "sourcesContent": ["'use client';\n\nimport * as ContextMenuPrimitive from '@radix-ui/react-context-menu';\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from 'lucide-react';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction ContextMenu({ ...props }: React.ComponentProps<typeof ContextMenuPrimitive.Root>) {\n    return <ContextMenuPrimitive.Root data-slot=\"context-menu\" {...props} />;\n}\n\nfunction ContextMenuTrigger({\n    ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Trigger>) {\n    return <ContextMenuPrimitive.Trigger data-slot=\"context-menu-trigger\" {...props} />;\n}\n\nfunction ContextMenuGroup({ ...props }: React.ComponentProps<typeof ContextMenuPrimitive.Group>) {\n    return <ContextMenuPrimitive.Group data-slot=\"context-menu-group\" {...props} />;\n}\n\nfunction ContextMenuPortal({ ...props }: React.ComponentProps<typeof ContextMenuPrimitive.Portal>) {\n    return <ContextMenuPrimitive.Portal data-slot=\"context-menu-portal\" {...props} />;\n}\n\nfunction ContextMenuSub({ ...props }: React.ComponentProps<typeof ContextMenuPrimitive.Sub>) {\n    return <ContextMenuPrimitive.Sub data-slot=\"context-menu-sub\" {...props} />;\n}\n\nfunction ContextMenuRadioGroup({\n    ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.RadioGroup>) {\n    return <ContextMenuPrimitive.RadioGroup data-slot=\"context-menu-radio-group\" {...props} />;\n}\n\nfunction ContextMenuSubTrigger({\n    className,\n    inset,\n    children,\n    ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.SubTrigger> & {\n    inset?: boolean;\n}) {\n    return (\n        <ContextMenuPrimitive.SubTrigger\n            data-slot=\"context-menu-sub-trigger\"\n            data-inset={inset}\n            className={cn(\n                \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n                className,\n            )}\n            {...props}\n        >\n            {children}\n            <ChevronRightIcon className=\"ml-auto\" />\n        </ContextMenuPrimitive.SubTrigger>\n    );\n}\n\nfunction ContextMenuSubContent({\n    className,\n    ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.SubContent>) {\n    return (\n        <ContextMenuPrimitive.SubContent\n            data-slot=\"context-menu-sub-content\"\n            className={cn(\n                'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-context-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg',\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nfunction ContextMenuContent({\n    className,\n    ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Content>) {\n    return (\n        <ContextMenuPrimitive.Portal>\n            <ContextMenuPrimitive.Content\n                data-slot=\"context-menu-content\"\n                className={cn(\n                    'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-context-menu-content-available-height) min-w-[8rem] origin-(--radix-context-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md',\n                    className,\n                )}\n                {...props}\n            />\n        </ContextMenuPrimitive.Portal>\n    );\n}\n\nfunction ContextMenuItem({\n    className,\n    inset,\n    variant = 'default',\n    ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Item> & {\n    inset?: boolean;\n    variant?: 'default' | 'destructive';\n}) {\n    return (\n        <ContextMenuPrimitive.Item\n            data-slot=\"context-menu-item\"\n            data-inset={inset}\n            data-variant={variant}\n            className={cn(\n                \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nfunction ContextMenuCheckboxItem({\n    className,\n    children,\n    checked,\n    ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.CheckboxItem>) {\n    return (\n        <ContextMenuPrimitive.CheckboxItem\n            data-slot=\"context-menu-checkbox-item\"\n            className={cn(\n                \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n                className,\n            )}\n            checked={checked}\n            {...props}\n        >\n            <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n                <ContextMenuPrimitive.ItemIndicator>\n                    <CheckIcon className=\"size-4\" />\n                </ContextMenuPrimitive.ItemIndicator>\n            </span>\n            {children}\n        </ContextMenuPrimitive.CheckboxItem>\n    );\n}\n\nfunction ContextMenuRadioItem({\n    className,\n    children,\n    ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.RadioItem>) {\n    return (\n        <ContextMenuPrimitive.RadioItem\n            data-slot=\"context-menu-radio-item\"\n            className={cn(\n                \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n                className,\n            )}\n            {...props}\n        >\n            <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n                <ContextMenuPrimitive.ItemIndicator>\n                    <CircleIcon className=\"size-2 fill-current\" />\n                </ContextMenuPrimitive.ItemIndicator>\n            </span>\n            {children}\n        </ContextMenuPrimitive.RadioItem>\n    );\n}\n\nfunction ContextMenuLabel({\n    className,\n    inset,\n    ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Label> & {\n    inset?: boolean;\n}) {\n    return (\n        <ContextMenuPrimitive.Label\n            data-slot=\"context-menu-label\"\n            data-inset={inset}\n            className={cn(\n                'text-foreground px-2 py-1.5 text-sm font-medium data-[inset]:pl-8',\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nfunction ContextMenuSeparator({\n    className,\n    ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Separator>) {\n    return (\n        <ContextMenuPrimitive.Separator\n            data-slot=\"context-menu-separator\"\n            className={cn('bg-border -mx-1 my-1 h-px', className)}\n            {...props}\n        />\n    );\n}\n\nfunction ContextMenuShortcut({ className, ...props }: React.ComponentProps<'span'>) {\n    return (\n        <span\n            data-slot=\"context-menu-shortcut\"\n            className={cn('text-muted-foreground ml-auto text-xs tracking-widest', className)}\n            {...props}\n        />\n    );\n}\n\nexport {\n    ContextMenu,\n    ContextMenuCheckboxItem,\n    ContextMenuContent,\n    ContextMenuGroup,\n    ContextMenuItem,\n    ContextMenuLabel,\n    ContextMenuPortal,\n    ContextMenuRadioGroup,\n    ContextMenuRadioItem,\n    ContextMenuSeparator,\n    ContextMenuShortcut,\n    ContextMenuSub,\n    ContextMenuSubContent,\n    ContextMenuSubTrigger,\n    ContextMenuTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AAAA;AAAA;AAGA;AAAA;AANA;;;;;AAQA,SAAS,YAAY,EAAE,GAAG,OAA+D;IACrF,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EACxB,GAAG,OACqD;IACxD,qBAAO,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AACnF;AAEA,SAAS,iBAAiB,EAAE,GAAG,OAAgE;IAC3F,qBAAO,8OAAC,2KAAA,CAAA,QAA0B;QAAC,aAAU;QAAsB,GAAG,KAAK;;;;;;AAC/E;AAEA,SAAS,kBAAkB,EAAE,GAAG,OAAiE;IAC7F,qBAAO,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AACjF;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACvF,qBAAO,8OAAC,2KAAA,CAAA,MAAwB;QAAC,aAAU;QAAoB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,sBAAsB,EAC3B,GAAG,OACwD;IAC3D,qBAAO,8OAAC,2KAAA,CAAA,aAA+B;QAAC,aAAU;QAA4B,GAAG,KAAK;;;;;;AAC1F;AAEA,SAAS,sBAAsB,EAC3B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGN;IACG,qBACI,8OAAC,2KAAA,CAAA,aAA+B;QAC5B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,oTACA;QAEH,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGxC;AAEA,SAAS,sBAAsB,EAC3B,SAAS,EACT,GAAG,OACwD;IAC3D,qBACI,8OAAC,2KAAA,CAAA,aAA+B;QAC5B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,gfACA;QAEH,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,mBAAmB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACI,8OAAC,2KAAA,CAAA,SAA2B;kBACxB,cAAA,8OAAC,2KAAA,CAAA,UAA4B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,wjBACA;YAEH,GAAG,KAAK;;;;;;;;;;;AAIzB;AAEA,SAAS,gBAAgB,EACrB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIN;IACG,qBACI,8OAAC,2KAAA,CAAA,OAAyB;QACtB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,+mBACA;QAEH,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,wBAAwB,EAC7B,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC0D;IAC7D,qBACI,8OAAC,2KAAA,CAAA,eAAiC;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,gTACA;QAEJ,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACZ,cAAA,8OAAC,2KAAA,CAAA,gBAAkC;8BAC/B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAG5B;;;;;;;AAGb;AAEA,SAAS,qBAAqB,EAC1B,SAAS,EACT,QAAQ,EACR,GAAG,OACuD;IAC1D,qBACI,8OAAC,2KAAA,CAAA,YAA8B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,gTACA;QAEH,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACZ,cAAA,8OAAC,2KAAA,CAAA,gBAAkC;8BAC/B,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAG7B;;;;;;;AAGb;AAEA,SAAS,iBAAiB,EACtB,SAAS,EACT,KAAK,EACL,GAAG,OAGN;IACG,qBACI,8OAAC,2KAAA,CAAA,QAA0B;QACvB,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,qEACA;QAEH,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,qBAAqB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACI,8OAAC,2KAAA,CAAA,YAA8B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,oBAAoB,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC9E,qBACI,8OAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 4261, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/button.tsx"], "sourcesContent": ["import { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nconst buttonVariants = cva(\n    \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:outline-none focus:outline-none aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n    {\n        variants: {\n            variant: {\n                default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n                destructive:\n                    'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n                outline:\n                    'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\n                secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n                ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n                link: 'text-primary underline-offset-4 hover:underline',\n            },\n            size: {\n                default: 'h-9 px-3 py-2 has-[>svg]:px-3',\n                sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\n                lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n                icon: 'size-9',\n                toolbar: 'h-8 min-w-[28px] px-1.5 py-1.5 rounded-md',\n            },\n        },\n        defaultVariants: {\n            variant: 'default',\n            size: 'default',\n        },\n    },\n);\n\nfunction Button({\n    className,\n    variant,\n    size,\n    asChild = false,\n    ...props\n}: React.ComponentProps<'button'> &\n    VariantProps<typeof buttonVariants> & {\n        asChild?: boolean;\n    }) {\n    const Comp = asChild ? Slot : 'button';\n\n    return (\n        <Comp\n            data-slot=\"button\"\n            className={cn(buttonVariants({ variant, size, className }))}\n            {...props}\n        />\n    );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;AAAA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,+ZACA;IACI,UAAU;QACN,SAAS;YACL,SAAS;YACT,aACI;YACJ,SACI;YACJ,WAAW;YACX,OAAO;YACP,MAAM;QACV;QACA,MAAM;YACF,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,SAAS;QACb;IACJ;IACA,iBAAiB;QACb,SAAS;QACT,MAAM;IACV;AACJ;AAGJ,SAAS,OAAO,EACZ,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACI,8OAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 4320, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/penpal/src/child.ts"], "sourcesContent": ["import type { PenpalChildMethods as PenpalChildMethodsType } from '@onlook/web-preload/script/api';\n\n// Preload methods should be treated as promises\nexport type PromisifiedPendpalChildMethods = {\n    [K in keyof PenpalChildMethods]: (\n        ...args: Parameters<PenpalChildMethods[K]>\n    ) => Promise<ReturnType<PenpalChildMethods[K]>>;\n};\n\nexport type PenpalChildMethods = PenpalChildMethodsType;\n\nexport const PENPAL_CHILD_CHANNEL = 'PENPAL_CHILD';\n"], "names": [], "mappings": ";;;AAWO,MAAM,uBAAuB", "debugId": null}}, {"offset": {"line": 4330, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/penpal/src/parent.ts"], "sourcesContent": ["export type PenpalParentMethods = {\n    getFrameId: () => string;\n};\n\n// Parent methods should be treated as promises\nexport type PromisifiedPenpalParentMethods = {\n    [K in keyof PenpalParentMethods]: (\n        ...args: Parameters<PenpalParentMethods[K]>\n    ) => Promise<ReturnType<PenpalParentMethods[K]>>;\n};\n\nexport const PENPAL_PARENT_CHANNEL = 'PENPAL_PARENT';\n"], "names": [], "mappings": ";;;AAWO,MAAM,wBAAwB", "debugId": null}}, {"offset": {"line": 4340, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/penpal/src/utils.ts"], "sourcesContent": ["export const promisifyMethod = <T extends (...args: any[]) => any>(\n    method: T | undefined,\n): ((...args: Parameters<T>) => Promise<ReturnType<T>>) => {\n    return async (...args: Parameters<T>) => {\n        if (!method) throw new Error('Method not initialized');\n        return method(...args);\n    };\n};\n"], "names": [], "mappings": ";;;AAAO,MAAM,kBAAkB,CAC3B;IAEA,OAAO,OAAO,GAAG;QACb,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;QAC7B,OAAO,UAAU;IACrB;AACJ", "debugId": null}}, {"offset": {"line": 4355, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/penpal/src/index.ts"], "sourcesContent": ["export * from './child';\nexport * from './parent';\nexport * from './utils';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 4379, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/dropdown-menu.tsx"], "sourcesContent": ["'use client';\n\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from 'lucide-react';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction DropdownMenu({ ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n    return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />;\n}\n\nfunction DropdownMenuPortal({\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n    return <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />;\n}\n\nfunction DropdownMenuTrigger({\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n    return <DropdownMenuPrimitive.Trigger data-slot=\"dropdown-menu-trigger\" {...props} />;\n}\n\nfunction DropdownMenuContent({\n    className,\n    sideOffset = 4,\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n    return (\n        <DropdownMenuPrimitive.Portal>\n            <DropdownMenuPrimitive.Content\n                data-slot=\"dropdown-menu-content\"\n                sideOffset={sideOffset}\n                className={cn(\n                    'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md',\n                    className,\n                )}\n                {...props}\n            />\n        </DropdownMenuPrimitive.Portal>\n    );\n}\n\nfunction DropdownMenuGroup({ ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n    return <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />;\n}\n\nfunction DropdownMenuItem({\n    className,\n    inset,\n    variant = 'default',\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean;\n    variant?: 'default' | 'destructive';\n}) {\n    return (\n        <DropdownMenuPrimitive.Item\n            data-slot=\"dropdown-menu-item\"\n            data-inset={inset}\n            data-variant={variant}\n            className={cn(\n                \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nfunction DropdownMenuCheckboxItem({\n    className,\n    children,\n    checked,\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n    return (\n        <DropdownMenuPrimitive.CheckboxItem\n            data-slot=\"dropdown-menu-checkbox-item\"\n            className={cn(\n                \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n                className,\n            )}\n            checked={checked}\n            {...props}\n        >\n            <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n                <DropdownMenuPrimitive.ItemIndicator>\n                    <CheckIcon className=\"size-4\" />\n                </DropdownMenuPrimitive.ItemIndicator>\n            </span>\n            {children}\n        </DropdownMenuPrimitive.CheckboxItem>\n    );\n}\n\nfunction DropdownMenuRadioGroup({\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n    return <DropdownMenuPrimitive.RadioGroup data-slot=\"dropdown-menu-radio-group\" {...props} />;\n}\n\nfunction DropdownMenuRadioItem({\n    className,\n    children,\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n    return (\n        <DropdownMenuPrimitive.RadioItem\n            data-slot=\"dropdown-menu-radio-item\"\n            className={cn(\n                \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n                className,\n            )}\n            {...props}\n        >\n            <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n                <DropdownMenuPrimitive.ItemIndicator>\n                    <CircleIcon className=\"size-2 fill-current\" />\n                </DropdownMenuPrimitive.ItemIndicator>\n            </span>\n            {children}\n        </DropdownMenuPrimitive.RadioItem>\n    );\n}\n\nfunction DropdownMenuLabel({\n    className,\n    inset,\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean;\n}) {\n    return (\n        <DropdownMenuPrimitive.Label\n            data-slot=\"dropdown-menu-label\"\n            data-inset={inset}\n            className={cn('px-2 py-1.5 text-sm font-medium data-[inset]:pl-8', className)}\n            {...props}\n        />\n    );\n}\n\nfunction DropdownMenuSeparator({\n    className,\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n    return (\n        <DropdownMenuPrimitive.Separator\n            data-slot=\"dropdown-menu-separator\"\n            className={cn('bg-border -mx-1 my-1 h-px', className)}\n            {...props}\n        />\n    );\n}\n\nfunction DropdownMenuShortcut({ className, ...props }: React.ComponentProps<'span'>) {\n    return (\n        <span\n            data-slot=\"dropdown-menu-shortcut\"\n            className={cn('text-muted-foreground ml-auto text-xs tracking-widest', className)}\n            {...props}\n        />\n    );\n}\n\nfunction DropdownMenuSub({ ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n    return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />;\n}\n\nfunction DropdownMenuSubTrigger({\n    className,\n    inset,\n    children,\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean;\n}) {\n    return (\n        <DropdownMenuPrimitive.SubTrigger\n            data-slot=\"dropdown-menu-sub-trigger\"\n            data-inset={inset}\n            className={cn(\n                'focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8',\n                className,\n            )}\n            {...props}\n        >\n            {children}\n            <ChevronRightIcon className=\"ml-auto size-4\" />\n        </DropdownMenuPrimitive.SubTrigger>\n    );\n}\n\nfunction DropdownMenuSubContent({\n    className,\n    ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n    return (\n        <DropdownMenuPrimitive.SubContent\n            data-slot=\"dropdown-menu-sub-content\"\n            className={cn(\n                'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg',\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nexport {\n    DropdownMenu,\n    DropdownMenuCheckboxItem,\n    DropdownMenuContent,\n    DropdownMenuGroup,\n    DropdownMenuItem,\n    DropdownMenuLabel,\n    DropdownMenuPortal,\n    DropdownMenuRadioGroup,\n    DropdownMenuRadioItem,\n    DropdownMenuSeparator,\n    DropdownMenuShortcut,\n    DropdownMenuSub,\n    DropdownMenuSubContent,\n    DropdownMenuSubTrigger,\n    DropdownMenuTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AAAA;AAAA;AAGA;AAAA;AANA;;;;;AAQA,SAAS,aAAa,EAAE,GAAG,OAAgE;IACvF,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AAC1E;AAEA,SAAS,mBAAmB,EACxB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AACnF;AAEA,SAAS,oBAAoB,EACzB,GAAG,OACsD;IACzD,qBAAO,8OAAC,4KAAA,CAAA,UAA6B;QAAC,aAAU;QAAyB,GAAG,KAAK;;;;;;AACrF;AAEA,SAAS,oBAAoB,EACzB,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACsD;IACzD,qBACI,8OAAC,4KAAA,CAAA,SAA4B;kBACzB,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC1B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,0jBACA;YAEH,GAAG,KAAK;;;;;;;;;;;AAIzB;AAEA,SAAS,kBAAkB,EAAE,GAAG,OAAiE;IAC7F,qBAAO,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AACjF;AAEA,SAAS,iBAAiB,EACtB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIN;IACG,qBACI,8OAAC,4KAAA,CAAA,OAA0B;QACvB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,+mBACA;QAEH,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,yBAAyB,EAC9B,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC2D;IAC9D,qBACI,8OAAC,4KAAA,CAAA,eAAkC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,gTACA;QAEJ,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACZ,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAChC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAG5B;;;;;;;AAGb;AAEA,SAAS,uBAAuB,EAC5B,GAAG,OACyD;IAC5D,qBAAO,8OAAC,4KAAA,CAAA,aAAgC;QAAC,aAAU;QAA6B,GAAG,KAAK;;;;;;AAC5F;AAEA,SAAS,sBAAsB,EAC3B,SAAS,EACT,QAAQ,EACR,GAAG,OACwD;IAC3D,qBACI,8OAAC,4KAAA,CAAA,YAA+B;QAC5B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,gTACA;QAEH,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACZ,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAChC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAG7B;;;;;;;AAGb;AAEA,SAAS,kBAAkB,EACvB,SAAS,EACT,KAAK,EACL,GAAG,OAGN;IACG,qBACI,8OAAC,4KAAA,CAAA,QAA2B;QACxB,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,sBAAsB,EAC3B,SAAS,EACT,GAAG,OACwD;IAC3D,qBACI,8OAAC,4KAAA,CAAA,YAA+B;QAC5B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,qBAAqB,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC/E,qBACI,8OAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,gBAAgB,EAAE,GAAG,OAA+D;IACzF,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC7E;AAEA,SAAS,uBAAuB,EAC5B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGN;IACG,qBACI,8OAAC,4KAAA,CAAA,aAAgC;QAC7B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,kOACA;QAEH,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGxC;AAEA,SAAS,uBAAuB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACI,8OAAC,4KAAA,CAAA,aAAgC;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,ifACA;QAEH,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 4642, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/color-picker/checkPattern.ts"], "sourcesContent": ["import { css } from '@emotion/react';\n\nexport function checkPattern(\n    color0: string,\n    color1: string,\n    size: string,\n    offsetX = '0px',\n    offsetY = '0px',\n) {\n    return css`\n        background-color: ${color0};\n        background-image:\n            linear-gradient(\n                45deg,\n                ${color1} 25%,\n                transparent 25%,\n                transparent 75%,\n                ${color1} 75%,\n                ${color1}\n            ),\n            linear-gradient(\n                45deg,\n                ${color1} 25%,\n                transparent 25%,\n                transparent 75%,\n                ${color1} 75%,\n                ${color1}\n            );\n        background-position:\n            ${offsetX} ${offsetY},\n            calc(${size} / 2 + ${offsetX}) calc(${size} / 2 + ${offsetY});\n        background-size: ${size} ${size};\n    `;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS,aACZ,MAAc,EACd,MAAc,EACd,IAAY,EACZ,UAAU,KAAK,EACf,UAAU,KAAK;IAEf,OAAO,oMAAA,CAAA,MAAG,CAAC;0BACW,EAAE,OAAO;;;;gBAInB,EAAE,OAAO;;;gBAGT,EAAE,OAAO;gBACT,EAAE,OAAO;;;;gBAIT,EAAE,OAAO;;;gBAGT,EAAE,OAAO;gBACT,EAAE,OAAO;;;YAGb,EAAE,QAAQ,CAAC,EAAE,QAAQ;iBAChB,EAAE,KAAK,OAAO,EAAE,QAAQ,OAAO,EAAE,KAAK,OAAO,EAAE,QAAQ;yBAC/C,EAAE,KAAK,CAAC,EAAE,KAAK;IACpC,CAAC;AACL", "debugId": null}}, {"offset": {"line": 4679, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/hooks/use-pointer-stroke.tsx"], "sourcesContent": ["import type React from 'react';\nimport { useCallback, useRef, type DependencyList } from 'react';\n\ninterface UsePointerStrokeOptions<T extends Element, InitData> {\n    onBegin: (e: React.PointerEvent<T>) => InitData;\n    onMove: (\n        e: React.PointerEvent<T>,\n        moves: {\n            totalDeltaX: number;\n            totalDeltaY: number;\n            deltaX: number;\n            deltaY: number;\n            initData: InitData;\n        },\n    ) => void;\n    onEnd?: (\n        e: React.PointerEvent<T>,\n        moves: {\n            totalDeltaX: number;\n            totalDeltaY: number;\n            initData: InitData;\n        },\n    ) => void;\n    onHover?: (e: React.PointerEvent<T>) => void;\n}\n\ninterface State<InitData> {\n    initX: number;\n    initY: number;\n    lastX: number;\n    lastY: number;\n    initData: InitData;\n}\n\nexport function usePointerStroke<T extends Element = Element, InitData = void>(\n    { onBegin, onMove, onEnd, onHover }: UsePointerStrokeOptions<T, InitData>,\n    deps?: DependencyList,\n): {\n    onPointerDown: (e: React.PointerEvent<T>) => void;\n    onPointerMove: (e: React.PointerEvent<T>) => void;\n    onPointerUp: (e: React.PointerEvent<T>) => void;\n} {\n    const stateRef = useRef<State<InitData> | null>(null);\n\n    const onPointerDown = useCallback((e: React.PointerEvent<T>) => {\n        if (e.button !== 0) {\n            return;\n        }\n\n        e.currentTarget.setPointerCapture(e.pointerId);\n        const x = Math.round(e.clientX);\n        const y = Math.round(e.clientY);\n        const initData = onBegin(e);\n        stateRef.current = {\n            initX: x,\n            initY: y,\n            lastX: x,\n            lastY: y,\n            initData,\n        };\n    }, deps as DependencyList);\n\n    const onPointerMove = useCallback((e: React.PointerEvent<T>) => {\n        if (!stateRef.current) {\n            onHover?.(e);\n            return;\n        }\n\n        const x = Math.round(e.clientX);\n        const y = Math.round(e.clientY);\n        const { initX, initY, lastX, lastY } = stateRef.current;\n\n        if (e.buttons === 0) {\n            // In some cases `onPointerUp` will not fire. Finish the stroke here\n            // and forward the last movement so state remains consistent.\n            const deltaX = x - lastX;\n            const deltaY = y - lastY;\n            stateRef.current.lastX = x;\n            stateRef.current.lastY = y;\n            onMove(e, {\n                totalDeltaX: x - initX,\n                totalDeltaY: y - initY,\n                deltaX,\n                deltaY,\n                initData: stateRef.current.initData,\n            });\n            e.currentTarget.releasePointerCapture(e.pointerId);\n            onEnd?.(e, {\n                totalDeltaX: x - initX,\n                totalDeltaY: y - initY,\n                initData: stateRef.current.initData,\n            });\n            stateRef.current = null;\n            return;\n        }\n\n        stateRef.current.lastX = x;\n        stateRef.current.lastY = y;\n        onMove(e, {\n            totalDeltaX: x - initX,\n            totalDeltaY: y - initY,\n            deltaX: x - lastX,\n            deltaY: y - lastY,\n            initData: stateRef.current.initData,\n        });\n    }, deps as DependencyList);\n\n    const onPointerUp = useCallback((e: React.PointerEvent<T>) => {\n        e.currentTarget.releasePointerCapture(e.pointerId);\n\n        if (!stateRef.current) {\n            return;\n        }\n\n        const x = Math.round(e.clientX);\n        const y = Math.round(e.clientY);\n        const { initX, initY } = stateRef.current;\n\n        onEnd?.(e, {\n            totalDeltaX: x - initX,\n            totalDeltaY: y - initY,\n            initData: stateRef.current.initData,\n        });\n        stateRef.current = null;\n    }, deps as DependencyList);\n\n    return { onPointerDown, onPointerMove, onPointerUp };\n}\n\nexport function usePointerStrokeCapture<T extends Element = Element, InitData = void>(\n    options: UsePointerStrokeOptions<T, InitData>,\n    deps?: DependencyList,\n): {\n    onPointerDownCapture: (e: React.PointerEvent<T>) => void;\n    onPointerMoveCapture: (e: React.PointerEvent<T>) => void;\n    onPointerUpCapture: (e: React.PointerEvent<T>) => void;\n} {\n    const props = usePointerStroke(options, deps);\n    return {\n        onPointerDownCapture: props.onPointerDown,\n        onPointerMoveCapture: props.onPointerMove,\n        onPointerUpCapture: props.onPointerUp,\n    };\n}\n"], "names": [], "mappings": ";;;;AACA;;AAiCO,SAAS,iBACZ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAwC,EACzE,IAAqB;IAMrB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAEhD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI,EAAE,MAAM,KAAK,GAAG;YAChB;QACJ;QAEA,EAAE,aAAa,CAAC,iBAAiB,CAAC,EAAE,SAAS;QAC7C,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO;QAC9B,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO;QAC9B,MAAM,WAAW,QAAQ;QACzB,SAAS,OAAO,GAAG;YACf,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP;QACJ;IACJ,GAAG;IAEH,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI,CAAC,SAAS,OAAO,EAAE;YACnB,UAAU;YACV;QACJ;QAEA,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO;QAC9B,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO;QAC9B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,SAAS,OAAO;QAEvD,IAAI,EAAE,OAAO,KAAK,GAAG;YACjB,oEAAoE;YACpE,6DAA6D;YAC7D,MAAM,SAAS,IAAI;YACnB,MAAM,SAAS,IAAI;YACnB,SAAS,OAAO,CAAC,KAAK,GAAG;YACzB,SAAS,OAAO,CAAC,KAAK,GAAG;YACzB,OAAO,GAAG;gBACN,aAAa,IAAI;gBACjB,aAAa,IAAI;gBACjB;gBACA;gBACA,UAAU,SAAS,OAAO,CAAC,QAAQ;YACvC;YACA,EAAE,aAAa,CAAC,qBAAqB,CAAC,EAAE,SAAS;YACjD,QAAQ,GAAG;gBACP,aAAa,IAAI;gBACjB,aAAa,IAAI;gBACjB,UAAU,SAAS,OAAO,CAAC,QAAQ;YACvC;YACA,SAAS,OAAO,GAAG;YACnB;QACJ;QAEA,SAAS,OAAO,CAAC,KAAK,GAAG;QACzB,SAAS,OAAO,CAAC,KAAK,GAAG;QACzB,OAAO,GAAG;YACN,aAAa,IAAI;YACjB,aAAa,IAAI;YACjB,QAAQ,IAAI;YACZ,QAAQ,IAAI;YACZ,UAAU,SAAS,OAAO,CAAC,QAAQ;QACvC;IACJ,GAAG;IAEH,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,EAAE,aAAa,CAAC,qBAAqB,CAAC,EAAE,SAAS;QAEjD,IAAI,CAAC,SAAS,OAAO,EAAE;YACnB;QACJ;QAEA,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO;QAC9B,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO;QAC9B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,SAAS,OAAO;QAEzC,QAAQ,GAAG;YACP,aAAa,IAAI;YACjB,aAAa,IAAI;YACjB,UAAU,SAAS,OAAO,CAAC,QAAQ;QACvC;QACA,SAAS,OAAO,GAAG;IACvB,GAAG;IAEH,OAAO;QAAE;QAAe;QAAe;IAAY;AACvD;AAEO,SAAS,wBACZ,OAA6C,EAC7C,IAAqB;IAMrB,MAAM,QAAQ,iBAAiB,SAAS;IACxC,OAAO;QACH,sBAAsB,MAAM,aAAa;QACzC,sBAAsB,MAAM,aAAa;QACzC,oBAAoB,MAAM,WAAW;IACzC;AACJ", "debugId": null}}, {"offset": {"line": 4779, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/color-picker/ColorSlider.tsx"], "sourcesContent": ["import { clamp } from 'lodash';\nimport type React from 'react';\nimport styled from '@emotion/styled';\nimport { usePointerStroke } from '../../hooks/use-pointer-stroke';\nimport { checkPattern } from './checkPattern';\n\nexport const ColorHandle = styled.div`\n    border-radius: 50%;\n    background: white;\n    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);\n    display: grid;\n    place-items: center;\n    pointer-events: none;\n    &::before {\n        content: '';\n        background-color: currentColor;\n        width: 50%;\n        height: 50%;\n        border-radius: 50%;\n    }\n`;\n\nconst ColorSliderWrap = styled.div`\n    display: grid;\n    place-items: center;\n    position: relative;\n    z-index: 0; /* Create stacking context */\n`;\nconst ColorSliderBar = styled.div`\n    position: relative;\n    overflow: hidden;\n    cursor: pointer;\n    &::before {\n        content: '';\n\n        z-index: -1;\n\n        position: absolute;\n        left: 0;\n        right: 0;\n        top: 0;\n        bottom: 0;\n\n        ${checkPattern('white', '#aaa', '8px')}\n    }\n`;\nconst ColorSliderGradient = styled.div`\n    z-index: -1;\n    position: absolute;\n    left: 0;\n    right: 0;\n    top: 0;\n    bottom: 0;\n`;\n\nexport function createGradient(\n    direction: 'right' | 'top',\n    length: number,\n    handleSize: number,\n    colors: [string, number][],\n): string {\n    const offset = handleSize / 2 / length;\n    const gradientLength = (length - handleSize) / length;\n\n    const stops = colors.map(\n        ([color, pos]) => `${color} ${(pos * gradientLength + offset) * 100}%`,\n    );\n\n    return `linear-gradient(${direction === 'right' ? 'to right' : 'to top'}, ${stops.join(',')})`;\n}\n\nexport const ColorSlider: React.FC<{\n    direction: 'right' | 'top';\n    length: number;\n    handleSize: number;\n    railWidth: number;\n    value: number;\n    color: string;\n    colorStops: string[];\n    onChangeEnd?: (value: number) => void;\n    onChange?: (value: number) => void;\n    onMouseDown?: (value: number) => void;\n}> = ({\n    direction,\n    length,\n    handleSize,\n    railWidth,\n    color,\n    colorStops,\n    value,\n    onChangeEnd,\n    onChange,\n    onMouseDown,\n}) => {\n    const gradient = createGradient(\n        direction,\n        length,\n        handleSize,\n        colorStops.map((stop, i) => [stop, i / (colorStops.length - 1)]),\n    );\n    const range = length - handleSize;\n\n    const pointerProps = usePointerStroke<HTMLElement>({\n        onBegin: (e) => {\n            onMouseDown?.(valueAtEvent(e));\n        },\n        onMove: (e) => {\n            onChange?.(valueAtEvent(e));\n        },\n        onEnd: (e) => {\n            onChangeEnd?.(valueAtEvent(e));\n        },\n    });\n\n    const valueAtEvent = (e: React.MouseEvent<HTMLElement>) => {\n        // TODO: Fix value is wrong when CSS transform is applied\n        const rect = e.currentTarget.getBoundingClientRect();\n        let value;\n        if (direction === 'right') {\n            const offset = e.clientX - rect.left - handleSize / 2;\n            value = clamp(offset / range, 0, 1);\n        } else {\n            const offset = e.clientY - rect.top - handleSize / 2;\n            value = clamp(1 - offset / range, 0, 1);\n        }\n\n        return value;\n    };\n\n    return (\n        <ColorSliderWrap\n            tabIndex={0}\n            style={\n                direction === 'right'\n                    ? { width: `${length}px`, height: `${handleSize}px` }\n                    : { height: `${length}px`, width: `${handleSize}px` }\n            }\n            {...pointerProps}\n        >\n            <ColorSliderBar\n                style={{\n                    borderRadius: `${railWidth / 2}px`,\n                    ...(direction === 'right'\n                        ? {\n                              width: length,\n                              height: railWidth,\n                          }\n                        : {\n                              height: length,\n                              width: railWidth,\n                          }),\n                }}\n            >\n                <ColorSliderGradient\n                    style={{\n                        background: gradient,\n                    }}\n                />\n            </ColorSliderBar>\n            <ColorHandle\n                style={{\n                    position: 'absolute',\n                    width: `${handleSize}px`,\n                    height: `${handleSize}px`,\n                    color: color,\n                    ...(direction === 'right'\n                        ? {\n                              left: `${range * value}px`,\n                              top: 0,\n                          }\n                        : {\n                              left: 0,\n                              top: `${range * (1 - value)}px`,\n                          }),\n                }}\n            />\n        </ColorSliderWrap>\n    );\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AAEA;AACA;AACA;;;;;;AAEO,MAAM,cAAc,sLAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;;;;;;;;;AActC,CAAC;AAED,MAAM,kBAAkB,sLAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;AAKnC,CAAC;AACD,MAAM,iBAAiB,sLAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;QAe1B,EAAE,CAAA,GAAA,sKAAA,CAAA,eAAY,AAAD,EAAE,SAAS,QAAQ,OAAO;;AAE/C,CAAC;AACD,MAAM,sBAAsB,sLAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;;AAOvC,CAAC;AAEM,SAAS,eACZ,SAA0B,EAC1B,MAAc,EACd,UAAkB,EAClB,MAA0B;IAE1B,MAAM,SAAS,aAAa,IAAI;IAChC,MAAM,iBAAiB,CAAC,SAAS,UAAU,IAAI;IAE/C,MAAM,QAAQ,OAAO,GAAG,CACpB,CAAC,CAAC,OAAO,IAAI,GAAK,GAAG,MAAM,CAAC,EAAE,CAAC,MAAM,iBAAiB,MAAM,IAAI,IAAI,CAAC,CAAC;IAG1E,OAAO,CAAC,gBAAgB,EAAE,cAAc,UAAU,aAAa,SAAS,EAAE,EAAE,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClG;AAEO,MAAM,cAWR,CAAC,EACF,SAAS,EACT,MAAM,EACN,UAAU,EACV,SAAS,EACT,KAAK,EACL,UAAU,EACV,KAAK,EACL,WAAW,EACX,QAAQ,EACR,WAAW,EACd;IACG,MAAM,WAAW,eACb,WACA,QACA,YACA,WAAW,GAAG,CAAC,CAAC,MAAM,IAAM;YAAC;YAAM,IAAI,CAAC,WAAW,MAAM,GAAG,CAAC;SAAE;IAEnE,MAAM,QAAQ,SAAS;IAEvB,MAAM,eAAe,CAAA,GAAA,2JAAA,CAAA,mBAAgB,AAAD,EAAe;QAC/C,SAAS,CAAC;YACN,cAAc,aAAa;QAC/B;QACA,QAAQ,CAAC;YACL,WAAW,aAAa;QAC5B;QACA,OAAO,CAAC;YACJ,cAAc,aAAa;QAC/B;IACJ;IAEA,MAAM,eAAe,CAAC;QAClB,yDAAyD;QACzD,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;QAClD,IAAI;QACJ,IAAI,cAAc,SAAS;YACvB,MAAM,SAAS,EAAE,OAAO,GAAG,KAAK,IAAI,GAAG,aAAa;YACpD,QAAQ,CAAA,GAAA,+HAAA,CAAA,UAAK,AAAD,EAAE,SAAS,OAAO,GAAG;QACrC,OAAO;YACH,MAAM,SAAS,EAAE,OAAO,GAAG,KAAK,GAAG,GAAG,aAAa;YACnD,QAAQ,CAAA,GAAA,+HAAA,CAAA,UAAK,AAAD,EAAE,IAAI,SAAS,OAAO,GAAG;QACzC;QAEA,OAAO;IACX;IAEA,qBACI,8OAAC;QACG,UAAU;QACV,OACI,cAAc,UACR;YAAE,OAAO,GAAG,OAAO,EAAE,CAAC;YAAE,QAAQ,GAAG,WAAW,EAAE,CAAC;QAAC,IAClD;YAAE,QAAQ,GAAG,OAAO,EAAE,CAAC;YAAE,OAAO,GAAG,WAAW,EAAE,CAAC;QAAC;QAE3D,GAAG,YAAY;;0BAEhB,8OAAC;gBACG,OAAO;oBACH,cAAc,GAAG,YAAY,EAAE,EAAE,CAAC;oBAClC,GAAI,cAAc,UACZ;wBACI,OAAO;wBACP,QAAQ;oBACZ,IACA;wBACI,QAAQ;wBACR,OAAO;oBACX,CAAC;gBACX;0BAEA,cAAA,8OAAC;oBACG,OAAO;wBACH,YAAY;oBAChB;;;;;;;;;;;0BAGR,8OAAC;gBACG,OAAO;oBACH,UAAU;oBACV,OAAO,GAAG,WAAW,EAAE,CAAC;oBACxB,QAAQ,GAAG,WAAW,EAAE,CAAC;oBACzB,OAAO;oBACP,GAAI,cAAc,UACZ;wBACI,MAAM,GAAG,QAAQ,MAAM,EAAE,CAAC;wBAC1B,KAAK;oBACT,IACA;wBACI,MAAM;wBACN,KAAK,GAAG,QAAQ,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;oBACnC,CAAC;gBACX;;;;;;;;;;;;AAIhB", "debugId": null}}, {"offset": {"line": 4945, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/color-picker/SVPicker.tsx"], "sourcesContent": ["import { clamp } from 'lodash';\nimport type React from 'react';\nimport { usePointerStroke } from '../../hooks/use-pointer-stroke';\nimport { ColorHandle } from './ColorSlider';\nimport { Color } from '@onlook/utility';\n\ninterface SVPickerGradientProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nconst SVPickerGradient: React.FC<SVPickerGradientProps> = ({ ...props }) => (\n    <div className=\"absolute inset-0 z-[-1]\" {...props}></div>\n);\n\ninterface SVPickerWrapProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nconst SVPickerWrap: React.FC<SVPickerWrapProps> = ({ children, ...props }) => (\n    <div className=\"relative z-0\" {...props}>\n        {children}\n    </div>\n);\n\ninterface SVPickerBodyProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nconst SVPickerBody: React.FC<SVPickerBodyProps> = ({ children, ...props }) => (\n    <div\n        className=\"relative shadow-inner border border-gray-300 rounded-sm overflow-hidden cursor-pointer\"\n        {...props}\n    >\n        {children}\n    </div>\n);\n\nexport const SVPicker: React.FC<{\n    width: number;\n    height: number;\n    handleSize: number;\n    color: Color;\n    onChangeEnd: (color: Color) => void;\n    onChange: (color: Color) => void;\n    onMouseDown: (color: Color) => void;\n}> = ({ width, height, handleSize, color, onChangeEnd, onChange, onMouseDown }) => {\n    const hueDeg = Math.round(color.h * 360);\n\n    const saturationGradient = `linear-gradient(to right, hsl(${hueDeg}, 0%, 100%), hsl(${hueDeg}, 100%, 50%))`;\n    const valueGradient = `linear-gradient(to top, hsl(${hueDeg}, 0%, 0%), hsl(${hueDeg}, 0%, 100%))`;\n\n    const valueAtEvent = (e: React.MouseEvent<HTMLElement>) => {\n        const rect = e.currentTarget.getBoundingClientRect();\n        const s = clamp((e.clientX - rect.left) / rect.width, 0, 1);\n        const v = clamp(1 - (e.clientY - rect.top) / rect.height, 0, 1);\n        return new Color({ ...color, s, v });\n    };\n\n    const pointerProps = usePointerStroke<HTMLElement>({\n        onBegin: (e) => {\n            onMouseDown(valueAtEvent(e));\n        },\n        onMove: (e) => {\n            onChange(valueAtEvent(e));\n        },\n        onEnd: (e) => {\n            onChangeEnd(valueAtEvent(e));\n        },\n    });\n\n    return (\n        <SVPickerWrap>\n            <SVPickerBody\n                style={{\n                    width: `${width}px`,\n                    height: `${height}px`,\n                }}\n                {...pointerProps}\n            >\n                <SVPickerGradient style={{ background: valueGradient }} />\n                <SVPickerGradient\n                    style={{ background: saturationGradient, mixBlendMode: 'multiply' }}\n                />\n                <ColorHandle\n                    style={{\n                        position: 'absolute',\n                        left: `${-handleSize / 2 + width * color.s}px`,\n                        top: `${-handleSize / 2 + height * (1 - color.v)}px`,\n                        width: `${handleSize}px`,\n                        height: `${handleSize}px`,\n                        color: color.toHex(),\n                    }}\n                />\n            </SVPickerBody>\n        </SVPickerWrap>\n    );\n};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AAAA;;;;;;AAIA,MAAM,mBAAoD,CAAC,EAAE,GAAG,OAAO,iBACnE,8OAAC;QAAI,WAAU;QAA2B,GAAG,KAAK;;;;;;AAKtD,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO,iBACrE,8OAAC;QAAI,WAAU;QAAgB,GAAG,KAAK;kBAClC;;;;;;AAMT,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO,iBACrE,8OAAC;QACG,WAAU;QACT,GAAG,KAAK;kBAER;;;;;;AAIF,MAAM,WAQR,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC1E,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG;IAEpC,MAAM,qBAAqB,CAAC,8BAA8B,EAAE,OAAO,iBAAiB,EAAE,OAAO,aAAa,CAAC;IAC3G,MAAM,gBAAgB,CAAC,4BAA4B,EAAE,OAAO,eAAe,EAAE,OAAO,YAAY,CAAC;IAEjG,MAAM,eAAe,CAAC;QAClB,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;QAClD,MAAM,IAAI,CAAA,GAAA,+HAAA,CAAA,UAAK,AAAD,EAAE,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,GAAG;QACzD,MAAM,IAAI,CAAA,GAAA,+HAAA,CAAA,UAAK,AAAD,EAAE,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,GAAG,IAAI,KAAK,MAAM,EAAE,GAAG;QAC7D,OAAO,IAAI,mIAAA,CAAA,QAAK,CAAC;YAAE,GAAG,KAAK;YAAE;YAAG;QAAE;IACtC;IAEA,MAAM,eAAe,CAAA,GAAA,2JAAA,CAAA,mBAAgB,AAAD,EAAe;QAC/C,SAAS,CAAC;YACN,YAAY,aAAa;QAC7B;QACA,QAAQ,CAAC;YACL,SAAS,aAAa;QAC1B;QACA,OAAO,CAAC;YACJ,YAAY,aAAa;QAC7B;IACJ;IAEA,qBACI,8OAAC;kBACG,cAAA,8OAAC;YACG,OAAO;gBACH,OAAO,GAAG,MAAM,EAAE,CAAC;gBACnB,QAAQ,GAAG,OAAO,EAAE,CAAC;YACzB;YACC,GAAG,YAAY;;8BAEhB,8OAAC;oBAAiB,OAAO;wBAAE,YAAY;oBAAc;;;;;;8BACrD,8OAAC;oBACG,OAAO;wBAAE,YAAY;wBAAoB,cAAc;oBAAW;;;;;;8BAEtE,8OAAC,sKAAA,CAAA,cAAW;oBACR,OAAO;wBACH,UAAU;wBACV,MAAM,GAAG,CAAC,aAAa,IAAI,QAAQ,MAAM,CAAC,CAAC,EAAE,CAAC;wBAC9C,KAAK,GAAG,CAAC,aAAa,IAAI,SAAS,CAAC,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;wBACpD,OAAO,GAAG,WAAW,EAAE,CAAC;wBACxB,QAAQ,GAAG,WAAW,EAAE,CAAC;wBACzB,OAAO,MAAM,KAAK;oBACtB;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 5069, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/draftable-input.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { mergeRefs } from 'react-merge-refs';\n\nexport function useDraftValue<T>(\n    value: T,\n    onChange: (value: T) => void,\n): [\n    T, // draft\n    (value: T) => void, // on change draft value\n    () => void, // on change done\n] {\n    const [draft, setDraft] = React.useState(value);\n\n    React.useEffect(() => {\n        setDraft(value);\n    }, [value]);\n\n    return [draft, setDraft, () => onChange(draft)];\n}\n\nexport type DraftableInputProps = Omit<\n    React.InputHTMLAttributes<HTMLInputElement>,\n    'ref' | 'value'\n> & {\n    ref?: React.Ref<HTMLInputElement>;\n    value?: string;\n    onChangeValue?: (value: string) => void;\n};\n\nconst DraftableInput = React.forwardRef<HTMLInputElement, DraftableInputProps>(\n    ({ value, placeholder, onChangeValue, ...props }, ref) => {\n        const inputRef = React.createRef<HTMLInputElement>();\n\n        const [draft, onDraftChange, onDraftChangeDone] = useDraftValue<string>(\n            value ?? '',\n            onChangeValue ?? (() => {}),\n        );\n\n        React.useEffect(() => {\n            const input = inputRef.current;\n            if (input) {\n                const onChangeNative = () => {\n                    onDraftChangeDone();\n                };\n                input.addEventListener('change', onChangeNative);\n                return () => {\n                    input.removeEventListener('change', onChangeNative);\n                };\n            }\n        }, [inputRef, onDraftChangeDone]);\n\n        return (\n            <input\n                {...props}\n                value={draft}\n                placeholder={placeholder}\n                onChange={(e) => onDraftChange(e.currentTarget.value)}\n                ref={mergeRefs([inputRef, ref])}\n            />\n        );\n    },\n);\n\nDraftableInput.displayName = 'DraftableInput';\n\nexport { DraftableInput };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAEO,SAAS,cACZ,KAAQ,EACR,QAA4B;IAM5B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACZ,SAAS;IACb,GAAG;QAAC;KAAM;IAEV,OAAO;QAAC;QAAO;QAAU,IAAM,SAAS;KAAO;AACnD;AAWA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAClC,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,OAAO,EAAE;IAC9C,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD;IAE/B,MAAM,CAAC,OAAO,eAAe,kBAAkB,GAAG,cAC9C,SAAS,IACT,iBAAiB,CAAC,KAAO,CAAC;IAG9B,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACZ,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACP,MAAM,iBAAiB;gBACnB;YACJ;YACA,MAAM,gBAAgB,CAAC,UAAU;YACjC,OAAO;gBACH,MAAM,mBAAmB,CAAC,UAAU;YACxC;QACJ;IACJ,GAAG;QAAC;QAAU;KAAkB;IAEhC,qBACI,8OAAC;QACI,GAAG,KAAK;QACT,OAAO;QACP,aAAa;QACb,UAAU,CAAC,IAAM,cAAc,EAAE,aAAa,CAAC,KAAK;QACpD,KAAK,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE;YAAC;YAAU;SAAI;;;;;;AAG1C;AAGJ,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5133, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/input-group.tsx"], "sourcesContent": ["import { Children, type ReactElement, cloneElement } from 'react';\nimport type { DraftableInputProps } from './draftable-input';\nimport { cn } from '../utils';\n\ninterface InputGroupProps {\n    className?: string;\n    children: ReactElement<DraftableInputProps>[] | ReactElement<DraftableInputProps>;\n}\n\nexport const InputGroup = ({ className, children }: InputGroupProps) => {\n    const childrenArray = Children.toArray(children) as ReactElement<DraftableInputProps>[];\n    const totalInputs = childrenArray.length;\n\n    return (\n        <div className={cn('flex w-fit min-w-0', className)}>\n            {childrenArray.map((child, index) => {\n                const isFirst = index === 0;\n                const isLast = index === totalInputs - 1;\n\n                return cloneElement(child, {\n                    className: cn(child.props.className, {\n                        'rounded-l-none': !isFirst,\n                        'rounded-r-none': !isLast,\n                        'rounded-none': !isFirst && !isLast,\n                        'border-l-0': !isFirst,\n                        'border-r-0': !isLast,\n                    }),\n                });\n            })}\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;;;;AAOO,MAAM,aAAa,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAmB;IAC/D,MAAM,gBAAgB,qMAAA,CAAA,WAAQ,CAAC,OAAO,CAAC;IACvC,MAAM,cAAc,cAAc,MAAM;IAExC,qBACI,8OAAC;QAAI,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;kBACpC,cAAc,GAAG,CAAC,CAAC,OAAO;YACvB,MAAM,UAAU,UAAU;YAC1B,MAAM,SAAS,UAAU,cAAc;YAEvC,qBAAO,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBACvB,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,MAAM,KAAK,CAAC,SAAS,EAAE;oBACjC,kBAAkB,CAAC;oBACnB,kBAAkB,CAAC;oBACnB,gBAAgB,CAAC,WAAW,CAAC;oBAC7B,cAAc,CAAC;oBACf,cAAc,CAAC;gBACnB;YACJ;QACJ;;;;;;AAGZ", "debugId": null}}, {"offset": {"line": 5173, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/color-picker/EyeDropperButton.tsx"], "sourcesContent": ["'use client';\n\nimport { Color } from '@onlook/utility';\nimport { useCallback, useMemo } from 'react';\nimport useEyeDropper from 'use-eye-dropper';\nimport { Button } from '../button';\nimport { Icons } from '../icons';\n\ntype EyeDropperButtonProps = React.ComponentProps<'button'> & {\n    onColorSelect?: (color: Color) => void;\n};\n\nexport const useIsEyeDropperSupported = () => {\n    const { isSupported } = useEyeDropper();\n    const isSupportedFlag = useMemo(() => isSupported(), [isSupported]);\n    return isSupportedFlag;\n};\n\nexport const EyeDropperButton = ({ onColorSelect, disabled }: EyeDropperButtonProps) => {\n    const { open, isSupported } = useEyeDropper();\n\n    const pickColor = useCallback(() => {\n        const openPicker = async () => {\n            try {\n                const result = await open();\n                const color = Color.from(result.sRGBHex);\n                onColorSelect?.(color);\n            } catch (e: any) {\n                console.error('Error while opening color picker: ', e);\n            }\n        };\n        openPicker();\n    }, [open, onColorSelect]);\n\n    return (\n        <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            disabled={!isSupported() || disabled}\n            onClick={pickColor}\n        >\n            <Icons.EyeDropper />\n        </Button>\n    );\n};\n\nexport default EyeDropperButton;\n"], "names": [], "mappings": ";;;;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYO,MAAM,2BAA2B;IACpC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAa,AAAD;IACpC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,eAAe;QAAC;KAAY;IAClE,OAAO;AACX;AAEO,MAAM,mBAAmB,CAAC,EAAE,aAAa,EAAE,QAAQ,EAAyB;IAC/E,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAa,AAAD;IAE1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,MAAM,aAAa;YACf,IAAI;gBACA,MAAM,SAAS,MAAM;gBACrB,MAAM,QAAQ,mIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,OAAO,OAAO;gBACvC,gBAAgB;YACpB,EAAE,OAAO,GAAQ;gBACb,QAAQ,KAAK,CAAC,sCAAsC;YACxD;QACJ;QACA;IACJ,GAAG;QAAC;QAAM;KAAc;IAExB,qBACI,8OAAC,8IAAA,CAAA,SAAM;QACH,SAAQ;QACR,MAAK;QACL,UAAU,CAAC,iBAAiB;QAC5B,SAAS;kBAET,cAAA,8OAAC,sJAAA,CAAA,QAAK,CAAC,UAAU;;;;;;;;;;AAG7B;uCAEe", "debugId": null}}, {"offset": {"line": 5239, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/color-picker/ColorPicker.tsx"], "sourcesContent": ["import type React from 'react';\nimport { useState, useEffect, useCallback } from 'react';\nimport { ColorSlider } from './ColorSlider';\nimport { SVPicker } from './SVPicker';\nimport tw from 'tailwind-styled-components';\nimport { twMerge } from 'tailwind-merge';\nimport { Color } from '@onlook/utility';\nimport { mod } from '@onlook/utility';\nimport { DraftableInput } from '../draftable-input';\nimport styled from '@emotion/styled';\nimport { InputGroup } from '../input-group';\nimport EyeDropperButton from './EyeDropperButton';\n\nconst Input = tw(DraftableInput)`\n  outline-0 w-full h-6 bg-background-onlook/70 rounded focus:ring-1 ring-inset ring-foreground-active text-foreground-primary placeholder:text-foreground-disabled text-center\n`;\n\ntype SliderMode = 'hsl' | 'hsv' | 'rgb' | 'hex';\n\nconst InputsRow = ({\n    color,\n    onChangeEnd,\n    onChange,\n}: {\n    color: Color;\n    onChangeEnd?: (color: Color) => void;\n    onChange?: (color: Color) => void;\n}) => {\n    const [mode, setMode] = useState<SliderMode>('hex');\n\n    const rgbColor = color.rgb;\n    const hslColor = color.hsl;\n\n    return (\n        <div className=\"z-50 grid grid-cols-[48px_1fr_1fr_1fr_46px] gap-1 text-mini\">\n            <div className=\"flex items-center justify-center gap-1 min-w-0 \">\n                <label\n                    className=\"text-small text-foreground-primary cursor-pointer hover:text-foreground-hover bg-background-secondary border-[0.5px] border-foreground-tertiary/50 hover:bg-background-hover w-full flex rounded-sm justify-center py-[0.5px] select-none\"\n                    onClick={() =>\n                        mode === 'hsl'\n                            ? setMode('hsv')\n                            : mode === 'hsv'\n                              ? setMode('rgb')\n                              : mode === 'rgb'\n                                ? setMode('hex')\n                                : setMode('hsl')\n                    }\n                >\n                    {mode.toUpperCase()}\n                </label>\n            </div>\n            {mode === 'hsl' ? (\n                <InputGroup className=\"grid grid-cols-subgrid col-span-3 gap-[1px]\">\n                    <Input\n                        value={Math.round(hslColor['h'] * 100).toString()}\n                        onChangeValue={(valueString) => {\n                            const value = mod(Number.parseInt(valueString) / 100, 1);\n                            const newColor = Color.hsl({\n                                ...hslColor,\n                                h: value,\n                            });\n                            onChangeEnd?.(newColor);\n                            onChange?.(newColor);\n                            return true;\n                        }}\n                    />\n                    <Input\n                        value={Math.round(hslColor['s'] * 100).toString()}\n                        onChangeValue={(valueString) => {\n                            const value = mod(Number.parseInt(valueString) / 100, 1);\n                            const newColor = Color.hsl({\n                                ...hslColor,\n                                s: value,\n                            });\n                            onChangeEnd?.(newColor);\n                            onChange?.(newColor);\n                            return true;\n                        }}\n                    />\n                    <Input\n                        value={Math.round(hslColor['l'] * 100).toString()}\n                        onChangeValue={(valueString) => {\n                            const value = mod(Number.parseInt(valueString) / 100, 1);\n                            const newColor = Color.hsl({\n                                ...hslColor,\n                                l: value,\n                            });\n                            onChangeEnd?.(newColor);\n                            onChange?.(newColor);\n                            return true;\n                        }}\n                    />\n                </InputGroup>\n            ) : mode === 'hsv' ? (\n                <InputGroup className=\"grid grid-cols-subgrid col-span-3 gap-[1px]\">\n                    <Input\n                        value={Math.round(color.h * 360).toString()}\n                        onChangeValue={(hString) => {\n                            const h = mod(Number.parseInt(hString) / 360, 1);\n                            const newColor = new Color({ ...color, h });\n                            onChangeEnd?.(newColor);\n                            onChange?.(newColor);\n                            return true;\n                        }}\n                    />\n                    <Input\n                        value={Math.round(color['s'] * 100).toString()}\n                        onChangeValue={(valueString) => {\n                            const value = mod(Number.parseInt(valueString) / 100, 1);\n                            const newColor = new Color({ ...color, s: value });\n                            onChangeEnd?.(newColor);\n                            onChange?.(newColor);\n                            return true;\n                        }}\n                    />\n                    <Input\n                        value={Math.round(color['v'] * 100).toString()}\n                        onChangeValue={(valueString) => {\n                            const value = mod(Number.parseInt(valueString) / 100, 1);\n                            const newColor = new Color({ ...color, v: value });\n                            onChangeEnd?.(newColor);\n                            onChange?.(newColor);\n                            return true;\n                        }}\n                    />\n                </InputGroup>\n            ) : mode === 'rgb' ? (\n                <InputGroup className=\"grid grid-cols-subgrid col-span-3 gap-[1px]\">\n                    <Input\n                        value={Math.round(rgbColor['r'] * 255).toString()}\n                        onChangeValue={(valueString) => {\n                            const value = mod(Number.parseInt(valueString) / 255, 1);\n                            const newColor = Color.rgb({\n                                ...rgbColor,\n                                r: value,\n                            });\n                            onChangeEnd?.(newColor);\n                            onChange?.(newColor);\n                            return true;\n                        }}\n                    />\n                    <Input\n                        value={Math.round(rgbColor['g'] * 255).toString()}\n                        onChangeValue={(valueString) => {\n                            const value = mod(Number.parseInt(valueString) / 255, 1);\n                            const newColor = Color.rgb({\n                                ...rgbColor,\n                                g: value,\n                            });\n                            onChangeEnd?.(newColor);\n                            onChange?.(newColor);\n                            return true;\n                        }}\n                    />\n                    <Input\n                        value={Math.round(rgbColor['b'] * 255).toString()}\n                        onChangeValue={(valueString) => {\n                            const value = mod(Number.parseInt(valueString) / 255, 1);\n                            const newColor = Color.rgb({\n                                ...rgbColor,\n                                b: value,\n                            });\n                            onChangeEnd?.(newColor);\n                            onChange?.(newColor);\n                            return true;\n                        }}\n                    />\n                </InputGroup>\n            ) : (\n                <InputGroup className=\"col-span-3\">\n                    <Input\n                        value={color.toHex6()}\n                        onChangeValue={(hexString) => {\n                            const newColor = Color.from(hexString);\n                            onChange?.(newColor);\n                            onChangeEnd?.(newColor);\n                            return true;\n                        }}\n                    />\n                </InputGroup>\n            )}\n            <div className=\"relative w-full\">\n                <Input\n                    value={Math.round(color.a * 100).toString()}\n                    onChangeValue={(aString) => {\n                        const a = mod(Number.parseInt(aString.replace('%', '')) / 100, 1);\n                        const newColor = new Color({ ...color, a });\n                        onChangeEnd?.(newColor);\n                        onChange?.(newColor);\n                        return true;\n                    }}\n                    className=\"pr-3\"\n                />\n                <span\n                    className=\"absolute right-[5px] top-1/2 transform -translate-y-1/2 text-foreground-tertiary\"\n                    style={{ userSelect: 'none' }}\n                >\n                    %\n                </span>\n            </div>\n        </div>\n    );\n};\n\nconst EyeDropperBox = styled.div`\n    position: relative;\n    overflow: hidden;\n    width: 36px;\n    height: 36px;\n    border-radius: 25%;\n    &::before {\n        content: '';\n\n        z-index: -1;\n\n        position: absolute;\n        left: 0;\n        right: 0;\n        top: 0;\n        bottom: 0;\n    }\n`;\n\nexport const ColorPicker: React.FC<{\n    color: Color;\n    onChangeEnd?: (color: Color) => void;\n    onChange?: (color: Color) => void;\n    onMouseDown?: (color: Color) => void;\n    className?: string;\n}> = ({ color, onChangeEnd, onChange, onMouseDown, className }) => {\n    const [activeHue, setActiveHue] = useState(color.h);\n    const [localColor, setLocalColor] = useState(color);\n\n    useEffect(() => {\n        if (color.s > 0.01) {\n            setActiveHue(color.h);\n        }\n        setLocalColor(color);\n    }, [color]);\n\n    const handleHueChange = useCallback(\n        (h: number) => {\n            const newHue = mod(h, 1);\n            setActiveHue(newHue);\n\n            const newColor = new Color({\n                ...localColor,\n                h: newHue,\n            });\n\n            setLocalColor(newColor);\n            onChange?.(newColor);\n        },\n        [localColor, onChange],\n    );\n\n    const handleSVChange = useCallback(\n        (newColor: Color) => {\n            const updatedColor = new Color({\n                h: activeHue,\n                s: newColor.s,\n                v: newColor.v,\n                a: localColor.a,\n            });\n\n            setLocalColor(updatedColor);\n            onChange?.(updatedColor);\n        },\n        [activeHue, localColor.a, onChange],\n    );\n\n    return (\n        <div className={twMerge('w-[224px] flex flex-col gap-1.5 p-2', className)}>\n            <SVPicker\n                width={208}\n                height={160}\n                handleSize={16}\n                color={new Color({ ...localColor, h: activeHue, a: 1 })}\n                onChangeEnd={(newColor) => {\n                    const updatedColor = new Color({\n                        h: activeHue,\n                        s: newColor.s,\n                        v: newColor.v,\n                        a: localColor.a,\n                    });\n                    setLocalColor(updatedColor);\n                    onChangeEnd?.(updatedColor);\n                }}\n                onChange={handleSVChange}\n                onMouseDown={(newColor) => {\n                    const updatedColor = new Color({\n                        h: activeHue,\n                        s: newColor.s,\n                        v: newColor.v,\n                        a: localColor.a,\n                    });\n                    setLocalColor(updatedColor);\n                    onMouseDown?.(updatedColor);\n                }}\n            />\n            <div className=\"z-50 flex justify-between items-center\">\n                <EyeDropperBox>\n                    <EyeDropperButton\n                        onColorSelect={(newColor) => {\n                            setActiveHue(newColor.h);\n                            setLocalColor(newColor);\n                            onChangeEnd?.(newColor);\n                        }}\n                    />\n                </EyeDropperBox>\n                <div className=\"flex flex-col gap-1\">\n                    <ColorSlider\n                        direction=\"right\"\n                        length={165}\n                        handleSize={16}\n                        railWidth={13}\n                        color={new Color({ h: activeHue, s: 1, v: 1 }).toHex()}\n                        colorStops={[\n                            '#FF0000',\n                            '#FFFF00',\n                            '#00FF00',\n                            '#00FFFF',\n                            '#0000FF',\n                            '#FF00FF',\n                            '#FF0000',\n                        ]}\n                        value={activeHue}\n                        onChangeEnd={(h) => {\n                            setActiveHue(mod(h, 1));\n                            const newColor = new Color({ ...localColor, h: mod(h, 1) });\n                            setLocalColor(newColor);\n                            onChangeEnd?.(newColor);\n                        }}\n                        onChange={handleHueChange}\n                        onMouseDown={(h) => {\n                            setActiveHue(mod(h, 1));\n                            setLocalColor(new Color({ ...localColor, h: mod(h, 1) }));\n                            onMouseDown?.(new Color({ ...localColor, h: mod(h, 1) }));\n                        }}\n                    />\n                    <ColorSlider\n                        direction=\"right\"\n                        length={165}\n                        handleSize={16}\n                        railWidth={13}\n                        color={new Color({ ...localColor, a: 1 }).toHex()}\n                        colorStops={[\n                            new Color({ ...localColor, a: 0 }).toHex(),\n                            new Color({ ...localColor, a: 1 }).toHex(),\n                        ]}\n                        value={localColor.a}\n                        onChangeEnd={(a) => {\n                            setLocalColor(new Color({ ...localColor, a }));\n                            onChangeEnd?.(new Color({ ...localColor, a }));\n                        }}\n                        onChange={(a) => {\n                            setLocalColor(new Color({ ...localColor, a }));\n                            onChange?.(new Color({ ...localColor, a }));\n                        }}\n                        onMouseDown={(a) => {\n                            setLocalColor(new Color({ ...localColor, a }));\n                            onMouseDown?.(new Color({ ...localColor, a }));\n                        }}\n                    />\n                </div>\n            </div>\n            <InputsRow color={color} onChange={onChange} onChangeEnd={onChangeEnd} />\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAEA,MAAM,QAAQ,CAAA,GAAA,mMAAA,CAAA,UAAE,AAAD,EAAE,0JAAA,CAAA,iBAAc,CAAC,CAAC;;AAEjC,CAAC;AAID,MAAM,YAAY,CAAC,EACf,KAAK,EACL,WAAW,EACX,QAAQ,EAKX;IACG,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAE7C,MAAM,WAAW,MAAM,GAAG;IAC1B,MAAM,WAAW,MAAM,GAAG;IAE1B,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBACG,WAAU;oBACV,SAAS,IACL,SAAS,QACH,QAAQ,SACR,SAAS,QACP,QAAQ,SACR,SAAS,QACP,QAAQ,SACR,QAAQ;8BAGrB,KAAK,WAAW;;;;;;;;;;;YAGxB,SAAS,sBACN,8OAAC,sJAAA,CAAA,aAAU;gBAAC,WAAU;;kCAClB,8OAAC;wBACG,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,QAAQ;wBAC/C,eAAe,CAAC;4BACZ,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,OAAO,QAAQ,CAAC,eAAe,KAAK;4BACtD,MAAM,WAAW,mIAAA,CAAA,QAAK,CAAC,GAAG,CAAC;gCACvB,GAAG,QAAQ;gCACX,GAAG;4BACP;4BACA,cAAc;4BACd,WAAW;4BACX,OAAO;wBACX;;;;;;kCAEJ,8OAAC;wBACG,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,QAAQ;wBAC/C,eAAe,CAAC;4BACZ,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,OAAO,QAAQ,CAAC,eAAe,KAAK;4BACtD,MAAM,WAAW,mIAAA,CAAA,QAAK,CAAC,GAAG,CAAC;gCACvB,GAAG,QAAQ;gCACX,GAAG;4BACP;4BACA,cAAc;4BACd,WAAW;4BACX,OAAO;wBACX;;;;;;kCAEJ,8OAAC;wBACG,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,QAAQ;wBAC/C,eAAe,CAAC;4BACZ,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,OAAO,QAAQ,CAAC,eAAe,KAAK;4BACtD,MAAM,WAAW,mIAAA,CAAA,QAAK,CAAC,GAAG,CAAC;gCACvB,GAAG,QAAQ;gCACX,GAAG;4BACP;4BACA,cAAc;4BACd,WAAW;4BACX,OAAO;wBACX;;;;;;;;;;;uBAGR,SAAS,sBACT,8OAAC,sJAAA,CAAA,aAAU;gBAAC,WAAU;;kCAClB,8OAAC;wBACG,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,QAAQ;wBACzC,eAAe,CAAC;4BACZ,MAAM,IAAI,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,OAAO,QAAQ,CAAC,WAAW,KAAK;4BAC9C,MAAM,WAAW,IAAI,mIAAA,CAAA,QAAK,CAAC;gCAAE,GAAG,KAAK;gCAAE;4BAAE;4BACzC,cAAc;4BACd,WAAW;4BACX,OAAO;wBACX;;;;;;kCAEJ,8OAAC;wBACG,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,QAAQ;wBAC5C,eAAe,CAAC;4BACZ,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,OAAO,QAAQ,CAAC,eAAe,KAAK;4BACtD,MAAM,WAAW,IAAI,mIAAA,CAAA,QAAK,CAAC;gCAAE,GAAG,KAAK;gCAAE,GAAG;4BAAM;4BAChD,cAAc;4BACd,WAAW;4BACX,OAAO;wBACX;;;;;;kCAEJ,8OAAC;wBACG,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,QAAQ;wBAC5C,eAAe,CAAC;4BACZ,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,OAAO,QAAQ,CAAC,eAAe,KAAK;4BACtD,MAAM,WAAW,IAAI,mIAAA,CAAA,QAAK,CAAC;gCAAE,GAAG,KAAK;gCAAE,GAAG;4BAAM;4BAChD,cAAc;4BACd,WAAW;4BACX,OAAO;wBACX;;;;;;;;;;;uBAGR,SAAS,sBACT,8OAAC,sJAAA,CAAA,aAAU;gBAAC,WAAU;;kCAClB,8OAAC;wBACG,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,QAAQ;wBAC/C,eAAe,CAAC;4BACZ,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,OAAO,QAAQ,CAAC,eAAe,KAAK;4BACtD,MAAM,WAAW,mIAAA,CAAA,QAAK,CAAC,GAAG,CAAC;gCACvB,GAAG,QAAQ;gCACX,GAAG;4BACP;4BACA,cAAc;4BACd,WAAW;4BACX,OAAO;wBACX;;;;;;kCAEJ,8OAAC;wBACG,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,QAAQ;wBAC/C,eAAe,CAAC;4BACZ,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,OAAO,QAAQ,CAAC,eAAe,KAAK;4BACtD,MAAM,WAAW,mIAAA,CAAA,QAAK,CAAC,GAAG,CAAC;gCACvB,GAAG,QAAQ;gCACX,GAAG;4BACP;4BACA,cAAc;4BACd,WAAW;4BACX,OAAO;wBACX;;;;;;kCAEJ,8OAAC;wBACG,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,QAAQ;wBAC/C,eAAe,CAAC;4BACZ,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,OAAO,QAAQ,CAAC,eAAe,KAAK;4BACtD,MAAM,WAAW,mIAAA,CAAA,QAAK,CAAC,GAAG,CAAC;gCACvB,GAAG,QAAQ;gCACX,GAAG;4BACP;4BACA,cAAc;4BACd,WAAW;4BACX,OAAO;wBACX;;;;;;;;;;;qCAIR,8OAAC,sJAAA,CAAA,aAAU;gBAAC,WAAU;0BAClB,cAAA,8OAAC;oBACG,OAAO,MAAM,MAAM;oBACnB,eAAe,CAAC;wBACZ,MAAM,WAAW,mIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;wBAC5B,WAAW;wBACX,cAAc;wBACd,OAAO;oBACX;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBACG,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,QAAQ;wBACzC,eAAe,CAAC;4BACZ,MAAM,IAAI,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,OAAO,QAAQ,CAAC,QAAQ,OAAO,CAAC,KAAK,OAAO,KAAK;4BAC/D,MAAM,WAAW,IAAI,mIAAA,CAAA,QAAK,CAAC;gCAAE,GAAG,KAAK;gCAAE;4BAAE;4BACzC,cAAc;4BACd,WAAW;4BACX,OAAO;wBACX;wBACA,WAAU;;;;;;kCAEd,8OAAC;wBACG,WAAU;wBACV,OAAO;4BAAE,YAAY;wBAAO;kCAC/B;;;;;;;;;;;;;;;;;;AAMjB;AAEA,MAAM,gBAAgB,sLAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;AAiBjC,CAAC;AAEM,MAAM,cAMR,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,CAAC;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,MAAM,CAAC,GAAG,MAAM;YAChB,aAAa,MAAM,CAAC;QACxB;QACA,cAAc;IAClB,GAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACG,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,GAAG;QACtB,aAAa;QAEb,MAAM,WAAW,IAAI,mIAAA,CAAA,QAAK,CAAC;YACvB,GAAG,UAAU;YACb,GAAG;QACP;QAEA,cAAc;QACd,WAAW;IACf,GACA;QAAC;QAAY;KAAS;IAG1B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,CAAC;QACG,MAAM,eAAe,IAAI,mIAAA,CAAA,QAAK,CAAC;YAC3B,GAAG;YACH,GAAG,SAAS,CAAC;YACb,GAAG,SAAS,CAAC;YACb,GAAG,WAAW,CAAC;QACnB;QAEA,cAAc;QACd,WAAW;IACf,GACA;QAAC;QAAW,WAAW,CAAC;QAAE;KAAS;IAGvC,qBACI,8OAAC;QAAI,WAAW,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,uCAAuC;;0BAC3D,8OAAC,mKAAA,CAAA,WAAQ;gBACL,OAAO;gBACP,QAAQ;gBACR,YAAY;gBACZ,OAAO,IAAI,mIAAA,CAAA,QAAK,CAAC;oBAAE,GAAG,UAAU;oBAAE,GAAG;oBAAW,GAAG;gBAAE;gBACrD,aAAa,CAAC;oBACV,MAAM,eAAe,IAAI,mIAAA,CAAA,QAAK,CAAC;wBAC3B,GAAG;wBACH,GAAG,SAAS,CAAC;wBACb,GAAG,SAAS,CAAC;wBACb,GAAG,WAAW,CAAC;oBACnB;oBACA,cAAc;oBACd,cAAc;gBAClB;gBACA,UAAU;gBACV,aAAa,CAAC;oBACV,MAAM,eAAe,IAAI,mIAAA,CAAA,QAAK,CAAC;wBAC3B,GAAG;wBACH,GAAG,SAAS,CAAC;wBACb,GAAG,SAAS,CAAC;wBACb,GAAG,WAAW,CAAC;oBACnB;oBACA,cAAc;oBACd,cAAc;gBAClB;;;;;;0BAEJ,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;kCACG,cAAA,8OAAC,2KAAA,CAAA,UAAgB;4BACb,eAAe,CAAC;gCACZ,aAAa,SAAS,CAAC;gCACvB,cAAc;gCACd,cAAc;4BAClB;;;;;;;;;;;kCAGR,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,sKAAA,CAAA,cAAW;gCACR,WAAU;gCACV,QAAQ;gCACR,YAAY;gCACZ,WAAW;gCACX,OAAO,IAAI,mIAAA,CAAA,QAAK,CAAC;oCAAE,GAAG;oCAAW,GAAG;oCAAG,GAAG;gCAAE,GAAG,KAAK;gCACpD,YAAY;oCACR;oCACA;oCACA;oCACA;oCACA;oCACA;oCACA;iCACH;gCACD,OAAO;gCACP,aAAa,CAAC;oCACV,aAAa,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,GAAG;oCACpB,MAAM,WAAW,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,GAAG;oCAAG;oCACzD,cAAc;oCACd,cAAc;gCAClB;gCACA,UAAU;gCACV,aAAa,CAAC;oCACV,aAAa,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,GAAG;oCACpB,cAAc,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,GAAG;oCAAG;oCACtD,cAAc,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,MAAG,AAAD,EAAE,GAAG;oCAAG;gCAC1D;;;;;;0CAEJ,8OAAC,sKAAA,CAAA,cAAW;gCACR,WAAU;gCACV,QAAQ;gCACR,YAAY;gCACZ,WAAW;gCACX,OAAO,IAAI,mIAAA,CAAA,QAAK,CAAC;oCAAE,GAAG,UAAU;oCAAE,GAAG;gCAAE,GAAG,KAAK;gCAC/C,YAAY;oCACR,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE,GAAG;oCAAE,GAAG,KAAK;oCACxC,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE,GAAG;oCAAE,GAAG,KAAK;iCAC3C;gCACD,OAAO,WAAW,CAAC;gCACnB,aAAa,CAAC;oCACV,cAAc,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE;oCAAE;oCAC3C,cAAc,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE;oCAAE;gCAC/C;gCACA,UAAU,CAAC;oCACP,cAAc,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE;oCAAE;oCAC3C,WAAW,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE;oCAAE;gCAC5C;gCACA,aAAa,CAAC;oCACV,cAAc,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE;oCAAE;oCAC3C,cAAc,IAAI,mIAAA,CAAA,QAAK,CAAC;wCAAE,GAAG,UAAU;wCAAE;oCAAE;gCAC/C;;;;;;;;;;;;;;;;;;0BAIZ,8OAAC;gBAAU,OAAO;gBAAO,UAAU;gBAAU,aAAa;;;;;;;;;;;;AAGtE", "debugId": null}}, {"offset": {"line": 5787, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/color-picker/index.tsx"], "sourcesContent": ["export * from './checkPattern';\nexport * from './ColorPicker';\nexport * from './ColorSlider';\nexport * from './SVPicker';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 5814, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/input.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\n    return (\n        <input\n            type={type}\n            data-slot=\"input\"\n            className={cn(\n                'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n                'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\n                'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACvE,qBACI,8OAAC;QACG,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,mcACA,iFACA,0GACA;QAEH,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 5841, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/separator.tsx"], "sourcesContent": ["'use client';\n\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction Separator({\n    className,\n    orientation = 'horizontal',\n    decorative = true,\n    ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n    return (\n        <SeparatorPrimitive.Root\n            data-slot=\"separator-root\"\n            decorative={decorative}\n            orientation={orientation}\n            className={cn(\n                'bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px',\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nexport { Separator };\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAAA;AALA;;;;AAOA,SAAS,UAAU,EACf,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACgD;IACnD,qBACI,8OAAC,qKAAA,CAAA,OAAuB;QACpB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,kKACA;QAEH,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 5872, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/popover.tsx"], "sourcesContent": ["'use client';\n\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n    return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\n}\n\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n    return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\n}\n\nfunction PopoverContent({\n    className,\n    align = 'center',\n    sideOffset = 4,\n    ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n    return (\n        <PopoverPrimitive.Portal>\n            <PopoverPrimitive.Content\n                data-slot=\"popover-content\"\n                align={align}\n                sideOffset={sideOffset}\n                onWheel={(e) => e.stopPropagation()}\n                onTouchMove={(e) => e.stopPropagation()}\n                className={cn(\n                    'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\n                    className,\n                )}\n                {...props}\n            />\n        </PopoverPrimitive.Portal>\n    );\n}\n\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n    return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\n}\n\nexport { Popover, PopoverAnchor, PopoverContent, PopoverTrigger };\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAAA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC7E,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC/D;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACvF,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AAC1E;AAEA,SAAS,eAAe,EACpB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACiD;IACpD,qBACI,8OAAC,mKAAA,CAAA,SAAuB;kBACpB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACrB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,SAAS,CAAC,IAAM,EAAE,eAAe;YACjC,aAAa,CAAC,IAAM,EAAE,eAAe;YACrC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,keACA;YAEH,GAAG,KAAK;;;;;;;;;;;AAIzB;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACrF,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACxE", "debugId": null}}, {"offset": {"line": 5944, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/alert-dialog.tsx"], "sourcesContent": ["'use client';\n\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\nimport { buttonVariants } from './button';\n\nfunction AlertDialog({ ...props }: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\n    return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />;\n}\n\nfunction AlertDialogTrigger({\n    ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\n    return <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />;\n}\n\nfunction AlertDialogPortal({ ...props }: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\n    return <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />;\n}\n\nfunction AlertDialogOverlay({\n    className,\n    ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\n    return (\n        <AlertDialogPrimitive.Overlay\n            data-slot=\"alert-dialog-overlay\"\n            className={cn(\n                'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nfunction AlertDialogContent({\n    className,\n    ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\n    return (\n        <AlertDialogPortal>\n            <AlertDialogOverlay />\n            <AlertDialogPrimitive.Content\n                data-slot=\"alert-dialog-content\"\n                className={cn(\n                    'bg-background-onlook backdrop-blur-xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\n                    className,\n                )}\n                {...props}\n            />\n        </AlertDialogPortal>\n    );\n}\n\nfunction AlertDialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\n    return (\n        <div\n            data-slot=\"alert-dialog-header\"\n            className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\n            {...props}\n        />\n    );\n}\n\nfunction AlertDialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\n    return (\n        <div\n            data-slot=\"alert-dialog-footer\"\n            className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)}\n            {...props}\n        />\n    );\n}\n\nfunction AlertDialogTitle({\n    className,\n    ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\n    return (\n        <AlertDialogPrimitive.Title\n            data-slot=\"alert-dialog-title\"\n            className={cn('text-lg font-semibold', className)}\n            {...props}\n        />\n    );\n}\n\nfunction AlertDialogDescription({\n    className,\n    ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\n    return (\n        <AlertDialogPrimitive.Description\n            data-slot=\"alert-dialog-description\"\n            className={cn('text-muted-foreground text-sm', className)}\n            {...props}\n        />\n    );\n}\n\nfunction AlertDialogAction({\n    className,\n    ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\n    return <AlertDialogPrimitive.Action className={cn(buttonVariants(), className)} {...props} />;\n}\n\nfunction AlertDialogCancel({\n    className,\n    ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\n    return (\n        <AlertDialogPrimitive.Cancel\n            className={cn(buttonVariants({ variant: 'outline' }), className)}\n            {...props}\n        />\n    );\n}\n\nexport {\n    AlertDialog,\n    AlertDialogAction,\n    AlertDialogCancel,\n    AlertDialogContent,\n    AlertDialogDescription,\n    AlertDialogFooter,\n    AlertDialogHeader,\n    AlertDialogOverlay,\n    AlertDialogPortal,\n    AlertDialogTitle,\n    AlertDialogTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AAGA;AAAA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EAAE,GAAG,OAA+D;IACrF,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EACxB,GAAG,OACqD;IACxD,qBAAO,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AACnF;AAEA,SAAS,kBAAkB,EAAE,GAAG,OAAiE;IAC7F,qBAAO,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AACjF;AAEA,SAAS,mBAAmB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACI,8OAAC,2KAAA,CAAA,UAA4B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,0JACA;QAEH,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,mBAAmB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACI,8OAAC;;0BACG,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBACzB,aAAU;gBACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,uYACA;gBAEH,GAAG,KAAK;;;;;;;;;;;;AAIzB;AAEA,SAAS,kBAAkB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACI,8OAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,kBAAkB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACI,8OAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;QACvE,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,iBAAiB,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACI,8OAAC,2KAAA,CAAA,QAA0B;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,uBAAuB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACI,8OAAC,2KAAA,CAAA,cAAgC;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,kBAAkB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBAAO,8OAAC,2KAAA,CAAA,SAA2B;QAAC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD,KAAK;QAAa,GAAG,KAAK;;;;;;AAC7F;AAEA,SAAS,kBAAkB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACI,8OAAC,2KAAA,CAAA,SAA2B;QACxB,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 6105, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/dialog.tsx"], "sourcesContent": ["import * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { XIcon } from 'lucide-react';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction Dialog({ ...props }: React.ComponentProps<typeof DialogPrimitive.Root>) {\n    return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\n}\n\nfunction DialogTrigger({ ...props }: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n    return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\n}\n\nfunction DialogPortal({ ...props }: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n    return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />;\n}\n\nfunction DialogClose({ ...props }: React.ComponentProps<typeof DialogPrimitive.Close>) {\n    return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\n}\n\nfunction DialogOverlay({\n    className,\n    ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n    return (\n        <DialogPrimitive.Overlay\n            data-slot=\"dialog-overlay\"\n            className={cn(\n                'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nfunction DialogContent({\n    className,\n    children,\n    ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n    return (\n        <DialogPortal data-slot=\"dialog-portal\">\n            <DialogOverlay />\n            <DialogPrimitive.Content\n                data-slot=\"dialog-content\"\n                className={cn(\n                    'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\n                    className,\n                )}\n                {...props}\n            >\n                {children}\n                <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n                    <XIcon />\n                    <span className=\"sr-only\">Close</span>\n                </DialogPrimitive.Close>\n            </DialogPrimitive.Content>\n        </DialogPortal>\n    );\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\n    return (\n        <div\n            data-slot=\"dialog-header\"\n            className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\n            {...props}\n        />\n    );\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\n    return (\n        <div\n            data-slot=\"dialog-footer\"\n            className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)}\n            {...props}\n        />\n    );\n}\n\nfunction DialogTitle({ className, ...props }: React.ComponentProps<typeof DialogPrimitive.Title>) {\n    return (\n        <DialogPrimitive.Title\n            data-slot=\"dialog-title\"\n            className={cn('text-lg leading-none font-semibold', className)}\n            {...props}\n        />\n    );\n}\n\nfunction DialogDescription({\n    className,\n    ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n    return (\n        <DialogPrimitive.Description\n            data-slot=\"dialog-description\"\n            className={cn('text-muted-foreground text-sm', className)}\n            {...props}\n        />\n    );\n}\n\nexport {\n    Dialog,\n    DialogClose,\n    DialogContent,\n    DialogDescription,\n    DialogFooter,\n    DialogHeader,\n    DialogOverlay,\n    DialogPortal,\n    DialogTitle,\n    DialogTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AAGA;AAAA;;;;;AAEA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACrF,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,aAAa,EAAE,GAAG,OAA4D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACjF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,cAAc,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACI,8OAAC,kKAAA,CAAA,UAAuB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,0JACA;QAEH,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,cAAc,EACnB,SAAS,EACT,QAAQ,EACR,GAAG,OACgD;IACnD,qBACI,8OAAC;QAAa,aAAU;;0BACpB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,+WACA;gBAEH,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC7B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAK9C;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACI,8OAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACI,8OAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;QACvE,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC5F,qBACI,8OAAC,kKAAA,CAAA,QAAqB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,kBAAkB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACI,8OAAC,kKAAA,CAAA,cAA2B;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 6278, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/select.tsx"], "sourcesContent": ["import * as SelectPrimitive from '@radix-ui/react-select';\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction Select({ ...props }: React.ComponentProps<typeof SelectPrimitive.Root>) {\n    return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\n}\n\nfunction SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {\n    return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\n}\n\nfunction SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {\n    return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\n}\n\nfunction SelectTrigger({\n    className,\n    size = 'default',\n    children,\n    ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n    size?: 'sm' | 'default';\n}) {\n    return (\n        <SelectPrimitive.Trigger\n            data-slot=\"select-trigger\"\n            data-size={size}\n            className={cn(\n                \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n                className,\n            )}\n            {...props}\n        >\n            {children}\n            <SelectPrimitive.Icon asChild>\n                <ChevronDownIcon className=\"size-4 opacity-50\" />\n            </SelectPrimitive.Icon>\n        </SelectPrimitive.Trigger>\n    );\n}\n\nfunction SelectContent({\n    className,\n    children,\n    position = 'popper',\n    ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n    return (\n        <SelectPrimitive.Portal>\n            <SelectPrimitive.Content\n                data-slot=\"select-content\"\n                className={cn(\n                    'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\n                    position === 'popper' &&\n                        'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\n                    className,\n                )}\n                position={position}\n                {...props}\n            >\n                <SelectScrollUpButton />\n                <SelectPrimitive.Viewport\n                    className={cn(\n                        'p-1',\n                        position === 'popper' &&\n                            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1',\n                    )}\n                >\n                    {children}\n                </SelectPrimitive.Viewport>\n                <SelectScrollDownButton />\n            </SelectPrimitive.Content>\n        </SelectPrimitive.Portal>\n    );\n}\n\nfunction SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {\n    return (\n        <SelectPrimitive.Label\n            data-slot=\"select-label\"\n            className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\n            {...props}\n        />\n    );\n}\n\nfunction SelectItem({\n    className,\n    children,\n    ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n    return (\n        <SelectPrimitive.Item\n            data-slot=\"select-item\"\n            className={cn(\n                \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n                className,\n            )}\n            {...props}\n        >\n            <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n                <SelectPrimitive.ItemIndicator>\n                    <CheckIcon className=\"size-4\" />\n                </SelectPrimitive.ItemIndicator>\n            </span>\n            <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n        </SelectPrimitive.Item>\n    );\n}\n\nfunction SelectSeparator({\n    className,\n    ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n    return (\n        <SelectPrimitive.Separator\n            data-slot=\"select-separator\"\n            className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\n            {...props}\n        />\n    );\n}\n\nfunction SelectScrollUpButton({\n    className,\n    ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n    return (\n        <SelectPrimitive.ScrollUpButton\n            data-slot=\"select-scroll-up-button\"\n            className={cn('flex cursor-default items-center justify-center py-1', className)}\n            {...props}\n        >\n            <ChevronUpIcon className=\"size-4\" />\n        </SelectPrimitive.ScrollUpButton>\n    );\n}\n\nfunction SelectScrollDownButton({\n    className,\n    ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n    return (\n        <SelectPrimitive.ScrollDownButton\n            data-slot=\"select-scroll-down-button\"\n            className={cn('flex cursor-default items-center justify-center py-1', className)}\n            {...props}\n        >\n            <ChevronDownIcon className=\"size-4\" />\n        </SelectPrimitive.ScrollDownButton>\n    );\n}\n\nexport {\n    Select,\n    SelectContent,\n    SelectGroup,\n    SelectItem,\n    SelectLabel,\n    SelectScrollDownButton,\n    SelectScrollUpButton,\n    SelectSeparator,\n    SelectTrigger,\n    SelectValue,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AAAA;AAAA;AAGA;AAAA;;;;;AAEA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACjF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACjF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,cAAc,EACnB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGN;IACG,qBACI,8OAAC,kKAAA,CAAA,UAAuB;QACpB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,gzBACA;QAEH,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BACzB,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI3C;AAEA,SAAS,cAAc,EACnB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACgD;IACnD,qBACI,8OAAC,kKAAA,CAAA,SAAsB;kBACnB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,ijBACA,aAAa,YACT,mIACJ;YAEJ,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACrB,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,OACA,aAAa,YACT;8BAGP;;;;;;8BAEL,8OAAC;;;;;;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC5F,qBACI,8OAAC,kKAAA,CAAA,QAAqB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,WAAW,EAChB,SAAS,EACT,QAAQ,EACR,GAAG,OAC6C;IAChD,qBACI,8OAAC,kKAAA,CAAA,OAAoB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,6aACA;QAEH,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACZ,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC1B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG7B,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGvC;AAEA,SAAS,gBAAgB,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACI,8OAAC,kKAAA,CAAA,YAAyB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,qBAAqB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACI,8OAAC,kKAAA,CAAA,iBAA8B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAGrC;AAEA,SAAS,uBAAuB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACI,8OAAC,kKAAA,CAAA,mBAAgC;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGvC", "debugId": null}}, {"offset": {"line": 6503, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/resizable.tsx"], "sourcesContent": ["import { cn } from '@onlook/ui/utils';\nimport React, { useCallback, useEffect, useRef, useState } from 'react';\n\nexport function useResizable({\n    defaultWidth = 240,\n    minWidth = 200,\n    maxWidth = 600,\n    side = 'left',\n    forceWidth,\n}: {\n    defaultWidth?: number;\n    minWidth?: number;\n    maxWidth?: number;\n    side?: 'left' | 'right';\n    forceWidth?: number;\n}) {\n    const [width, setWidth] = useState(defaultWidth);\n    const [isAnimating, setIsAnimating] = useState(false);\n    const isDragging = useRef(false);\n    const startPos = useRef(0);\n    const startWidth = useRef(0);\n\n    // Effect to handle forced width changes\n    useEffect(() => {\n        if (forceWidth !== undefined) {\n            setIsAnimating(true);\n            setWidth(forceWidth);\n            // Reset animating after transition completes\n            const timer = setTimeout(() => setIsAnimating(false), 300);\n            return () => clearTimeout(timer);\n        }\n    }, [forceWidth]);\n\n    const handleMouseDown = useCallback(\n        (e: React.MouseEvent) => {\n            isDragging.current = true;\n            startPos.current = e.clientX;\n            startWidth.current = width;\n            document.body.style.cursor = 'col-resize';\n            document.body.style.userSelect = 'none';\n        },\n        [width],\n    );\n\n    const handleMouseMove = useCallback(\n        (e: MouseEvent) => {\n            if (!isDragging.current) return;\n\n            const delta = e.clientX - startPos.current;\n            let newWidth =\n                side === 'left' ? startWidth.current + delta : startWidth.current - delta;\n            newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));\n            setWidth(newWidth);\n        },\n        [side, minWidth, maxWidth],\n    );\n\n    const handleMouseUp = useCallback(() => {\n        isDragging.current = false;\n        document.body.style.cursor = '';\n        document.body.style.userSelect = '';\n    }, []);\n\n    useEffect(() => {\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n        return () => {\n            document.removeEventListener('mousemove', handleMouseMove);\n            document.removeEventListener('mouseup', handleMouseUp);\n        };\n    }, [handleMouseMove, handleMouseUp]);\n\n    return { width, handleMouseDown, isAnimating };\n}\n\n// Simplified component using the hook\ninterface ResizablePanelProps {\n    children: React.ReactNode;\n    side?: 'left' | 'right';\n    defaultWidth?: number;\n    minWidth?: number;\n    maxWidth?: number;\n    forceWidth?: number;\n    className?: string;\n    [key: string]: any;\n}\n\nexport const ResizablePanel: React.FC<ResizablePanelProps> = ({\n    children,\n    side = 'left',\n    defaultWidth = 240,\n    minWidth = 200,\n    maxWidth = 600,\n    forceWidth,\n    className,\n    ...props\n}) => {\n    const { width, handleMouseDown, isAnimating } = useResizable({\n        defaultWidth,\n        minWidth,\n        maxWidth,\n        side,\n        forceWidth,\n    });\n\n    return (\n        <div\n            style={{ width: `${width}px` }}\n            className={cn(\n                'h-full relative',\n                isAnimating && 'transition-[width] duration-300 ease-in-out',\n                side === 'left' ? 'left-0' : 'right-0',\n                className,\n            )}\n            {...props}\n        >\n            <div className=\"h-full\">{children}</div>\n            <div\n                className={cn(\n                    'absolute top-0 h-full w-1 cursor-col-resize transition-all',\n                    side === 'left' ? 'right-0' : 'left-0',\n                )}\n                onMouseDown={handleMouseDown}\n            />\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;;;;AAEO,SAAS,aAAa,EACzB,eAAe,GAAG,EAClB,WAAW,GAAG,EACd,WAAW,GAAG,EACd,OAAO,MAAM,EACb,UAAU,EAOb;IACG,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,eAAe,WAAW;YAC1B,eAAe;YACf,SAAS;YACT,6CAA6C;YAC7C,MAAM,QAAQ,WAAW,IAAM,eAAe,QAAQ;YACtD,OAAO,IAAM,aAAa;QAC9B;IACJ,GAAG;QAAC;KAAW;IAEf,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACG,WAAW,OAAO,GAAG;QACrB,SAAS,OAAO,GAAG,EAAE,OAAO;QAC5B,WAAW,OAAO,GAAG;QACrB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC7B,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;IACrC,GACA;QAAC;KAAM;IAGX,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACG,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,MAAM,QAAQ,EAAE,OAAO,GAAG,SAAS,OAAO;QAC1C,IAAI,WACA,SAAS,SAAS,WAAW,OAAO,GAAG,QAAQ,WAAW,OAAO,GAAG;QACxE,WAAW,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU;QACjD,SAAS;IACb,GACA;QAAC;QAAM;QAAU;KAAS;IAG9B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,WAAW,OAAO,GAAG;QACrB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC7B,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;IACrC,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO;YACH,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;QAC5C;IACJ,GAAG;QAAC;QAAiB;KAAc;IAEnC,OAAO;QAAE;QAAO;QAAiB;IAAY;AACjD;AAcO,MAAM,iBAAgD,CAAC,EAC1D,QAAQ,EACR,OAAO,MAAM,EACb,eAAe,GAAG,EAClB,WAAW,GAAG,EACd,WAAW,GAAG,EACd,UAAU,EACV,SAAS,EACT,GAAG,OACN;IACG,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,aAAa;QACzD;QACA;QACA;QACA;QACA;IACJ;IAEA,qBACI,8OAAC;QACG,OAAO;YAAE,OAAO,GAAG,MAAM,EAAE,CAAC;QAAC;QAC7B,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,mBACA,eAAe,+CACf,SAAS,SAAS,WAAW,WAC7B;QAEH,GAAG,KAAK;;0BAET,8OAAC;gBAAI,WAAU;0BAAU;;;;;;0BACzB,8OAAC;gBACG,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,8DACA,SAAS,SAAS,YAAY;gBAElC,aAAa;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 6618, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction Textarea({ className, ...props }: React.ComponentProps<'textarea'>) {\n    return (\n        <textarea\n            data-slot=\"textarea\"\n            className={cn(\n                'border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nexport { Textarea };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACvE,qBACI,8OAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,ucACA;QAEH,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 6644, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/chat/hooks/use-auto-scroll.tsx"], "sourcesContent": ["// @hidden\nimport { useCallback, useEffect, useRef, useState } from 'react';\n\ninterface ScrollState {\n    isAtBottom: boolean;\n    autoScrollEnabled: boolean;\n}\n\ninterface UseAutoScrollOptions {\n    offset?: number;\n    smooth?: boolean;\n    content?: React.ReactNode;\n    contentKey?: string;\n}\n\nexport function useAutoScroll(options: UseAutoScrollOptions = {}) {\n    const { offset = 20, smooth = false, content, contentKey } = options;\n    const scrollRef = useRef<HTMLDivElement>(null);\n    const lastContentHeight = useRef(0);\n    const userHasScrolled = useRef(false);\n\n    const [scrollState, setScrollState] = useState<ScrollState>({\n        isAtBottom: true,\n        autoScrollEnabled: true,\n    });\n\n    const checkIsAtBottom = useCallback(\n        (element: HTMLElement) => {\n            const { scrollTop, scrollHeight, clientHeight } = element;\n            const distanceToBottom = Math.abs(scrollHeight - scrollTop - clientHeight);\n            return distanceToBottom <= offset;\n        },\n        [offset],\n    );\n\n    const scrollToBottom = useCallback(\n        (instant?: boolean) => {\n            if (!scrollRef.current) return;\n\n            const targetScrollTop = scrollRef.current.scrollHeight - scrollRef.current.clientHeight;\n\n            if (instant) {\n                scrollRef.current.scrollTop = targetScrollTop;\n            } else {\n                scrollRef.current.scrollTo({\n                    top: targetScrollTop,\n                    behavior: smooth ? 'smooth' : 'auto',\n                });\n            }\n\n            setScrollState({\n                isAtBottom: true,\n                autoScrollEnabled: true,\n            });\n            userHasScrolled.current = false;\n        },\n        [smooth],\n    );\n\n    const handleScroll = useCallback(() => {\n        if (!scrollRef.current) return;\n\n        const atBottom = checkIsAtBottom(scrollRef.current);\n\n        setScrollState((prev) => ({\n            isAtBottom: atBottom,\n            // Re-enable auto-scroll if at the bottom\n            autoScrollEnabled: atBottom ? true : prev.autoScrollEnabled,\n        }));\n    }, [checkIsAtBottom]);\n\n    useEffect(() => {\n        const element = scrollRef.current;\n        if (!element) return;\n\n        element.addEventListener('scroll', handleScroll, { passive: true });\n        return () => element.removeEventListener('scroll', handleScroll);\n    }, [handleScroll]);\n\n    useEffect(() => {\n        const scrollElement = scrollRef.current;\n        if (!scrollElement) return;\n\n        const currentHeight = scrollElement.scrollHeight;\n        const hasNewContent = currentHeight !== lastContentHeight.current;\n\n        if (hasNewContent) {\n            if (scrollState.autoScrollEnabled) {\n                requestAnimationFrame(() => {\n                    scrollToBottom(lastContentHeight.current === 0);\n                });\n            }\n            lastContentHeight.current = currentHeight;\n        }\n    }, [content, contentKey, scrollState.autoScrollEnabled, scrollToBottom]);\n\n    useEffect(() => {\n        const element = scrollRef.current;\n        if (!element) return;\n\n        const resizeObserver = new ResizeObserver(() => {\n            if (scrollState.autoScrollEnabled) {\n                scrollToBottom(true);\n            }\n        });\n\n        resizeObserver.observe(element);\n        return () => resizeObserver.disconnect();\n    }, [scrollState.autoScrollEnabled, scrollToBottom]);\n\n    const disableAutoScroll = useCallback(() => {\n        const atBottom = scrollRef.current ? checkIsAtBottom(scrollRef.current) : false;\n\n        // Only disable if not at bottom\n        if (!atBottom) {\n            userHasScrolled.current = true;\n            setScrollState((prev) => ({\n                ...prev,\n                autoScrollEnabled: false,\n            }));\n        }\n    }, [checkIsAtBottom]);\n\n    return {\n        scrollRef,\n        isAtBottom: scrollState.isAtBottom,\n        autoScrollEnabled: scrollState.autoScrollEnabled,\n        scrollToBottom: () => scrollToBottom(false),\n        disableAutoScroll,\n    };\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;AACV;;AAcO,SAAS,cAAc,UAAgC,CAAC,CAAC;IAC5D,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG;IAC7D,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QACxD,YAAY;QACZ,mBAAmB;IACvB;IAEA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACG,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;QAClD,MAAM,mBAAmB,KAAK,GAAG,CAAC,eAAe,YAAY;QAC7D,OAAO,oBAAoB;IAC/B,GACA;QAAC;KAAO;IAGZ,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,CAAC;QACG,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,MAAM,kBAAkB,UAAU,OAAO,CAAC,YAAY,GAAG,UAAU,OAAO,CAAC,YAAY;QAEvF,IAAI,SAAS;YACT,UAAU,OAAO,CAAC,SAAS,GAAG;QAClC,OAAO;YACH,UAAU,OAAO,CAAC,QAAQ,CAAC;gBACvB,KAAK;gBACL,UAAU,SAAS,WAAW;YAClC;QACJ;QAEA,eAAe;YACX,YAAY;YACZ,mBAAmB;QACvB;QACA,gBAAgB,OAAO,GAAG;IAC9B,GACA;QAAC;KAAO;IAGZ,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,MAAM,WAAW,gBAAgB,UAAU,OAAO;QAElD,eAAe,CAAC,OAAS,CAAC;gBACtB,YAAY;gBACZ,yCAAyC;gBACzC,mBAAmB,WAAW,OAAO,KAAK,iBAAiB;YAC/D,CAAC;IACL,GAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,UAAU,OAAO;QACjC,IAAI,CAAC,SAAS;QAEd,QAAQ,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QACjE,OAAO,IAAM,QAAQ,mBAAmB,CAAC,UAAU;IACvD,GAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,gBAAgB,UAAU,OAAO;QACvC,IAAI,CAAC,eAAe;QAEpB,MAAM,gBAAgB,cAAc,YAAY;QAChD,MAAM,gBAAgB,kBAAkB,kBAAkB,OAAO;QAEjE,IAAI,eAAe;YACf,IAAI,YAAY,iBAAiB,EAAE;gBAC/B,sBAAsB;oBAClB,eAAe,kBAAkB,OAAO,KAAK;gBACjD;YACJ;YACA,kBAAkB,OAAO,GAAG;QAChC;IACJ,GAAG;QAAC;QAAS;QAAY,YAAY,iBAAiB;QAAE;KAAe;IAEvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,UAAU,OAAO;QACjC,IAAI,CAAC,SAAS;QAEd,MAAM,iBAAiB,IAAI,eAAe;YACtC,IAAI,YAAY,iBAAiB,EAAE;gBAC/B,eAAe;YACnB;QACJ;QAEA,eAAe,OAAO,CAAC;QACvB,OAAO,IAAM,eAAe,UAAU;IAC1C,GAAG;QAAC,YAAY,iBAAiB;QAAE;KAAe;IAElD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,MAAM,WAAW,UAAU,OAAO,GAAG,gBAAgB,UAAU,OAAO,IAAI;QAE1E,gCAAgC;QAChC,IAAI,CAAC,UAAU;YACX,gBAAgB,OAAO,GAAG;YAC1B,eAAe,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,mBAAmB;gBACvB,CAAC;QACL;IACJ,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACH;QACA,YAAY,YAAY,UAAU;QAClC,mBAAmB,YAAY,iBAAiB;QAChD,gBAAgB,IAAM,eAAe;QACrC;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 6766, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/chat/chat-message-list.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '../../utils';\nimport { Button } from '../button';\nimport { Icons } from '../icons';\nimport { useAutoScroll } from './hooks/use-auto-scroll';\n\ninterface ChatMessageListProps extends React.HTMLAttributes<HTMLDivElement> {\n    smooth?: boolean;\n    contentKey: string;\n}\n\nconst ChatMessageList = React.forwardRef<HTMLDivElement, ChatMessageListProps>(\n    ({ className, children, smooth = true, contentKey, ...props }, _ref) => {\n        const { scrollRef, isAtBottom, autoScrollEnabled, scrollToBottom, disableAutoScroll } =\n            useAutoScroll({\n                smooth,\n                content: children,\n                contentKey,\n            });\n\n        return (\n            <div className=\"relative h-full\">\n                <div\n                    className={cn('flex flex-col w-full h-full overflow-y-auto', className)}\n                    ref={scrollRef}\n                    onWheel={disableAutoScroll}\n                    onTouchMove={disableAutoScroll}\n                    {...props}\n                >\n                    <div className=\"flex flex-col gap-2\">{children}</div>\n                </div>\n\n                {!isAtBottom && (\n                    <Button\n                        onClick={() => {\n                            scrollToBottom();\n                        }}\n                        size=\"icon\"\n                        variant=\"outline\"\n                        className=\"absolute dark:bg-background bottom-2 left-1/2 transform -translate-x-1/2 inline-flex rounded-full\"\n                        aria-label=\"Scroll to bottom\"\n                    >\n                        <Icons.ArrowDown className=\"h-4 w-4\" />\n                    </Button>\n                )}\n            </div>\n        );\n    },\n);\n\nChatMessageList.displayName = 'ChatMessageList';\n\nexport { ChatMessageList };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAOA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,EAAE;IAC3D,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,iBAAiB,EAAE,cAAc,EAAE,iBAAiB,EAAE,GACjF,CAAA,GAAA,8KAAA,CAAA,gBAAa,AAAD,EAAE;QACV;QACA,SAAS;QACT;IACJ;IAEJ,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBACG,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;gBAC7D,KAAK;gBACL,SAAS;gBACT,aAAa;gBACZ,GAAG,KAAK;0BAET,cAAA,8OAAC;oBAAI,WAAU;8BAAuB;;;;;;;;;;;YAGzC,CAAC,4BACE,8OAAC,8IAAA,CAAA,SAAM;gBACH,SAAS;oBACL;gBACJ;gBACA,MAAK;gBACL,SAAQ;gBACR,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC,sJAAA,CAAA,QAAK,CAAC,SAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK/C;AAGJ,gBAAgB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 6845, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/collapsible.tsx"], "sourcesContent": ["import * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\n\nfunction Collapsible({ ...props }: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\n    return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />;\n}\n\nfunction CollapsibleTrigger({\n    ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\n    return <CollapsiblePrimitive.CollapsibleTrigger data-slot=\"collapsible-trigger\" {...props} />;\n}\n\nfunction CollapsibleContent({\n    ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\n    return <CollapsiblePrimitive.CollapsibleContent data-slot=\"collapsible-content\" {...props} />;\n}\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent };\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAEA,SAAS,YAAY,EAAE,GAAG,OAA+D;IACrF,qBAAO,8OAAC,uKAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACvE;AAEA,SAAS,mBAAmB,EACxB,GAAG,OACgE;IACnE,qBAAO,8OAAC,uKAAA,CAAA,qBAAuC;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAC7F;AAEA,SAAS,mBAAmB,EACxB,GAAG,OACgE;IACnE,qBAAO,8OAAC,uKAAA,CAAA,qBAAuC;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAC7F", "debugId": null}}, {"offset": {"line": 6891, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/avatar.tsx"], "sourcesContent": ["'use client';\n\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n    return (\n        <AvatarPrimitive.Root\n            data-slot=\"avatar\"\n            className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\n            {...props}\n        />\n    );\n}\n\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n    return (\n        <AvatarPrimitive.Image\n            data-slot=\"avatar-image\"\n            className={cn('aspect-square size-full', className)}\n            {...props}\n        />\n    );\n}\n\nfunction AvatarFallback({\n    className,\n    ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n    return (\n        <AvatarPrimitive.Fallback\n            data-slot=\"avatar-fallback\"\n            className={cn(\n                'bg-muted flex size-full items-center justify-center rounded-full',\n                className,\n            )}\n            {...props}\n        />\n    );\n}\n\nexport { Avatar, AvatarFallback, AvatarImage };\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAAA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACtF,qBACI,8OAAC,kKAAA,CAAA,OAAoB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC5F,qBACI,8OAAC,kKAAA,CAAA,QAAqB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGrB;AAEA,SAAS,eAAe,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACI,8OAAC,kKAAA,CAAA,WAAwB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,oEACA;QAEH,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 6944, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/ui/src/components/progress.tsx"], "sourcesContent": ["import * as ProgressPrimitive from '@radix-ui/react-progress';\nimport * as React from 'react';\n\nimport { cn } from '../utils';\n\nfunction Progress({\n    className,\n    value,\n    ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n    return (\n        <ProgressPrimitive.Root\n            data-slot=\"progress\"\n            className={cn(\n                'bg-primary/20 relative h-2 w-full overflow-hidden rounded-full',\n                className,\n            )}\n            {...props}\n        >\n            <ProgressPrimitive.Indicator\n                data-slot=\"progress-indicator\"\n                className=\"bg-primary h-full w-full flex-1 transition-all\"\n                style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n            />\n        </ProgressPrimitive.Root>\n    );\n}\n\nexport { Progress };\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AAAA;;;;AAEA,SAAS,SAAS,EACd,SAAS,EACT,KAAK,EACL,GAAG,OAC+C;IAClD,qBACI,8OAAC,oKAAA,CAAA,OAAsB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,kEACA;QAEH,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YACxB,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAI1E", "debugId": null}}]}