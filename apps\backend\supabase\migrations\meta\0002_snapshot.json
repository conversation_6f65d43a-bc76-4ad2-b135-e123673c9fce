{"id": "cf650e40-2b28-43b0-bfcd-fdd3603f7f88", "prevId": "7fab2c6f-cf80-420e-bad4-3d6065d58b9c", "version": "7", "dialect": "postgresql", "tables": {"public.canvas": {"name": "canvas", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "scale": {"name": "scale", "type": "numeric", "primaryKey": false, "notNull": true}, "x": {"name": "x", "type": "numeric", "primaryKey": false, "notNull": true}, "y": {"name": "y", "type": "numeric", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"canvas_project_id_projects_id_fk": {"name": "canvas_project_id_projects_id_fk", "tableFrom": "canvas", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.frames": {"name": "frames", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "canvas_id": {"name": "canvas_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "frame_type", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "x": {"name": "x", "type": "numeric", "primaryKey": false, "notNull": true}, "y": {"name": "y", "type": "numeric", "primaryKey": false, "notNull": true}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": true}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"frames_canvas_id_canvas_id_fk": {"name": "frames_canvas_id_canvas_id_fk", "tableFrom": "frames", "tableTo": "canvas", "columnsFrom": ["canvas_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.conversations": {"name": "conversations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"conversations_project_id_projects_id_fk": {"name": "conversations_project_id_projects_id_fk", "tableFrom": "conversations", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "conversation_id": {"name": "conversation_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "role": {"name": "role", "type": "role", "primaryKey": false, "notNull": true}, "applied": {"name": "applied", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "snapshots": {"name": "snapshots", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "parts": {"name": "parts", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}}, "indexes": {}, "foreignKeys": {"messages_conversation_id_conversations_id_fk": {"name": "messages_conversation_id_conversations_id_fk", "tableFrom": "messages", "tableTo": "conversations", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "sandbox_id": {"name": "sandbox_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "sandbox_url": {"name": "sandbox_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "preview_img": {"name": "preview_img", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.user_settings": {"name": "user_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "auto_apply_code": {"name": "auto_apply_code", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "expand_code_blocks": {"name": "expand_code_blocks", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_suggestions": {"name": "show_suggestions", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_mini_chat": {"name": "show_mini_chat", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"user_settings_user_id_users_id_fk": {"name": "user_settings_user_id_users_id_fk", "tableFrom": "user_settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_settings_user_id_unique": {"name": "user_settings_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {"users_id_users_id_fk": {"name": "users_id_users_id_fk", "tableFrom": "users", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.user_projects": {"name": "user_projects", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_projects_user_id_users_id_fk": {"name": "user_projects_user_id_users_id_fk", "tableFrom": "user_projects", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "user_projects_project_id_projects_id_fk": {"name": "user_projects_project_id_projects_id_fk", "tableFrom": "user_projects", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"user_projects_user_id_project_id_pk": {"name": "user_projects_user_id_project_id_pk", "columns": ["user_id", "project_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}}, "enums": {"public.frame_type": {"name": "frame_type", "schema": "public", "values": ["web"]}, "public.role": {"name": "role", "schema": "public", "values": ["user", "assistant", "system"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}