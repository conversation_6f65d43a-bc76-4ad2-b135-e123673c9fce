---
title: Modes
description: Understanding Onlook's different editing modes
---

# Onlook Modes

Onlook provides different modes to help you work efficiently based on your current task. Each mode offers specialized tools and features tailored to specific workflows.

![Modes Overview](/images/modes-overview.png)
*Switch between modes to access different tools and capabilities*

## Available Modes

### Visual Edit Mode

Visual Edit Mode allows you to manipulate elements directly in the browser, similar to design tools like Figma. In this mode, you can:

- Select and resize elements
- Drag and drop components
- Modify text and images
- Apply styles using a visual interface

This mode is perfect for designers who want to make quick visual adjustments without touching code.

![Visual Edit Mode](/images/visual-edit-mode.png)
*Make visual changes directly in the browser*

### Code Mode

Code Mode gives you direct access to the underlying React and CSS code. Use this mode when you need to:

- Make precise code adjustments
- Add custom logic
- Implement complex functionality
- Debug specific issues

This mode is ideal for developers or designers who are comfortable with code.

![Code Mode](/images/code-mode.png)
*Access and edit the underlying code directly*

### AI Mode

AI Mode leverages artificial intelligence to help you create and modify components. In this mode, you can:

- Generate new components with natural language descriptions
- Convert existing designs to code
- Get suggestions for improvements
- Fix issues with AI assistance

This mode is perfect for quickly ideating or making changes that would be time-consuming to do manually.

![AI Mode](/images/ai-mode.png)
*Let AI help you create and modify your components*

## Switching Between Modes

You can switch between modes using the mode selector in the top toolbar. Your changes are synchronized across all modes, so you can switch freely without losing any work.

## Best Practices

For the best Onlook experience:

- Use **Visual Edit Mode** for layout and styling adjustments
- Switch to **Code Mode** for adding custom logic and functionality
- Use **AI Mode** to accelerate development or overcome challenges

Remember that all modes are working with the same underlying project, just providing different ways to interact with it.
