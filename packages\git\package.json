{"name": "@onlook/git", "description": "A git library for Onlook", "main": "./src/index.ts", "type": "module", "module": "src/index.ts", "types": "src/index.ts", "version": "0.0.0", "private": true, "repository": {"type": "git", "url": "https://github.com/onlook-dev/onlook.git"}, "scripts": {"clean": "rm -rf node_modules", "lint": "eslint --fix .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "keywords": ["onlook", "git"], "author": {"name": "Onlook", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://onlook.com", "devDependencies": {"@onlook/typescript": "*"}, "dependencies": {"globby": "^14.1.0", "isomorphic-git": "^1.29.0"}}