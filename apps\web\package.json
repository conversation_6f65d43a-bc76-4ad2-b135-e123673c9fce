{"name": "@onlook/web", "module": "index.ts", "type": "module", "private": true, "scripts": {"dev": "bun --filter '@onlook/web-client' --filter '@onlook/web-server' --filter '@onlook/web-preload' --filter '@onlook/web-template' dev", "dev:client": "bun --filter @onlook/web-client dev", "dev:server": "bun --filter @onlook/web-server dev", "dev:preload": "bun --filter @onlook/web-preload dev", "build:client": "bun --filter @onlook/web-client build", "start:client": "bun --filter @onlook/web-client start", "docker:up": "docker compose up", "docker:build": "docker compose build", "docker:down": "docker compose down", "docker:restart": "docker compose down && docker compose up", "csb:build": "bunx @codesandbox/sdk build ./template --ports 8084"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {}}