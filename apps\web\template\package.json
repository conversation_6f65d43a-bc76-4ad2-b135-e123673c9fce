{"name": "@onlook/web-template", "version": "0.1.0", "private": true, "scripts": {"dev": "PORT=8084 next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@radix-ui/react-slot": "^1.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.438.0", "next": "14.2.26", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^15.1.6", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "typescript": "^5"}}