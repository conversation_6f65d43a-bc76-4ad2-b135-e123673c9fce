---
title: User Guide
description: How to use Onlook effectively
---

# Onlook User Guide

This guide will help you get the most out of Onlook as a designer working with React and TailwindCSS projects.

## Basic Concepts

Onlook provides a visual interface for editing React components while automatically generating the corresponding code. Key concepts include:

- **Visual Editor**: Edit your components visually, similar to design tools like Figma
- **Live Code Sync**: Changes you make visually are immediately reflected in the code
- **AI Assistance**: Use AI to help generate and modify components
- **Layers Panel**: Navigate the component hierarchy
- **Style Editor**: Modify Tailwind styles through a visual interface

## Sections

<Cards>
  <Card title="UI Overview" href="/docs/user-guide/ui-overview" />
  <Card title="Editing Projects" href="/docs/user-guide/editing-projects" />
  <Card title="AI Features" href="/docs/user-guide/ai-features" />
  <Card title="Styling" href="/docs/user-guide/styling" />
  <Card title="Deployment" href="/docs/user-guide/deployment" />
</Cards>
