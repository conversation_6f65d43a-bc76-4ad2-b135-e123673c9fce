{"productName": "Onlook", "name": "@onlook/web-client", "version": "0.1.0", "private": true, "homepage": "https://onlook.com", "description": "The first-ever devtool for designers", "license": "Apache-2.0", "author": {"name": "Onlook", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/onlook-dev/onlook.git"}, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@ai-sdk/openai": "^1.3.20", "@ai-sdk/react": "^1.2.9", "@codemirror/lang-css": "^6.2.0", "@codemirror/lang-html": "^6.4.7", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.1.7", "@codesandbox/sdk": "^1.1.6", "@onlook/constants": "*", "@onlook/db": "*", "@onlook/email": "*", "@onlook/fonts": "*", "@onlook/models": "*", "@onlook/parser": "*", "@onlook/penpal": "*", "@onlook/rpc": "*", "@onlook/ui": "*", "@onlook/utility": "*", "@onlook/growth": "*", "@radix-ui/react-slot": "^1.1.2", "@supabase/ssr": "^0.6.1", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@uiw/codemirror-extensions-basic-setup": "^4.23.10", "@uiw/react-codemirror": "^4.23.10", "@xterm/xterm": "^5.5.0", "ai": "^4.3.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "culori": "^4.0.1", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "flexsearch": "^0.8.160", "freestyle-sandboxes": "^0.0.77", "localforage": "^1.10.0", "lucide-react": "^0.486.0", "mobx-react-lite": "^4.1.0", "motion": "^12.6.3", "next": "^15.2.3", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "penpal": "^7.0.4", "posthog-js": "^1.246.0", "posthog-node": "^4.17.2", "prosemirror-commands": "^1.7.1", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.2", "react": "^19.0.0", "react-arborist": "^3.4.3", "react-codemirror-merge": "4.23.10", "react-dom": "^19.0.0", "react-hotkeys-hook": "^5.0.1", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "shiki": "^3.2.2", "strip-ansi": "^7.1.0", "superjson": "^2.2.1", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "url-join": "^5.0.0", "use-resize-observer": "^9.1.0", "uuid": "^11.1.0", "webfontloader": "^1.6.28", "zod": "^3.24.3"}, "devDependencies": {"@types/culori": "^4.0.0", "@types/webfontloader": "^1.6.38", "@eslint/eslintrc": "^3.3.1", "@onlook/typescript": "*", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.15", "type-fest": "^4.41.0", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.2"}}