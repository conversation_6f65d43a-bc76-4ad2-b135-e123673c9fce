---
title: Onlook Documentation
description: Build real web apps fast using visual editing with Onlook
---

# Onlook Documentation

Welcome to the Onlook documentation. Onlook is an open-source "Cursor for Designers" that lets you visually edit React websites or webapps and write your changes back to code in real-time.

![Onlook Hero Banner](/images/hero-banner.png)
*Onlook empowers designers to make direct edits to React and TailwindCSS projects*

## For Users

<Cards>
  <Card title="Getting Started" href="/docs" />
  <Card title="User Guide" href="/docs/user-guide" />
  <Card title="Features" href="/docs/features" />
  <Card title="Tutorials" href="/docs/tutorials" />
  <Card title="FAQ" href="/docs/faq" />
</Cards>

## For Contributors

If you're a developer looking to contribute to Onlook or understand its architecture:

<Cards>
  <Card title="Running Locally" href="/docs/developer/running-locally" />
  <Card title="Architecture" href="/docs/developer/architecture" />
  <Card title="Contributing" href="/docs/developer/contributing" />
</Cards>
