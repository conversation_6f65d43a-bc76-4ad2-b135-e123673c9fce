{"name": "@onlook/ui", "description": "A tailwind ui library for Onlook", "version": "0.0.0", "private": true, "type": "module", "main": "src/index.ts", "module": "src/index.ts", "types": "src/index.ts", "repository": {"type": "git", "url": "https://github.com/onlook-dev/onlook.git"}, "files": ["tokens.ts", "tailwind.config.ts", "postcss.config.js", "globals.css"], "scripts": {"clean": "rm -rf node_modules", "lint": "eslint --fix .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "keywords": ["onlook", "ui"], "author": {"name": "Onlook", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://onlook.com", "devDependencies": {"@onlook/typescript": "*", "@onlook/utility": "*", "@types/color-namer": "^1.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.5.4"}, "exports": {"./*": "./src/components/*.tsx", "./icons": "./src/components/icons/index.tsx", "./color-picker": "./src/components/color-picker/index.tsx", "./tokens": "./tokens.ts", "./tailwind.config": "./tailwind.config.ts", "./postcss": "./postcss.config.js", "./globals.css": "./src/globals.css", "./utils": "./src/utils/index.ts", "./hooks": "./src/hooks/index.ts"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "color-namer": "^1.4.0", "css-color-names": "^1.0.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.503.0", "motion": "^11.15.0", "next-themes": "^0.4.6", "parse-css-color": "^0.2.1", "prosemirror-view": "^1.34.2", "react-day-picker": "8.10.1", "react-hook-form": "^7.56.1", "react-merge-refs": "^2.1.1", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.3", "sonner": "^2.0.4", "tailwind-merge": "^2.3.0", "tailwind-styled-components": "^2.2.0", "tailwindcss": "^4.1.0", "tailwindcss-animate": "^1.0.7", "use-eye-dropper": "^1.6.4", "vaul": "^1.1.2", "zod": "^3.24.3"}}