export const colors = {
    amber: {
        DEFAULT: '#B99000',
        100: '#FFF0BC',
        200: '#F6C100',
        300: '#D7A800',
        400: '#B99000',
        500: '#9B7900',
        600: '#7F6300',
        700: '#644E00',
        800: '#493900',
        900: '#312600',
        950: '#211A00',
    },
    black: {
        DEFAULT: '#000000',
        30: '#0000004d',
        60: '#00000099',
        85: '#000000d9',
    },
    purple: {
        DEFAULT: '#C174FF',
        100: '#F7EDFF',
        200: '#E1BBFF',
        300: '#D198FF',
        400: '#C174FF',
        500: '#AE4AFF',
        600: '#920EFF',
        700: '#7300D1',
        800: '#57009E',
        900: '#3D006E',
        950: '#29004B',
    },
    red: {
        DEFAULT: '#FF5B82',
        100: '#FFECF1',
        200: '#FFB3C6',
        300: '#FF8BA7',
        400: '#FF5B82',
        500: '#FA003C',
        600: '#CE0032',
        700: '#A40028',
        800: '#7C001E',
        900: '#560015',
        950: '#3E000F',
    },
    blue: {
        DEFAULT: '#90D1FF',
        100: '#E3F3FF',
        200: '#90D1FF',
        300: '#53B8FF',
        400: '#109BFF',
        500: '#0081DE',
        600: '#006AB5',
        700: '#00538F',
        800: '#003E69',
        900: '#002A48',
        950: '#001B2E',
    },
    gray: {
        DEFAULT: '#494949',
        50: '#ffffff',
        100: '#c7c7c7',
        200: '#acacac',
        300: '#929292',
        400: '#787878',
        500: '#606060',
        600: '#494949',
        700: '#333333',
        800: '#1f1f1f',
        900: '#1a1a1a',
    },
    green: {
        DEFAULT: '#00591e',
        100: '#d8ffe5',
        200: '#00e14b',
        300: '#00c441',
        400: '#00a838',
        500: '#008c2f',
        600: '#007226',
        700: '#00591e',
        800: '#004116',
        900: '#002a0e',
        950: '#00240c',
    },
    teal: {
        DEFAULT: '#00deba',
        100: '#cbfff6',
        200: '#00deba',
        300: '#00c1a2',
        400: '#00a68b',
        500: '#008b74',
        600: '#00715e',
        700: '#005849',
        800: '#004036',
        900: '#002a23',
        950: '#00211c',
    },
    yellow: {
        DEFAULT: '#644e00',
        100: '#fff0bc',
        200: '#f6c100',
        300: '#d7a800',
        400: '#b99000',
        500: '#9b7900',
        600: '#7f6300',
        700: '#644e00',
        800: '#493900',
        900: '#312600',
        950: '#211a00',
    },
};

export const fontSize: Record<
    string,
    [string, { lineHeight: string; letterSpacing?: string; fontWeight: string | number }]
> = {
    title1: [
        '2.25rem',
        {
            lineHeight: 'auto',
            fontWeight: 'normal',
        },
    ],
    title2: [
        '1.5rem',
        {
            lineHeight: 'normal',
            fontWeight: 'normal',
        },
    ],
    title3: [
        '1.25rem',
        {
            lineHeight: 'normal',
            fontWeight: 'normal',
        },
    ],
    largePlus: [
        '1rem',
        {
            lineHeight: '1.4rem',
            fontWeight: '400',
            letterSpacing: '0.02rem',
        },
    ],
    large: [
        '1rem',
        {
            lineHeight: '1.4rem',
            fontWeight: 'normal',
            letterSpacing: '0.02rem',
        },
    ],
    regularPlus: [
        '0.9375rem',
        {
            lineHeight: '1.4rem',
            fontWeight: '400',
            letterSpacing: '0.02rem',
        },
    ],
    regular: [
        '0.9375rem',
        {
            lineHeight: '1.4rem',
            fontWeight: '300',
            letterSpacing: '0.02rem',
        },
    ],
    smallPlus: [
        '0.8125rem',
        {
            lineHeight: '1.3rem',
            fontWeight: '500',
            letterSpacing: '0.00rem',
        },
    ],
    small: [
        '0.8125rem',
        {
            lineHeight: '1.3rem',
            fontWeight: '300',
            letterSpacing: '0.00rem',
        },
    ],
    miniPlus: [
        '0.75rem',
        {
            lineHeight: 'normal',
            fontWeight: '500',
            letterSpacing: '0.01rem',
        },
    ],
    mini: [
        '0.75rem',
        {
            lineHeight: 'normal',
            fontWeight: 'normal',
            letterSpacing: '0.01rem',
        },
    ],
    microPlus: [
        '0.6875rem',
        {
            lineHeight: 'normal',
            fontWeight: '500',
            letterSpacing: '0.005rem',
        },
    ],
    micro: [
        '0.6875rem',
        {
            lineHeight: 'normal',
            fontWeight: 'normal',
            letterSpacing: '0.005rem',
        },
    ],
};
