---
title: Get Started
description: Learn how to get started with Onlook
---

# Welcome to Onlook

Onlook is a "Cursor for Designers" that enables designers to make live edits to React and TailwindCSS projects directly within the browser DOM. It provides a seamless integration between design and development.

![Onlook Interface](/images/onlook-interface.png)
*Onlook's intuitive design interface lets you edit React components visually*

## What is Onlook?

<div className="relative h-[500px] overflow-hidden">
  <iframe
    className="absolute top-0 left-0 h-full w-full"
    src="https://www.youtube.com/embed/RjFBUkVfy1E"
    title="Cursor for Designers"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
    allowFullScreen
  ></iframe>
</div>

Onlook lets you:

- Edit designs directly in a browser-like interface
- Have changes automatically reflected in the code
- Use AI assistance to generate and modify code
- Manage project styling through a theme system
- Deploy websites directly from the application

With Onlook, designers can take control of implementation, reducing the gap between design and development. Create production-ready code without requiring deep coding knowledge.

## Getting Started

Ready to dive in? Here's how to get started with Onlook:

<Cards>
  <Card title="User Guide" href="/docs/user-guide" />
  <Card title="Features" href="/docs/features" />
  <Card title="Tutorials" href="/docs/tutorials" />
</Cards>

## For Contributors

Want to contribute to Onlook? Check out our developer documentation:

<Cards>
  <Card title="Running Locally" href="/docs/developer/running-locally" />
  <Card title="Architecture" href="/docs/developer/architecture" />
  <Card title="Contributing" href="/docs/developer/contributing" />
</Cards>

## Need Help?

If you run into any issues or have questions, you can:

- Join our [Discord community](https://discord.gg/hERDfFZCsH)
- Check out the [FAQ](/docs/faq)
- Follow us on [Twitter](https://twitter.com/onlookdev)
