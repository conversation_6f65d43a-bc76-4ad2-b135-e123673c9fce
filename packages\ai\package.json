{"name": "@onlook/ai", "description": "A AI library for Onlook", "main": "./src/index.ts", "type": "module", "module": "src/index.ts", "types": "src/index.ts", "version": "0.0.0", "private": true, "repository": {"type": "git", "url": "https://github.com/onlook-dev/onlook.git"}, "scripts": {"clean": "rm -rf node_modules", "lint": "eslint --fix .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "keywords": ["onlook", "AI"], "author": {"name": "Onlook", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://onlook.com", "devDependencies": {"@onlook/typescript": "*"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "ai": "^4.3.10", "diff-match-patch": "^1.0.5", "fg": "^0.0.3", "marked": "^15.0.7", "openai": "^4.103.0"}}