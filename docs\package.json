{"name": "@onlook/docs", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"next": "15.3.1", "react": "^19.1.0", "react-dom": "^19.1.0", "fumadocs-ui": "15.3.1", "fumadocs-core": "15.5.0", "fumadocs-mdx": "11.6.3", "@onlook/ui": "*"}, "devDependencies": {"@types/node": "22.15.12", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "typescript": "^5.8.3", "@types/mdx": "^2.0.13", "@tailwindcss/postcss": "^4.1.5", "tailwindcss": "^4.1.5", "postcss": "^8.5.3", "eslint": "^8", "eslint-config-next": "15.3.1"}}