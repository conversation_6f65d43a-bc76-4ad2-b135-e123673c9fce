{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/bottom-bar/terminal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport '@xterm/xterm/css/xterm.css';\r\n\r\nimport { useEditorEngine } from '@/components/store/editor';\r\nimport { cn } from '@onlook/ui/utils';\r\nimport { type ITheme } from '@xterm/xterm';\r\nimport { observer } from 'mobx-react-lite';\r\nimport { useTheme } from 'next-themes';\r\nimport { memo, useEffect, useRef } from 'react';\r\n\r\ninterface TerminalProps {\r\n    hidden: boolean;\r\n    terminalSessionId: string;\r\n}\r\n\r\nconst TERMINAL_THEME: Record<'LIGHT' | 'DARK', ITheme> = {\r\n    LIGHT: {\r\n        background: '#ffffff',\r\n        foreground: '#2d2d2d',\r\n        cursor: '#333333',\r\n        cursorAccent: '#ffffff',\r\n        black: '#2d2d2d',\r\n        red: '#d64646',\r\n        green: '#4e9a06',\r\n        yellow: '#c4a000',\r\n        blue: '#3465a4',\r\n        magenta: '#75507b',\r\n        cyan: '#06989a',\r\n        white: '#d3d7cf',\r\n        brightBlack: '#555753',\r\n        brightRed: '#ef2929',\r\n        brightGreen: '#8ae234',\r\n        brightYellow: '#fce94f',\r\n        brightBlue: '#729fcf',\r\n        brightMagenta: '#ad7fa8',\r\n        brightCyan: '#34e2e2',\r\n        brightWhite: '#eeeeec',\r\n        selectionBackground: '#bfbfbf',\r\n    },\r\n    DARK: {}, // Use default dark theme\r\n};\r\n\r\nexport const Terminal = memo(observer(({ hidden = false, terminalSessionId }: TerminalProps) => {\r\n    const editorEngine = useEditorEngine();\r\n    const terminalSession = editorEngine.sandbox.session.getTerminalSession(terminalSessionId);\r\n    const containerRef = useRef<HTMLDivElement>(null);\r\n    const { theme } = useTheme();\r\n\r\n    // Mount xterm to DOM\r\n    useEffect(() => {\r\n        if (!containerRef.current || !terminalSession?.xterm) return;\r\n        // Only open if not already attached\r\n        if (!terminalSession.xterm.element || terminalSession.xterm.element.parentElement !== containerRef.current) {\r\n            terminalSession.xterm.open(containerRef.current);\r\n        }\r\n        return () => {\r\n            // Detach xterm from DOM on unmount (but do not dispose)\r\n            if (\r\n                terminalSession.xterm.element &&\r\n                containerRef.current &&\r\n                terminalSession.xterm.element.parentElement === containerRef.current\r\n            ) {\r\n                containerRef.current.innerHTML = '';\r\n            }\r\n        };\r\n    }, [terminalSessionId, terminalSession, containerRef]);\r\n\r\n    useEffect(() => {\r\n        if (terminalSession?.xterm) {\r\n            terminalSession.xterm.options.theme = theme === 'light' ? TERMINAL_THEME.LIGHT : TERMINAL_THEME.DARK;\r\n        }\r\n    }, [theme, terminalSession]);\r\n\r\n    useEffect(() => {\r\n        if (!hidden && terminalSession?.xterm) {\r\n            setTimeout(() => {\r\n                terminalSession.xterm?.focus();\r\n            }, 100);\r\n        }\r\n    }, [hidden, terminalSession]);\r\n\r\n    return (\r\n        <div\r\n            ref={containerRef}\r\n            className={cn(\r\n                'h-full w-full p-2 transition-opacity duration-200',\r\n                hidden ? 'opacity-0' : 'opacity-100 delay-300',\r\n            )}\r\n        />\r\n    );\r\n}));\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAAA;AAEA;AAAA;AACA;AACA;;;;;AATA;;;;;;;;AAgBA,MAAM,iBAAmD;IACrD,OAAO;QACH,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,OAAO;QACP,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,MAAM;QACN,OAAO;QACP,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,eAAe;QACf,YAAY;QACZ,aAAa;QACb,qBAAqB;IACzB;IACA,MAAM,CAAC;AACX;AAEO,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EAAE,SAAS,KAAK,EAAE,iBAAiB,EAAiB;IACvF,MAAM,eAAe,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,kBAAkB,aAAa,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC;IACxE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEzB,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,iBAAiB,OAAO;QACtD,oCAAoC;QACpC,IAAI,CAAC,gBAAgB,KAAK,CAAC,OAAO,IAAI,gBAAgB,KAAK,CAAC,OAAO,CAAC,aAAa,KAAK,aAAa,OAAO,EAAE;YACxG,gBAAgB,KAAK,CAAC,IAAI,CAAC,aAAa,OAAO;QACnD;QACA,OAAO;YACH,wDAAwD;YACxD,IACI,gBAAgB,KAAK,CAAC,OAAO,IAC7B,aAAa,OAAO,IACpB,gBAAgB,KAAK,CAAC,OAAO,CAAC,aAAa,KAAK,aAAa,OAAO,EACtE;gBACE,aAAa,OAAO,CAAC,SAAS,GAAG;YACrC;QACJ;IACJ,GAAG;QAAC;QAAmB;QAAiB;KAAa;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,iBAAiB,OAAO;YACxB,gBAAgB,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,UAAU,UAAU,eAAe,KAAK,GAAG,eAAe,IAAI;QACxG;IACJ,GAAG;QAAC;QAAO;KAAgB;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,UAAU,iBAAiB,OAAO;YACnC,WAAW;gBACP,gBAAgB,KAAK,EAAE;YAC3B,GAAG;QACP;IACJ,GAAG;QAAC;QAAQ;KAAgB;IAE5B,qBACI,8OAAC;QACG,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,qDACA,SAAS,cAAc;;;;;;AAIvC", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/bottom-bar/terminal-area.tsx"], "sourcesContent": ["import { useEditorEngine } from '@/components/store/editor';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@onlook/ui/tabs';\r\nimport { Tooltip, TooltipContent, TooltipTrigger } from '@onlook/ui/tooltip';\r\nimport { cn } from '@onlook/ui/utils';\r\nimport { observer } from 'mobx-react-lite';\r\nimport { motion } from 'motion/react';\r\nimport { useState } from 'react';\r\nimport { Terminal } from './terminal';\r\n\r\nexport const TerminalArea = observer(({ children }: { children: React.ReactNode }) => {\r\n    const editorEngine = useEditorEngine();\r\n    const terminalSessions = editorEngine.sandbox.session.terminalSessions;\r\n    const activeSessionId = editorEngine.sandbox.session.activeTerminalSessionId;\r\n\r\n    const [terminalHidden, setTerminalHidden] = useState(true);\r\n\r\n    if (!terminalSessions.size) {\r\n        return (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n                Initializing Sandbox...\r\n            </div>\r\n        )\r\n    }\r\n\r\n    return (\r\n        <>\r\n            {terminalHidden ? (\r\n                <motion.div layout className=\"flex items-center gap-1\">\r\n                    {children}\r\n                    <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                            <button\r\n                                onClick={() => setTerminalHidden(!terminalHidden)}\r\n                                className=\"h-9 w-9 flex items-center justify-center hover:text-foreground-hover text-foreground-tertiary hover:bg-accent rounded-md\"\r\n                            >\r\n                                <Icons.Terminal />\r\n                            </button>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>Toggle Terminal</TooltipContent>\r\n                    </Tooltip>\r\n                </motion.div>\r\n            ) : (\r\n                <motion.div\r\n                    layout\r\n                    className=\"flex items-center justify-between w-full mb-1\"\r\n                >\r\n                    <motion.span\r\n                        initial={{ opacity: 0, x: 10 }}\r\n                        animate={{ opacity: 1, x: 0 }}\r\n                        exit={{ opacity: 0, x: -10 }}\r\n                        transition={{ duration: 0.7 }}\r\n                        className=\"text-small text-foreground-secondary ml-2 select-none\"\r\n                    >\r\n                        Terminal\r\n                    </motion.span>\r\n                    <div className=\"flex items-center gap-1\">\r\n                        <motion.div layout>{/* <RunButton /> */}</motion.div>\r\n                        <Tooltip>\r\n                            <TooltipTrigger asChild>\r\n                                <button\r\n                                    onClick={() => setTerminalHidden(!terminalHidden)}\r\n                                    className=\"h-9 w-9 flex items-center justify-center hover:text-foreground-hover text-foreground-tertiary hover:bg-accent rounded-lg\"\r\n                                >\r\n                                    <Icons.ChevronDown />\r\n                                </button>\r\n                            </TooltipTrigger>\r\n                            <TooltipContent>Toggle Terminal</TooltipContent>\r\n                        </Tooltip>\r\n                    </div>\r\n                </motion.div>\r\n            )}\r\n            <div\r\n                className={cn(\r\n                    'bg-background rounded-lg transition-all duration-300 flex flex-col items-center justify-between h-full overflow-auto',\r\n                    terminalHidden ? 'h-0 w-0 invisible' : 'h-[22rem] w-[37rem]',\r\n                )}\r\n            >\r\n                <Tabs defaultValue={'cli'} value={activeSessionId} onValueChange={(value) => editorEngine.sandbox.session.activeTerminalSessionId = value}\r\n                    className=\"w-full h-full\">\r\n                    <TabsList className=\"w-full h-8 rounded-none border-b border-border\">\r\n                        {Array.from(terminalSessions).map(([id, terminal]) => (\r\n                            <TabsTrigger key={id} value={id} className=\"flex-1\">{terminal.name}</TabsTrigger>\r\n                        ))}\r\n                    </TabsList>\r\n                    <div className=\"w-full h-full overflow-auto\">\r\n                        {Array.from(terminalSessions).map(([id]) => (\r\n                            <TabsContent key={id} forceMount value={id} className=\"h-full\" hidden={activeSessionId !== id}>\r\n                                <Terminal hidden={terminalHidden} terminalSessionId={id} />\r\n                            </TabsContent>\r\n                        ))}\r\n                    </div>\r\n                </Tabs>\r\n            </div >\r\n        </>\r\n    );\r\n});"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAEO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EAAE,QAAQ,EAAiC;IAC7E,MAAM,eAAe,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,mBAAmB,aAAa,OAAO,CAAC,OAAO,CAAC,gBAAgB;IACtE,MAAM,kBAAkB,aAAa,OAAO,CAAC,OAAO,CAAC,uBAAuB;IAE5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,IAAI,CAAC,iBAAiB,IAAI,EAAE;QACxB,qBACI,8OAAC;YAAI,WAAU;sBAA0C;;;;;;IAIjE;IAEA,qBACI;;YACK,+BACG,8OAAC,2OAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,MAAM;gBAAC,WAAU;;oBACxB;kCACD,8OAAC,+IAAA,CAAA,UAAO;;0CACJ,8OAAC,+IAAA,CAAA,iBAAc;gCAAC,OAAO;0CACnB,cAAA,8OAAC;oCACG,SAAS,IAAM,kBAAkB,CAAC;oCAClC,WAAU;8CAEV,cAAA,8OAAC,sJAAA,CAAA,QAAK,CAAC,QAAQ;;;;;;;;;;;;;;;0CAGvB,8OAAC,+IAAA,CAAA,iBAAc;0CAAC;;;;;;;;;;;;;;;;;qCAIxB,8OAAC,2OAAA,CAAA,SAAM,CAAC,GAAG;gBACP,MAAM;gBACN,WAAU;;kCAEV,8OAAC,2OAAA,CAAA,SAAM,CAAC,IAAI;wBACR,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCACb;;;;;;kCAGD,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,2OAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,MAAM;;;;;;0CAClB,8OAAC,+IAAA,CAAA,UAAO;;kDACJ,8OAAC,+IAAA,CAAA,iBAAc;wCAAC,OAAO;kDACnB,cAAA,8OAAC;4CACG,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;sDAEV,cAAA,8OAAC,sJAAA,CAAA,QAAK,CAAC,WAAW;;;;;;;;;;;;;;;kDAG1B,8OAAC,+IAAA,CAAA,iBAAc;kDAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAKhC,8OAAC;gBACG,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,wHACA,iBAAiB,sBAAsB;0BAG3C,cAAA,8OAAC,4IAAA,CAAA,OAAI;oBAAC,cAAc;oBAAO,OAAO;oBAAiB,eAAe,CAAC,QAAU,aAAa,OAAO,CAAC,OAAO,CAAC,uBAAuB,GAAG;oBAChI,WAAU;;sCACV,8OAAC,4IAAA,CAAA,WAAQ;4BAAC,WAAU;sCACf,MAAM,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,IAAI,SAAS,iBAC7C,8OAAC,4IAAA,CAAA,cAAW;oCAAU,OAAO;oCAAI,WAAU;8CAAU,SAAS,IAAI;mCAAhD;;;;;;;;;;sCAG1B,8OAAC;4BAAI,WAAU;sCACV,MAAM,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,GAAG,iBACnC,8OAAC,4IAAA,CAAA,cAAW;oCAAU,UAAU;oCAAC,OAAO;oCAAI,WAAU;oCAAS,QAAQ,oBAAoB;8CACvF,cAAA,8OAAC,qMAAA,CAAA,WAAQ;wCAAC,QAAQ;wCAAgB,mBAAmB;;;;;;mCADvC;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/bottom-bar/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Hotkey } from '@/components/hotkey';\r\nimport { useEditorEngine } from '@/components/store/editor';\r\nimport { transKeys } from '@/i18n/keys';\r\nimport { EditorMode } from '@onlook/models';\r\nimport { HotkeyLabel } from '@onlook/ui/hotkey-label';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport { ToggleGroup, ToggleGroupItem } from '@onlook/ui/toggle-group';\r\nimport { Tooltip, TooltipContent, TooltipTrigger } from '@onlook/ui/tooltip';\r\nimport { observer } from 'mobx-react-lite';\r\nimport { AnimatePresence, motion } from 'motion/react';\r\nimport { useTranslations } from 'next-intl';\r\nimport { TerminalArea } from './terminal-area';\r\n\r\nconst TOOLBAR_ITEMS = ({ t }: { t: ReturnType<typeof useTranslations> }) => [\r\n    {\r\n        mode: EditorMode.DESIGN,\r\n        icon: Icons.CursorArrow,\r\n        hotkey: Hotkey.SELECT,\r\n        disabled: false,\r\n        draggable: false,\r\n        label: t(transKeys.editor.toolbar.tools.select.name),\r\n        tooltip: t(transKeys.editor.toolbar.tools.select.tooltip),\r\n    },\r\n    {\r\n        mode: EditorMode.PAN,\r\n        icon: Icons.Hand,\r\n        hotkey: Hotkey.PAN,\r\n        disabled: false,\r\n        draggable: false,\r\n        label: t(transKeys.editor.toolbar.tools.pan.name),\r\n        tooltip: t(transKeys.editor.toolbar.tools.pan.tooltip),\r\n    },\r\n    {\r\n        mode: EditorMode.INSERT_DIV,\r\n        icon: Icons.Square,\r\n        hotkey: Hotkey.INSERT_DIV,\r\n        disabled: false,\r\n        draggable: true,\r\n        label: t(transKeys.editor.toolbar.tools.insertDiv.name),\r\n        tooltip: t(transKeys.editor.toolbar.tools.insertDiv.tooltip),\r\n    },\r\n    {\r\n        mode: EditorMode.INSERT_TEXT,\r\n        icon: Icons.Text,\r\n        hotkey: Hotkey.INSERT_TEXT,\r\n        disabled: false,\r\n        draggable: true,\r\n        label: t(transKeys.editor.toolbar.tools.insertText.name),\r\n        tooltip: t(transKeys.editor.toolbar.tools.insertText.tooltip),\r\n    },\r\n];\r\n\r\nexport const BottomBar = observer(() => {\r\n    const t = useTranslations();\r\n    const editorEngine = useEditorEngine();\r\n    const toolbarItems = TOOLBAR_ITEMS({ t });\r\n\r\n    return (\r\n        <AnimatePresence mode=\"wait\">\r\n            {editorEngine.state.editorMode !== EditorMode.PREVIEW && (\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    exit={{ opacity: 0, y: 20 }}\r\n                    className=\"flex flex-col border p-1 px-1.5 bg-background-secondary/85 dark:bg-background/85 backdrop-blur rounded-lg drop-shadow-xl\"\r\n                    transition={{\r\n                        type: 'spring',\r\n                        bounce: 0.1,\r\n                        duration: 0.4,\r\n                        stiffness: 200,\r\n                        damping: 25,\r\n                    }}\r\n                >\r\n                    <TerminalArea>\r\n                        <ToggleGroup\r\n                            type=\"single\"\r\n                            value={editorEngine.state.editorMode}\r\n                            onValueChange={(value) => {\r\n                                if (value) {\r\n                                    editorEngine.state.editorMode = value as EditorMode;\r\n                                }\r\n                            }}\r\n                        >\r\n                            {toolbarItems.map((item) => (\r\n                                <Tooltip key={item.mode}>\r\n                                    <TooltipTrigger asChild>\r\n                                        <ToggleGroupItem\r\n                                            value={item.mode}\r\n                                            aria-label={item.hotkey.description}\r\n                                            disabled={item.disabled}\r\n                                            className=\"hover:text-foreground-hover text-foreground-tertiary\"\r\n                                        >\r\n                                            <item.icon />\r\n                                        </ToggleGroupItem>\r\n                                    </TooltipTrigger>\r\n                                    <TooltipContent>\r\n                                        <HotkeyLabel hotkey={item.hotkey} />\r\n                                    </TooltipContent>\r\n                                </Tooltip>\r\n                            ))}\r\n                        </ToggleGroup>\r\n                    </TerminalArea>\r\n                </motion.div>\r\n            )}\r\n        </AnimatePresence>\r\n    );\r\n});\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;;;;AAbA;;;;;;;;;;;;;;AAeA,MAAM,gBAAgB,CAAC,EAAE,CAAC,EAA6C,GAAK;QACxE;YACI,MAAM,4IAAA,CAAA,aAAU,CAAC,MAAM;YACvB,MAAM,sJAAA,CAAA,QAAK,CAAC,WAAW;YACvB,QAAQ,oJAAA,CAAA,SAAM,CAAC,MAAM;YACrB,UAAU;YACV,WAAW;YACX,OAAO,EAAE,4IAAA,CAAA,YAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;YACnD,SAAS,EAAE,4IAAA,CAAA,YAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;QAC5D;QACA;YACI,MAAM,4IAAA,CAAA,aAAU,CAAC,GAAG;YACpB,MAAM,sJAAA,CAAA,QAAK,CAAC,IAAI;YAChB,QAAQ,oJAAA,CAAA,SAAM,CAAC,GAAG;YAClB,UAAU;YACV,WAAW;YACX,OAAO,EAAE,4IAAA,CAAA,YAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;YAChD,SAAS,EAAE,4IAAA,CAAA,YAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO;QACzD;QACA;YACI,MAAM,4IAAA,CAAA,aAAU,CAAC,UAAU;YAC3B,MAAM,sJAAA,CAAA,QAAK,CAAC,MAAM;YAClB,QAAQ,oJAAA,CAAA,SAAM,CAAC,UAAU;YACzB,UAAU;YACV,WAAW;YACX,OAAO,EAAE,4IAAA,CAAA,YAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI;YACtD,SAAS,EAAE,4IAAA,CAAA,YAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO;QAC/D;QACA;YACI,MAAM,4IAAA,CAAA,aAAU,CAAC,WAAW;YAC5B,MAAM,sJAAA,CAAA,QAAK,CAAC,IAAI;YAChB,QAAQ,oJAAA,CAAA,SAAM,CAAC,WAAW;YAC1B,UAAU;YACV,WAAW;YACX,OAAO,EAAE,4IAAA,CAAA,YAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI;YACvD,SAAS,EAAE,4IAAA,CAAA,YAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO;QAChE;KACH;AAEM,MAAM,YAAY,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE;IAC9B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,eAAe,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,eAAe,cAAc;QAAE;IAAE;IAEvC,qBACI,8OAAC,0OAAA,CAAA,kBAAe;QAAC,MAAK;kBACjB,aAAa,KAAK,CAAC,UAAU,KAAK,4IAAA,CAAA,aAAU,CAAC,OAAO,kBACjD,8OAAC,2OAAA,CAAA,SAAM,CAAC,GAAG;YACP,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC1B,WAAU;YACV,YAAY;gBACR,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,WAAW;gBACX,SAAS;YACb;sBAEA,cAAA,8OAAC,6MAAA,CAAA,eAAY;0BACT,cAAA,8OAAC,uJAAA,CAAA,cAAW;oBACR,MAAK;oBACL,OAAO,aAAa,KAAK,CAAC,UAAU;oBACpC,eAAe,CAAC;wBACZ,IAAI,OAAO;4BACP,aAAa,KAAK,CAAC,UAAU,GAAG;wBACpC;oBACJ;8BAEC,aAAa,GAAG,CAAC,CAAC,qBACf,8OAAC,+IAAA,CAAA,UAAO;;8CACJ,8OAAC,+IAAA,CAAA,iBAAc;oCAAC,OAAO;8CACnB,cAAA,8OAAC,uJAAA,CAAA,kBAAe;wCACZ,OAAO,KAAK,IAAI;wCAChB,cAAY,KAAK,MAAM,CAAC,WAAW;wCACnC,UAAU,KAAK,QAAQ;wCACvB,WAAU;kDAEV,cAAA,8OAAC,KAAK,IAAI;;;;;;;;;;;;;;;8CAGlB,8OAAC,+IAAA,CAAA,iBAAc;8CACX,cAAA,8OAAC,uJAAA,CAAA,cAAW;wCAAC,QAAQ,KAAK,MAAM;;;;;;;;;;;;2BAZ1B,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;AAsBvD", "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/right-click-menu/index.tsx"], "sourcesContent": ["import { Hotkey } from '@/components/hotkey';\r\nimport { IDE } from '@/components/ide';\r\nimport { useEditorEngine } from '@/components/store/editor';\r\nimport { EditorTabValue } from '@onlook/models/editor';\r\nimport type { DomElement } from '@onlook/models/element';\r\nimport { DEFAULT_IDE } from '@onlook/models/ide';\r\nimport {\r\n    ContextMenu,\r\n    ContextMenuContent,\r\n    ContextMenuItem,\r\n    ContextMenuSeparator,\r\n    ContextMenuTrigger,\r\n} from '@onlook/ui/context-menu';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport { Kbd } from '@onlook/ui/kbd';\r\nimport { cn } from '@onlook/ui/utils';\r\nimport { observer } from 'mobx-react-lite';\r\nimport { useEffect, useState } from 'react';\r\n\r\ninterface RightClickMenuProps {\r\n    children: React.ReactNode;\r\n}\r\n\r\ninterface MenuItem {\r\n    label: string;\r\n    action: () => void;\r\n    hotkey?: Hotkey;\r\n    children?: MenuItem[];\r\n    icon: React.ReactNode;\r\n    disabled?: boolean;\r\n    destructive?: boolean;\r\n}\r\n\r\nexport const RightClickMenu = observer(({ children }: RightClickMenuProps) => {\r\n    const editorEngine = useEditorEngine();\r\n    const [menuItems, setMenuItems] = useState<MenuItem[][]>([]);\r\n    const ide = IDE.fromType(DEFAULT_IDE);\r\n\r\n    useEffect(() => {\r\n        updateMenuItems();\r\n    }, [\r\n        editorEngine.elements.selected,\r\n        editorEngine.ast.mappings.layers,\r\n        editorEngine.frames.selected,\r\n    ]);\r\n\r\n    const TOOL_ITEMS: MenuItem[] = [\r\n        {\r\n            label: 'Add to AI Chat',\r\n            action: () => {\r\n                editorEngine.state.rightPanelTab = EditorTabValue.CHAT;\r\n                editorEngine.chat.focusChatInput();\r\n            },\r\n            icon: <Icons.MagicWand className=\"mr-2 h-4 w-4\" />,\r\n            hotkey: Hotkey.ADD_AI_CHAT,\r\n            disabled: !editorEngine.elements.selected.length,\r\n        },\r\n        {\r\n            label: 'New AI Chat',\r\n            action: () => {\r\n                editorEngine.state.rightPanelTab = EditorTabValue.CHAT;\r\n                editorEngine.chat.conversation.startNewConversation();\r\n                editorEngine.chat.focusChatInput();\r\n            },\r\n            icon: <Icons.MagicWand className=\"mr-2 h-4 w-4\" />,\r\n            hotkey: Hotkey.NEW_AI_CHAT,\r\n        },\r\n    ];\r\n\r\n    const GROUP_ITEMS: MenuItem[] = [\r\n        {\r\n            label: 'Group',\r\n            action: () => editorEngine.group.groupSelectedElements(),\r\n            icon: <Icons.Box className=\"mr-2 h-4 w-4\" />,\r\n            // disabled: !editorEngine.group.canGroupElements(),\r\n            hotkey: Hotkey.GROUP,\r\n        },\r\n        {\r\n            label: 'Ungroup',\r\n            action: () => editorEngine.group.ungroupSelectedElement(),\r\n            icon: <Icons.Group className=\"mr-2 h-4 w-4\" />,\r\n            disabled: !editorEngine.group.canUngroupElement(),\r\n            hotkey: Hotkey.UNGROUP,\r\n        },\r\n    ];\r\n\r\n    const EDITING_ITEMS: MenuItem[] = [\r\n        {\r\n            label: 'Edit text',\r\n            action: () => editorEngine.text.editSelectedElement(),\r\n            icon: <Icons.Pencil className=\"mr-2 h-4 w-4\" />,\r\n            hotkey: Hotkey.ENTER,\r\n        },\r\n        {\r\n            label: 'Copy',\r\n            action: () => editorEngine.copy.copy(),\r\n            icon: <Icons.Clipboard className=\"mr-2 h-4 w-4\" />,\r\n            hotkey: Hotkey.COPY,\r\n        },\r\n        {\r\n            label: 'Paste',\r\n            action: () => editorEngine.copy.paste(),\r\n            icon: <Icons.ClipboardCopy className=\"mr-2 h-4 w-4\" />,\r\n            hotkey: Hotkey.PASTE,\r\n        },\r\n        {\r\n            label: 'Cut',\r\n            action: () => editorEngine.copy.cut(),\r\n            icon: <Icons.Scissors className=\"mr-2 h-4 w-4\" />,\r\n            hotkey: Hotkey.CUT,\r\n        },\r\n        {\r\n            label: 'Duplicate',\r\n            action: () => editorEngine.copy.duplicate(),\r\n            icon: <Icons.Copy className=\"mr-2 h-4 w-4\" />,\r\n            hotkey: Hotkey.DUPLICATE,\r\n        },\r\n        {\r\n            label: 'Delete',\r\n            action: () => editorEngine.elements.delete(),\r\n            icon: <Icons.Trash className=\"mr-2 h-4 w-4\" />,\r\n            hotkey: Hotkey.DELETE,\r\n            destructive: true,\r\n        },\r\n    ];\r\n\r\n    const WINDOW_ITEMS: MenuItem[] = [\r\n        {\r\n            label: 'Duplicate',\r\n            action: () => editorEngine.frames.duplicateSelected(),\r\n            icon: <Icons.Copy className=\"mr-2 h-4 w-4\" />,\r\n            hotkey: Hotkey.DUPLICATE,\r\n        },\r\n        {\r\n            label: 'Delete',\r\n            action: () => editorEngine.frames.deleteSelected(),\r\n            icon: <Icons.Trash className=\"mr-2 h-4 w-4\" />,\r\n            hotkey: Hotkey.DELETE,\r\n            destructive: true,\r\n            disabled: !editorEngine.frames.canDelete(),\r\n        },\r\n    ];\r\n\r\n    const updateMenuItems = () => {\r\n        let instance: string | null = null;\r\n        let root: string | null = null;\r\n\r\n        if (editorEngine.elements.selected.length > 0) {\r\n            const element: DomElement | undefined = editorEngine.elements.selected[0];\r\n            if (element) {\r\n                instance = element.instanceId;\r\n                root = element.oid;\r\n            }\r\n        }\r\n        let menuItems: MenuItem[][] = [];\r\n\r\n        if (editorEngine.frames.selected.length > 0) {\r\n            menuItems = [WINDOW_ITEMS];\r\n        } else {\r\n            const updatedToolItems = [\r\n                instance !== null && {\r\n                    label: 'View instance code',\r\n                    action: () => viewSource(instance),\r\n                    icon: <Icons.ComponentInstance className=\"mr-2 h-4 w-4\" />,\r\n                },\r\n                {\r\n                    label: `View ${instance ? 'component' : 'element'} in ${ide.displayName}`,\r\n                    disabled: !root,\r\n                    action: () => viewSource(root),\r\n                    icon: instance ? (\r\n                        <Icons.Component className=\"mr-2 h-4 w-4\" />\r\n                    ) : (\r\n                        <Icons.ExternalLink className=\"mr-2 h-4 w-4\" />\r\n                    ),\r\n                },\r\n                ...TOOL_ITEMS,\r\n            ].filter(Boolean) as MenuItem[];\r\n\r\n            menuItems = [updatedToolItems, GROUP_ITEMS, EDITING_ITEMS];\r\n        }\r\n\r\n        setMenuItems(menuItems);\r\n    };\r\n\r\n    function viewSource(oid: string | null) {\r\n        if (!oid) {\r\n            console.error('No oid found');\r\n            return;\r\n        }\r\n        editorEngine.code.viewCodeBlock(oid);\r\n    }\r\n\r\n    return (\r\n        <ContextMenu>\r\n            <ContextMenuTrigger>{children}</ContextMenuTrigger>\r\n            <ContextMenuContent className=\"w-64 bg-background/95 backdrop-blur-lg\">\r\n                {menuItems.map((group, groupIndex) => (\r\n                    <div key={groupIndex}>\r\n                        {group.map((item) => (\r\n                            <ContextMenuItem\r\n                                key={item.label}\r\n                                onClick={item.action}\r\n                                disabled={item.disabled}\r\n                                className=\"cursor-pointer\"\r\n                            >\r\n                                <span\r\n                                    className={cn(\r\n                                        'flex w-full items-center gap-1',\r\n                                        item.destructive && 'text-red',\r\n                                    )}\r\n                                >\r\n                                    <span>{item.icon}</span>\r\n                                    <span>{item.label}</span>\r\n                                    <span className=\"ml-auto\">\r\n                                        {item.hotkey && <Kbd>{item.hotkey.readableCommand}</Kbd>}\r\n                                    </span>\r\n                                </span>\r\n                            </ContextMenuItem>\r\n                        ))}\r\n                        {groupIndex < menuItems.length - 1 && <ContextMenuSeparator />}\r\n                    </div>\r\n                ))}\r\n            </ContextMenuContent>\r\n        </ContextMenu>\r\n    );\r\n});\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;;;;;;;;;;;;;;;AAgBO,MAAM,iBAAiB,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EAAE,QAAQ,EAAuB;IACrE,MAAM,eAAe,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC3D,MAAM,MAAM,iJAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,yIAAA,CAAA,cAAW;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN;IACJ,GAAG;QACC,aAAa,QAAQ,CAAC,QAAQ;QAC9B,aAAa,GAAG,CAAC,QAAQ,CAAC,MAAM;QAChC,aAAa,MAAM,CAAC,QAAQ;KAC/B;IAED,MAAM,aAAyB;QAC3B;YACI,OAAO;YACP,QAAQ;gBACJ,aAAa,KAAK,CAAC,aAAa,GAAG,4IAAA,CAAA,iBAAc,CAAC,IAAI;gBACtD,aAAa,IAAI,CAAC,cAAc;YACpC;YACA,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,SAAS;gBAAC,WAAU;;;;;;YACjC,QAAQ,oJAAA,CAAA,SAAM,CAAC,WAAW;YAC1B,UAAU,CAAC,aAAa,QAAQ,CAAC,QAAQ,CAAC,MAAM;QACpD;QACA;YACI,OAAO;YACP,QAAQ;gBACJ,aAAa,KAAK,CAAC,aAAa,GAAG,4IAAA,CAAA,iBAAc,CAAC,IAAI;gBACtD,aAAa,IAAI,CAAC,YAAY,CAAC,oBAAoB;gBACnD,aAAa,IAAI,CAAC,cAAc;YACpC;YACA,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,SAAS;gBAAC,WAAU;;;;;;YACjC,QAAQ,oJAAA,CAAA,SAAM,CAAC,WAAW;QAC9B;KACH;IAED,MAAM,cAA0B;QAC5B;YACI,OAAO;YACP,QAAQ,IAAM,aAAa,KAAK,CAAC,qBAAqB;YACtD,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,GAAG;gBAAC,WAAU;;;;;;YAC3B,oDAAoD;YACpD,QAAQ,oJAAA,CAAA,SAAM,CAAC,KAAK;QACxB;QACA;YACI,OAAO;YACP,QAAQ,IAAM,aAAa,KAAK,CAAC,sBAAsB;YACvD,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,KAAK;gBAAC,WAAU;;;;;;YAC7B,UAAU,CAAC,aAAa,KAAK,CAAC,iBAAiB;YAC/C,QAAQ,oJAAA,CAAA,SAAM,CAAC,OAAO;QAC1B;KACH;IAED,MAAM,gBAA4B;QAC9B;YACI,OAAO;YACP,QAAQ,IAAM,aAAa,IAAI,CAAC,mBAAmB;YACnD,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAU;;;;;;YAC9B,QAAQ,oJAAA,CAAA,SAAM,CAAC,KAAK;QACxB;QACA;YACI,OAAO;YACP,QAAQ,IAAM,aAAa,IAAI,CAAC,IAAI;YACpC,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,SAAS;gBAAC,WAAU;;;;;;YACjC,QAAQ,oJAAA,CAAA,SAAM,CAAC,IAAI;QACvB;QACA;YACI,OAAO;YACP,QAAQ,IAAM,aAAa,IAAI,CAAC,KAAK;YACrC,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,aAAa;gBAAC,WAAU;;;;;;YACrC,QAAQ,oJAAA,CAAA,SAAM,CAAC,KAAK;QACxB;QACA;YACI,OAAO;YACP,QAAQ,IAAM,aAAa,IAAI,CAAC,GAAG;YACnC,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,QAAQ;gBAAC,WAAU;;;;;;YAChC,QAAQ,oJAAA,CAAA,SAAM,CAAC,GAAG;QACtB;QACA;YACI,OAAO;YACP,QAAQ,IAAM,aAAa,IAAI,CAAC,SAAS;YACzC,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,IAAI;gBAAC,WAAU;;;;;;YAC5B,QAAQ,oJAAA,CAAA,SAAM,CAAC,SAAS;QAC5B;QACA;YACI,OAAO;YACP,QAAQ,IAAM,aAAa,QAAQ,CAAC,MAAM;YAC1C,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,KAAK;gBAAC,WAAU;;;;;;YAC7B,QAAQ,oJAAA,CAAA,SAAM,CAAC,MAAM;YACrB,aAAa;QACjB;KACH;IAED,MAAM,eAA2B;QAC7B;YACI,OAAO;YACP,QAAQ,IAAM,aAAa,MAAM,CAAC,iBAAiB;YACnD,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,IAAI;gBAAC,WAAU;;;;;;YAC5B,QAAQ,oJAAA,CAAA,SAAM,CAAC,SAAS;QAC5B;QACA;YACI,OAAO;YACP,QAAQ,IAAM,aAAa,MAAM,CAAC,cAAc;YAChD,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,KAAK;gBAAC,WAAU;;;;;;YAC7B,QAAQ,oJAAA,CAAA,SAAM,CAAC,MAAM;YACrB,aAAa;YACb,UAAU,CAAC,aAAa,MAAM,CAAC,SAAS;QAC5C;KACH;IAED,MAAM,kBAAkB;QACpB,IAAI,WAA0B;QAC9B,IAAI,OAAsB;QAE1B,IAAI,aAAa,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC3C,MAAM,UAAkC,aAAa,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACzE,IAAI,SAAS;gBACT,WAAW,QAAQ,UAAU;gBAC7B,OAAO,QAAQ,GAAG;YACtB;QACJ;QACA,IAAI,YAA0B,EAAE;QAEhC,IAAI,aAAa,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;YACzC,YAAY;gBAAC;aAAa;QAC9B,OAAO;YACH,MAAM,mBAAmB;gBACrB,aAAa,QAAQ;oBACjB,OAAO;oBACP,QAAQ,IAAM,WAAW;oBACzB,oBAAM,8OAAC,sJAAA,CAAA,QAAK,CAAC,iBAAiB;wBAAC,WAAU;;;;;;gBAC7C;gBACA;oBACI,OAAO,CAAC,KAAK,EAAE,WAAW,cAAc,UAAU,IAAI,EAAE,IAAI,WAAW,EAAE;oBACzE,UAAU,CAAC;oBACX,QAAQ,IAAM,WAAW;oBACzB,MAAM,yBACF,8OAAC,sJAAA,CAAA,QAAK,CAAC,SAAS;wBAAC,WAAU;;;;;6CAE3B,8OAAC,sJAAA,CAAA,QAAK,CAAC,YAAY;wBAAC,WAAU;;;;;;gBAEtC;mBACG;aACN,CAAC,MAAM,CAAC;YAET,YAAY;gBAAC;gBAAkB;gBAAa;aAAc;QAC9D;QAEA,aAAa;IACjB;IAEA,SAAS,WAAW,GAAkB;QAClC,IAAI,CAAC,KAAK;YACN,QAAQ,KAAK,CAAC;YACd;QACJ;QACA,aAAa,IAAI,CAAC,aAAa,CAAC;IACpC;IAEA,qBACI,8OAAC,uJAAA,CAAA,cAAW;;0BACR,8OAAC,uJAAA,CAAA,qBAAkB;0BAAE;;;;;;0BACrB,8OAAC,uJAAA,CAAA,qBAAkB;gBAAC,WAAU;0BACzB,UAAU,GAAG,CAAC,CAAC,OAAO,2BACnB,8OAAC;;4BACI,MAAM,GAAG,CAAC,CAAC,qBACR,8OAAC,uJAAA,CAAA,kBAAe;oCAEZ,SAAS,KAAK,MAAM;oCACpB,UAAU,KAAK,QAAQ;oCACvB,WAAU;8CAEV,cAAA,8OAAC;wCACG,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACR,kCACA,KAAK,WAAW,IAAI;;0DAGxB,8OAAC;0DAAM,KAAK,IAAI;;;;;;0DAChB,8OAAC;0DAAM,KAAK,KAAK;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DACX,KAAK,MAAM,kBAAI,8OAAC,2IAAA,CAAA,MAAG;8DAAE,KAAK,MAAM,CAAC,eAAe;;;;;;;;;;;;;;;;;mCAdpD,KAAK,KAAK;;;;;4BAmBtB,aAAa,UAAU,MAAM,GAAG,mBAAK,8OAAC,uJAAA,CAAA,uBAAoB;;;;;;uBAtBrD;;;;;;;;;;;;;;;;AA4B9B", "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/loader-overlay/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useChatContext } from '@/app/project/[id]/_hooks/use-chat';\r\nimport {\r\n    AlertDialog,\r\n    AlertDialogContent,\r\n    AlertDialogDescription,\r\n    AlertDialogHeader,\r\n    AlertDialogTitle,\r\n} from '@onlook/ui/alert-dialog';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport { observer } from 'mobx-react-lite';\r\n\r\nexport const LoaderOverlay = observer(() => {\r\n    const { isWaiting } = useChatContext();\r\n\r\n    return (\r\n        <AlertDialog open={isWaiting} onOpenChange={() => {}}>\r\n            <AlertDialogContent className=\"flex flex-col items-center gap-4 p-8 rounded-lg bg-background/90 backdrop-blur-md animate-in fade-in-0 duration-200 border shadow-lg\">\r\n                <AlertDialogHeader>\r\n                    <AlertDialogTitle className=\"text-lg justify-center text-center font-medium text-foreground-primary\">\r\n                        AI is generating code...\r\n                    </AlertDialogTitle>\r\n                    <AlertDialogDescription className=\"text-sm text-foreground-secondary max-w-md text-center\">\r\n                        Please wait while we process your request. The page is temporarily locked to\r\n                        prevent conflicts.\r\n                    </AlertDialogDescription>\r\n                </AlertDialogHeader>\r\n                <Icons.Shadow className=\"h-8 w-8 animate-spin text-foreground-primary\" />\r\n            </AlertDialogContent>\r\n        </AlertDialog>\r\n    );\r\n});\r\n\r\nLoaderOverlay.displayName = 'LoaderOverlay';\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AAAA;;;;;AAXA;;;;;;AAaO,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE;IAClC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD;IAEnC,qBACI,8OAAC,uJAAA,CAAA,cAAW;QAAC,MAAM;QAAW,cAAc,KAAO;kBAC/C,cAAA,8OAAC,uJAAA,CAAA,qBAAkB;YAAC,WAAU;;8BAC1B,8OAAC,uJAAA,CAAA,oBAAiB;;sCACd,8OAAC,uJAAA,CAAA,mBAAgB;4BAAC,WAAU;sCAAyE;;;;;;sCAGrG,8OAAC,uJAAA,CAAA,yBAAsB;4BAAC,WAAU;sCAAyD;;;;;;;;;;;;8BAK/F,8OAAC,sJAAA,CAAA,QAAK,CAAC,MAAM;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIxC;AAEA,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/members/invitation-row.tsx"], "sourcesContent": ["import { api } from '@/trpc/react';\r\nimport type { Invitation, ProjectRole } from '@onlook/models';\r\nimport { Avatar, AvatarFallback } from '@onlook/ui/avatar';\r\nimport { Button } from '@onlook/ui/button';\r\nimport { Icons } from '@onlook/ui/icons/index';\r\nimport { getInitials } from '@onlook/utility';\r\n\r\ninterface InvitationRowProps {\r\n    invitation: Invitation;\r\n}\r\n\r\nexport const InvitationRow = ({ invitation }: InvitationRowProps) => {\r\n    const apiUtils = api.useUtils();\r\n    const initials = getInitials(invitation.inviteeEmail ?? '');\r\n    const cancelInvitationMutation = api.invitation.delete.useMutation({\r\n        onSuccess: () => {\r\n            apiUtils.invitation.list.invalidate();\r\n        },\r\n    });\r\n\r\n    return (\r\n        <div className=\"py-2 px-3 flex gap-2 items-center\">\r\n            <Avatar>\r\n                <AvatarFallback>{initials}</AvatarFallback>\r\n            </Avatar>\r\n            <div className=\"flex flex-col justify-center gap-0.5 text-muted-foreground text-sm flex-1\">\r\n                <div>Pending Invitation</div>\r\n                <div>{invitation.inviteeEmail}</div>\r\n            </div>\r\n            <div className=\"flex flex-col items-center justify-center\">\r\n                <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={() => {\r\n                        cancelInvitationMutation.mutate({ id: invitation.id });\r\n                    }}\r\n                >\r\n                    <Icons.MailX className=\"size-4 text-muted-foreground transition-colors\" />\r\n                </Button>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AAAA;;;;;;;AAMO,MAAM,gBAAgB,CAAC,EAAE,UAAU,EAAsB;IAC5D,MAAM,WAAW,8IAAA,CAAA,MAAG,CAAC,QAAQ;IAC7B,MAAM,WAAW,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD,EAAE,WAAW,YAAY,IAAI;IACxD,MAAM,2BAA2B,8IAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QAC/D,WAAW;YACP,SAAS,UAAU,CAAC,IAAI,CAAC,UAAU;QACvC;IACJ;IAEA,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC,8IAAA,CAAA,SAAM;0BACH,cAAA,8OAAC,8IAAA,CAAA,iBAAc;8BAAE;;;;;;;;;;;0BAErB,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;kCAAI;;;;;;kCACL,8OAAC;kCAAK,WAAW,YAAY;;;;;;;;;;;;0BAEjC,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC,8IAAA,CAAA,SAAM;oBACH,SAAQ;oBACR,MAAK;oBACL,SAAS;wBACL,yBAAyB,MAAM,CAAC;4BAAE,IAAI,WAAW,EAAE;wBAAC;oBACxD;8BAEA,cAAA,8OAAC,sJAAA,CAAA,QAAK,CAAC,KAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/members/member-row.tsx"], "sourcesContent": ["import type { ProjectRole, UserMetadata } from '@onlook/models';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@onlook/ui/avatar';\r\nimport { getInitials } from '@onlook/utility';\r\n\r\ninterface MemberRowProps {\r\n    user: UserMetadata;\r\n    role: ProjectRole;\r\n}\r\n\r\nexport const MemberRow = ({ user, role }: MemberRowProps) => {\r\n    const initials = getInitials(user.name ?? '');\r\n\r\n    return (\r\n        <div className=\"py-2 px-3 flex gap-2 items-center\">\r\n            <Avatar>\r\n                {user?.avatarUrl && <AvatarImage src={user.avatarUrl} alt={initials} />}\r\n                <AvatarFallback>{initials}</AvatarFallback>\r\n            </Avatar>\r\n            <div className=\"flex flex-col justify-center gap-0.5 flex-1\">\r\n                <div>{user.name}</div>\r\n                <div className=\"text-xs text-muted-foreground\">{user.email}</div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;AAOO,MAAM,YAAY,CAAC,EAAE,IAAI,EAAE,IAAI,EAAkB;IACpD,MAAM,WAAW,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD,EAAE,KAAK,IAAI,IAAI;IAE1C,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC,8IAAA,CAAA,SAAM;;oBACF,MAAM,2BAAa,8OAAC,8IAAA,CAAA,cAAW;wBAAC,KAAK,KAAK,SAAS;wBAAE,KAAK;;;;;;kCAC3D,8OAAC,8IAAA,CAAA,iBAAc;kCAAE;;;;;;;;;;;;0BAErB,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;kCAAK,KAAK,IAAI;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCAAiC,KAAK,KAAK;;;;;;;;;;;;;;;;;;AAI1E", "debugId": null}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/members/invite-member-input.tsx"], "sourcesContent": ["import { api } from '@/trpc/react';\r\nimport { ProjectRole } from '@onlook/models';\r\nimport { Button } from '@onlook/ui/button';\r\nimport { Input } from '@onlook/ui/input';\r\nimport { toast } from '@onlook/ui/sonner';\r\nimport { useState } from 'react';\r\n\r\nexport const InviteMemberInput = ({ projectId }: { projectId: string }) => {\r\n    const apiUtils = api.useUtils();\r\n    const [email, setEmail] = useState('');\r\n    const [selectedRole, setSelectedRole] = useState<ProjectRole>(ProjectRole.ADMIN);\r\n\r\n    const createInvitationMutation = api.invitation.create.useMutation({\r\n        onSuccess: () => {\r\n            apiUtils.invitation.list.invalidate();\r\n            apiUtils.invitation.suggested.invalidate();\r\n        },\r\n        onError: (error) => {\r\n            toast.error('Failed to invite member', {\r\n                description: error instanceof Error ? error.message : String(error),\r\n            });\r\n        },\r\n    });\r\n\r\n    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {\r\n        e.preventDefault();\r\n        createInvitationMutation.mutate({\r\n            inviteeEmail: email,\r\n            role: selectedRole,\r\n            projectId: projectId,\r\n        });\r\n    };\r\n\r\n    return (\r\n        <form\r\n            className=\"flex items-center gap-2 p-3 border-b justify-between\"\r\n            onSubmit={handleSubmit}\r\n        >\r\n            <div className=\"flex flex-1 items-center gap-2 relative\">\r\n                <Input\r\n                    value={email}\r\n                    onChange={(e) => setEmail(e.target.value)}\r\n                    placeholder=\"<EMAIL>\"\r\n                    className=\"flex-1\"\r\n                />\r\n                {/* <Select\r\n                    value={selectedRole}\r\n                    onValueChange={(value) => setSelectedRole(value as ProjectRole)}\r\n                >\r\n                    <SelectTrigger className=\"w-22 text-xs border-0 p-2 rounded-tl-none rounded-bl-none focus:ring-0 bg-transparent absolute right-0\">\r\n                        <SelectValue />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                        <SelectItem value={ProjectRole.ADMIN}>\r\n                            <div className=\"flex flex-col\">\r\n                                <span>Admin</span>\r\n                            </div>\r\n                        </SelectItem>\r\n                    </SelectContent>\r\n                </Select> */}\r\n            </div>\r\n            <Button type=\"submit\" disabled={!email}>\r\n                Invite\r\n            </Button>\r\n        </form>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;AAEO,MAAM,oBAAoB,CAAC,EAAE,SAAS,EAAyB;IAClE,MAAM,WAAW,8IAAA,CAAA,MAAG,CAAC,QAAQ;IAC7B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,4IAAA,CAAA,cAAW,CAAC,KAAK;IAE/E,MAAM,2BAA2B,8IAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QAC/D,WAAW;YACP,SAAS,UAAU,CAAC,IAAI,CAAC,UAAU;YACnC,SAAS,UAAU,CAAC,SAAS,CAAC,UAAU;QAC5C;QACA,SAAS,CAAC;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;gBACnC,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;YACjE;QACJ;IACJ;IAEA,MAAM,eAAe,CAAC;QAClB,EAAE,cAAc;QAChB,yBAAyB,MAAM,CAAC;YAC5B,cAAc;YACd,MAAM;YACN,WAAW;QACf;IACJ;IAEA,qBACI,8OAAC;QACG,WAAU;QACV,UAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC,6IAAA,CAAA,QAAK;oBACF,OAAO;oBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oBACxC,aAAY;oBACZ,WAAU;;;;;;;;;;;0BAkBlB,8OAAC,8IAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,CAAC;0BAAO;;;;;;;;;;;;AAKpD", "debugId": null}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/members/suggested-teammates.tsx"], "sourcesContent": ["import { api } from '@/trpc/react';\r\nimport { ProjectRole } from '@onlook/models';\r\nimport { Button } from '@onlook/ui/button';\r\nimport { Icons } from '@onlook/ui/icons/index';\r\nimport { Separator } from '@onlook/ui/separator';\r\n\r\ninterface SuggestedTeammateProps {\r\n    projectId: string;\r\n}\r\n\r\nexport const SuggestedTeammates = ({ projectId }: SuggestedTeammateProps) => {\r\n    const apiUtils = api.useUtils();\r\n    const { data: suggestedUsers } = api.invitation.suggested.useQuery({ projectId });\r\n    const createInvitationMutation = api.invitation.create.useMutation({\r\n        onSuccess: () => {\r\n            apiUtils.invitation.suggested.invalidate();\r\n            apiUtils.invitation.list.invalidate();\r\n        },\r\n    });\r\n\r\n    return (\r\n        <div className=\"flex flex-col gap-2 p-3\">\r\n            <Separator />\r\n            <div className=\"space-y-0.5\">\r\n                <div className=\"text-sm\">Suggested Teammates</div>\r\n                <div className=\"text-xs text-muted-foreground\">\r\n                    Invite relevant people to collaborate\r\n                </div>\r\n            </div>\r\n            <div className=\"flex gap-0.5\">\r\n                {suggestedUsers?.map((email) => (\r\n                    <Button\r\n                        variant=\"secondary\"\r\n                        size=\"sm\"\r\n                        className=\"rounded-xl font-normal\"\r\n                        onClick={() => {\r\n                            createInvitationMutation.mutate({\r\n                                projectId,\r\n                                inviteeEmail: email,\r\n                                role: ProjectRole.ADMIN,\r\n                            });\r\n                        }}\r\n                    >\r\n                        {email}\r\n                        <Icons.PlusCircled className=\"ml-1 size-4\" />\r\n                    </Button>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAMO,MAAM,qBAAqB,CAAC,EAAE,SAAS,EAA0B;IACpE,MAAM,WAAW,8IAAA,CAAA,MAAG,CAAC,QAAQ;IAC7B,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,8IAAA,CAAA,MAAG,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC;QAAE;IAAU;IAC/E,MAAM,2BAA2B,8IAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QAC/D,WAAW;YACP,SAAS,UAAU,CAAC,SAAS,CAAC,UAAU;YACxC,SAAS,UAAU,CAAC,IAAI,CAAC,UAAU;QACvC;IACJ;IAEA,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC,iJAAA,CAAA,YAAS;;;;;0BACV,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;kCAAU;;;;;;kCACzB,8OAAC;wBAAI,WAAU;kCAAgC;;;;;;;;;;;;0BAInD,8OAAC;gBAAI,WAAU;0BACV,gBAAgB,IAAI,CAAC,sBAClB,8OAAC,8IAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;4BACL,yBAAyB,MAAM,CAAC;gCAC5B;gCACA,cAAc;gCACd,MAAM,4IAAA,CAAA,cAAW,CAAC,KAAK;4BAC3B;wBACJ;;4BAEC;0CACD,8OAAC,sJAAA,CAAA,QAAK,CAAC,WAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMrD", "debugId": null}}, {"offset": {"line": 1352, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/members/members-content.tsx"], "sourcesContent": ["import { InvitationRow } from './invitation-row';\r\n\r\nimport { MemberRow } from './member-row';\r\n\r\nimport { api } from '@/trpc/react';\r\n\r\nimport { InviteMemberInput } from './invite-member-input';\r\nimport { SuggestedTeammates } from './suggested-teammates';\r\n\r\nexport const MembersContent = ({ projectId }: { projectId: string }) => {\r\n    const { data: members, isLoading: loadingMembers } = api.member.list.useQuery({\r\n        projectId,\r\n    });\r\n    const { data: invitations, isLoading: loadingInvitations } = api.invitation.list.useQuery({\r\n        projectId,\r\n    });\r\n\r\n    if (loadingMembers && loadingInvitations) {\r\n        // TODO: Add skeleton\r\n        return null;\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <div className=\"border-b border-b-[0.5px] p-3 text-muted-foreground text-sm\">\r\n                Invite Others\r\n            </div>\r\n            <InviteMemberInput projectId={projectId} />\r\n            {members?.map((member) => (\r\n                <MemberRow key={member.userId} user={member.user} role={member.role} />\r\n            ))}\r\n            {invitations?.map((invitation) => (\r\n                <InvitationRow key={invitation.id} invitation={invitation} />\r\n            ))}\r\n            <SuggestedTeammates projectId={projectId} />\r\n        </>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAEA;AAEA;AACA;;;;;;;AAEO,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAyB;IAC/D,MAAM,EAAE,MAAM,OAAO,EAAE,WAAW,cAAc,EAAE,GAAG,8IAAA,CAAA,MAAG,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC1E;IACJ;IACA,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,kBAAkB,EAAE,GAAG,8IAAA,CAAA,MAAG,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;QACtF;IACJ;IAEA,IAAI,kBAAkB,oBAAoB;QACtC,qBAAqB;QACrB,OAAO;IACX;IAEA,qBACI;;0BACI,8OAAC;gBAAI,WAAU;0BAA8D;;;;;;0BAG7E,8OAAC,gNAAA,CAAA,oBAAiB;gBAAC,WAAW;;;;;;YAC7B,SAAS,IAAI,CAAC,uBACX,8OAAC,oMAAA,CAAA,YAAS;oBAAqB,MAAM,OAAO,IAAI;oBAAE,MAAM,OAAO,IAAI;mBAAnD,OAAO,MAAM;;;;;YAEhC,aAAa,IAAI,CAAC,2BACf,8OAAC,wMAAA,CAAA,gBAAa;oBAAqB,YAAY;mBAA3B,WAAW,EAAE;;;;;0BAErC,8OAAC,6MAAA,CAAA,qBAAkB;gBAAC,WAAW;;;;;;;;AAG3C", "debugId": null}}, {"offset": {"line": 1426, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/members/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { But<PERSON> } from '@onlook/ui/button';\r\nimport { Icons } from '@onlook/ui/icons/index';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@onlook/ui/popover';\r\nimport { useState } from 'react';\r\nimport { MembersContent } from './members-content';\r\n\r\nexport const Members = ({ projectId }: { projectId: string }) => {\r\n    const [isOpen, setIsOpen] = useState(false);\r\n\r\n    return (\r\n        <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n            <PopoverTrigger asChild>\r\n                <Button variant=\"outline\" size=\"icon\" className=\"rounded-full size-8 border-active\">\r\n                    <Icons.Plus className=\"size-4\" />\r\n                </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"p-0 w-96\" align=\"end\">\r\n                <MembersContent projectId={projectId} />\r\n            </PopoverContent>\r\n        </Popover>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQO,MAAM,UAAU,CAAC,EAAE,SAAS,EAAyB;IACxD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACI,8OAAC,+IAAA,CAAA,UAAO;QAAC,MAAM;QAAQ,cAAc;;0BACjC,8OAAC,+IAAA,CAAA,iBAAc;gBAAC,OAAO;0BACnB,cAAA,8OAAC,8IAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAO,WAAU;8BAC5C,cAAA,8OAAC,sJAAA,CAAA,QAAK,CAAC,IAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG9B,8OAAC,+IAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAW,OAAM;0BACvC,cAAA,8OAAC,yMAAA,CAAA,iBAAc;oBAAC,WAAW;;;;;;;;;;;;;;;;;AAI3C", "debugId": null}}, {"offset": {"line": 1499, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/project/%5Bid%5D/_components/main.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ChatType } from '@/app/api/chat/route';\r\nimport { useChatContext } from '@/app/project/[id]/_hooks/use-chat';\r\nimport { useCreateManager } from '@/components/store/create';\r\nimport { useEditorEngine } from '@/components/store/editor';\r\nimport { useProjectManager } from '@/components/store/project';\r\nimport { useUserManager } from '@/components/store/user';\r\nimport { api } from '@/trpc/react';\r\nimport { Routes } from '@/utils/constants';\r\nimport { Icons } from '@onlook/ui/icons';\r\nimport { TooltipProvider } from '@onlook/ui/tooltip';\r\nimport { observer } from 'mobx-react-lite';\r\nimport Link from 'next/link';\r\nimport { useEffect, useRef } from 'react';\r\nimport { usePanelMeasurements } from '../_hooks/use-panel-measure';\r\nimport { useTabActive } from '../_hooks/use-tab-active';\r\nimport { BottomBar } from './bottom-bar';\r\nimport { Canvas } from './canvas';\r\nimport { EditorBar } from './editor-bar';\r\nimport { LeftPanel } from './left-panel';\r\nimport { LoaderOverlay } from './loader-overlay';\r\nimport { RightPanel } from './right-panel';\r\nimport { TopBar } from './top-bar';\r\n\r\nexport const Main = observer(({ projectId }: { projectId: string }) => {\r\n    const editorEngine = useEditorEngine();\r\n    const projectManager = useProjectManager();\r\n    const createManager = useCreateManager();\r\n    const userManager = useUserManager();\r\n    const { sendMessages } = useChatContext();\r\n    const { data: result, isLoading } = api.project.getFullProject.useQuery({ projectId });\r\n    const leftPanelRef = useRef<HTMLDivElement | null>(null);\r\n    const rightPanelRef = useRef<HTMLDivElement | null>(null);\r\n    const { tabState } = useTabActive();\r\n\r\n    const { toolbarLeft, toolbarRight, editorBarAvailableWidth } = usePanelMeasurements(\r\n        leftPanelRef,\r\n        rightPanelRef,\r\n    );\r\n\r\n    useEffect(() => {\r\n        const initializeProject = async () => {\r\n            if (!result) {\r\n                return;\r\n            }\r\n            const { project, userCanvas, frames } = result;\r\n            projectManager.project = project;\r\n\r\n            if (project.sandbox?.id) {\r\n                if (userManager.user?.id) {\r\n                    if (!editorEngine.sandbox.session.session) {\r\n                        await editorEngine.sandbox.session.start(\r\n                            project.sandbox.id,\r\n                            userManager.user.id,\r\n                        );\r\n                    }\r\n                } else {\r\n                    console.error('Initializing project: No user id');\r\n                }\r\n            } else {\r\n                console.error('Initializing project: No sandbox id');\r\n            }\r\n\r\n            editorEngine.canvas.applyCanvas(userCanvas);\r\n            editorEngine.frames.applyFrames(frames);\r\n            await editorEngine.chat.conversation.fetchOrCreateConversation(project.id);\r\n            resumeCreate();\r\n        };\r\n\r\n        initializeProject().catch((error) => {\r\n            console.error('Error initializing project:', error);\r\n        });\r\n    }, [result, userManager.user?.id]);\r\n\r\n    const resumeCreate = async () => {\r\n        const creationData = createManager.pendingCreationData;\r\n        if (!creationData) return;\r\n\r\n        if (projectId !== creationData.project.id) return;\r\n\r\n        const messages = await editorEngine.chat.getStreamMessages(\r\n            creationData.prompt,\r\n            creationData.images,\r\n        );\r\n\r\n        if (!messages) {\r\n            console.error('Failed to get creation messages');\r\n            return;\r\n        }\r\n        createManager.pendingCreationData = null;\r\n        sendMessages(messages, ChatType.CREATE);\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (tabState === 'reactivated') {\r\n            editorEngine.sandbox.session.reconnect();\r\n        }\r\n    }, [tabState]);\r\n\r\n    if (isLoading) {\r\n        return (\r\n            <div className=\"h-screen w-screen flex items-center justify-center gap-2\">\r\n                <Icons.Shadow className=\"h-6 w-6 animate-spin text-foreground-primary\" />\r\n                <div className=\"text-xl\">Loading project...</div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (!result) {\r\n        return (\r\n            <div className=\"h-screen w-screen flex flex-col items-center justify-center gap-4\">\r\n                <div className=\"text-xl\">Project not found</div>\r\n                <Link href={Routes.PROJECTS} className=\"text-sm text-foreground-secondary\">\r\n                    Go to projects\r\n                </Link>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (editorEngine.sandbox.session.isConnecting) {\r\n        return (\r\n            <div className=\"h-screen w-screen flex items-center justify-center gap-2\">\r\n                <Icons.Shadow className=\"h-6 w-6 animate-spin text-foreground-primary\" />\r\n                <div className=\"text-xl\">Connecting to sandbox...</div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <TooltipProvider>\r\n            <div className=\"h-screen w-screen flex flex-row select-none relative\">\r\n                <Canvas />\r\n\r\n                <div className=\"absolute top-0 w-full\">\r\n                    <TopBar projectId={projectId} />\r\n                </div>\r\n\r\n                {/* Left Panel */}\r\n                <div\r\n                    ref={leftPanelRef}\r\n                    className=\"absolute top-10 left-0 animate-layer-panel-in h-[calc(100%-40px)] z-50\"\r\n                >\r\n                    <LeftPanel />\r\n                </div>\r\n\r\n                {/* EditorBar anchored between panels */}\r\n                <div\r\n                    className=\"absolute top-10 z-49\"\r\n                    style={{\r\n                        left: toolbarLeft,\r\n                        right: toolbarRight,\r\n                        overflow: 'hidden',\r\n                        pointerEvents: 'none',\r\n                        maxWidth: editorBarAvailableWidth,\r\n                        display: 'flex',\r\n                        justifyContent: 'center',\r\n                        alignItems: 'flex-start',\r\n                    }}\r\n                >\r\n                    <div style={{ pointerEvents: 'auto' }}>\r\n                        <EditorBar availableWidth={editorBarAvailableWidth} />\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Right Panel */}\r\n                <div\r\n                    ref={rightPanelRef}\r\n                    className=\"absolute top-10 right-0 animate-edit-panel-in h-[calc(100%-40px)] z-50\"\r\n                >\r\n                    <RightPanel />\r\n                </div>\r\n\r\n                <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 animate-toolbar-up \">\r\n                    <BottomBar />\r\n                </div>\r\n\r\n                {/* Loader Overlay - appears when AI is generating code */}\r\n                <LoaderOverlay />\r\n            </div>\r\n        </TooltipProvider>\r\n    );\r\n});\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAvBA;;;;;;;;;;;;;;;;;;;;;;;;AAyBO,MAAM,OAAO,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EAAE,SAAS,EAAyB;IAC9D,MAAM,eAAe,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,iBAAiB,CAAA,GAAA,uKAAA,CAAA,oBAAiB,AAAD;IACvC,MAAM,gBAAgB,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,cAAc,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD;IACtC,MAAM,EAAE,MAAM,MAAM,EAAE,SAAS,EAAE,GAAG,8IAAA,CAAA,MAAG,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC;QAAE;IAAU;IACpF,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACnD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACpD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD;IAEhC,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,uBAAuB,EAAE,GAAG,CAAA,GAAA,8LAAA,CAAA,uBAAoB,AAAD,EAC9E,cACA;IAGJ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,oBAAoB;YACtB,IAAI,CAAC,QAAQ;gBACT;YACJ;YACA,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;YACxC,eAAe,OAAO,GAAG;YAEzB,IAAI,QAAQ,OAAO,EAAE,IAAI;gBACrB,IAAI,YAAY,IAAI,EAAE,IAAI;oBACtB,IAAI,CAAC,aAAa,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;wBACvC,MAAM,aAAa,OAAO,CAAC,OAAO,CAAC,KAAK,CACpC,QAAQ,OAAO,CAAC,EAAE,EAClB,YAAY,IAAI,CAAC,EAAE;oBAE3B;gBACJ,OAAO;oBACH,QAAQ,KAAK,CAAC;gBAClB;YACJ,OAAO;gBACH,QAAQ,KAAK,CAAC;YAClB;YAEA,aAAa,MAAM,CAAC,WAAW,CAAC;YAChC,aAAa,MAAM,CAAC,WAAW,CAAC;YAChC,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC,yBAAyB,CAAC,QAAQ,EAAE;YACzE;QACJ;QAEA,oBAAoB,KAAK,CAAC,CAAC;YACvB,QAAQ,KAAK,CAAC,+BAA+B;QACjD;IACJ,GAAG;QAAC;QAAQ,YAAY,IAAI,EAAE;KAAG;IAEjC,MAAM,eAAe;QACjB,MAAM,eAAe,cAAc,mBAAmB;QACtD,IAAI,CAAC,cAAc;QAEnB,IAAI,cAAc,aAAa,OAAO,CAAC,EAAE,EAAE;QAE3C,MAAM,WAAW,MAAM,aAAa,IAAI,CAAC,iBAAiB,CACtD,aAAa,MAAM,EACnB,aAAa,MAAM;QAGvB,IAAI,CAAC,UAAU;YACX,QAAQ,KAAK,CAAC;YACd;QACJ;QACA,cAAc,mBAAmB,GAAG;QACpC,aAAa,UAAU,2JAAA,CAAA,WAAQ,CAAC,MAAM;IAC1C;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,aAAa,eAAe;YAC5B,aAAa,OAAO,CAAC,OAAO,CAAC,SAAS;QAC1C;IACJ,GAAG;QAAC;KAAS;IAEb,IAAI,WAAW;QACX,qBACI,8OAAC;YAAI,WAAU;;8BACX,8OAAC,sJAAA,CAAA,QAAK,CAAC,MAAM;oBAAC,WAAU;;;;;;8BACxB,8OAAC;oBAAI,WAAU;8BAAU;;;;;;;;;;;;IAGrC;IAEA,IAAI,CAAC,QAAQ;QACT,qBACI,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;8BAAU;;;;;;8BACzB,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAM,2JAAA,CAAA,SAAM,CAAC,QAAQ;oBAAE,WAAU;8BAAoC;;;;;;;;;;;;IAKvF;IAEA,IAAI,aAAa,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE;QAC3C,qBACI,8OAAC;YAAI,WAAU;;8BACX,8OAAC,sJAAA,CAAA,QAAK,CAAC,MAAM;oBAAC,WAAU;;;;;;8BACxB,8OAAC;oBAAI,WAAU;8BAAU;;;;;;;;;;;;IAGrC;IAEA,qBACI,8OAAC,+IAAA,CAAA,kBAAe;kBACZ,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC,2LAAA,CAAA,SAAM;;;;;8BAEP,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC,+LAAA,CAAA,SAAM;wBAAC,WAAW;;;;;;;;;;;8BAIvB,8OAAC;oBACG,KAAK;oBACL,WAAU;8BAEV,cAAA,8OAAC,kMAAA,CAAA,YAAS;;;;;;;;;;8BAId,8OAAC;oBACG,WAAU;oBACV,OAAO;wBACH,MAAM;wBACN,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,UAAU;wBACV,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBAChB;8BAEA,cAAA,8OAAC;wBAAI,OAAO;4BAAE,eAAe;wBAAO;kCAChC,cAAA,8OAAC,kMAAA,CAAA,YAAS;4BAAC,gBAAgB;;;;;;;;;;;;;;;;8BAKnC,8OAAC;oBACG,KAAK;oBACL,WAAU;8BAEV,cAAA,8OAAC,mMAAA,CAAA,aAAU;;;;;;;;;;8BAGf,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC,kMAAA,CAAA,YAAS;;;;;;;;;;8BAId,8OAAC,sMAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;AAI9B", "debugId": null}}]}