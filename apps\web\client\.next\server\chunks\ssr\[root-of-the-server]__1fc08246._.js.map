{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/utils/constants/index.ts"], "sourcesContent": ["export const Routes = {\r\n    HOME: '/',\r\n    LOGIN: '/login',\r\n    PRICING: '/pricing',\r\n    PROJECTS: '/projects',\r\n    PROJECT: '/project',\r\n};\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,SAAS;IAClB,MAAM;IACN,OAAO;IACP,SAAS;IACT,UAAU;IACV,SAAS;AACb", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/env.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\r\nimport { z } from 'zod';\r\n\r\nexport const env = createEnv({\r\n    /**\r\n     * Specify your server-side environment variables schema here. This way you can ensure the app\r\n     * isn't built with invalid env vars.\r\n     */\r\n    server: {\r\n        NODE_ENV: z.enum(['development', 'test', 'production']),\r\n        ANTHROPIC_API_KEY: z.string(),\r\n        CSB_API_KEY: z.string(),\r\n        SUPABASE_DATABASE_URL: z.string().url(),\r\n        RESEND_API_KEY: z.string().optional(),\r\n        MORPH_API_KEY: z.string().optional(),\r\n        RELACE_API_KEY: z.string().optional(),\r\n        FREESTYLE_API_KEY: z.string().optional(),\r\n    },\r\n    /**\r\n     * Specify your client-side environment variables schema here. This way you can ensure the app\r\n     * isn't built with invalid env vars. To expose them to the client, prefix them with\r\n     * `NEXT_PUBLIC_`.\r\n     */\r\n    client: {\r\n        NEXT_PUBLIC_SITE_URL: z.string().url().default('http://localhost:3000'),\r\n        NEXT_PUBLIC_SUPABASE_URL: z.string(),\r\n        NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string(),\r\n        NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),\r\n        NEXT_PUBLIC_POSTHOG_HOST: z.string().optional(),\r\n        NEXT_PUBLIC_FEATURE_COLLABORATION: z.boolean().default(false),\r\n        NEXT_PUBLIC_HOSTING_DOMAIN: z.string().optional(),\r\n    },\r\n\r\n    /**\r\n     * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\r\n     * middlewares) or client-side so we need to destruct manually.\r\n     */\r\n    runtimeEnv: {\r\n        NODE_ENV: process.env.NODE_ENV,\r\n        ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,\r\n        CSB_API_KEY: process.env.CSB_API_KEY,\r\n        RESEND_API_KEY: process.env.RESEND_API_KEY,\r\n        SUPABASE_DATABASE_URL: process.env.SUPABASE_DATABASE_URL,\r\n        NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,\r\n        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,\r\n        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n        NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,\r\n        NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,\r\n        MORPH_API_KEY: process.env.MORPH_API_KEY,\r\n        RELACE_API_KEY: process.env.RELACE_API_KEY,\r\n        NEXT_PUBLIC_FEATURE_COLLABORATION: process.env.NEXT_PUBLIC_FEATURE_COLLABORATION,\r\n        FREESTYLE_API_KEY: process.env.FREESTYLE_API_KEY,\r\n        NEXT_PUBLIC_HOSTING_DOMAIN: process.env.NEXT_PUBLIC_HOSTING_DOMAIN,\r\n    },\r\n    /**\r\n     * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\r\n     * useful for Docker builds.\r\n     */\r\n    skipValidation: !!process.env.SKIP_ENV_VALIDATION,\r\n    /**\r\n     * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\r\n     * `SOME_VAR=''` will throw an error.\r\n     */\r\n    emptyStringAsUndefined: true,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE;IACzB;;;KAGC,GACD,QAAQ;QACJ,UAAU,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa;QACtD,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM;QAC3B,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM;QACrB,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QACrC,gBAAgB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAClC,gBAAgB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1C;IACA;;;;KAIC,GACD,QAAQ;QACJ,sBAAsB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;QAC/C,0BAA0B,iLAAA,CAAA,IAAC,CAAC,MAAM;QAClC,+BAA+B,iLAAA,CAAA,IAAC,CAAC,MAAM;QACvC,yBAAyB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5C,0BAA0B,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7C,mCAAmC,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QACvD,4BAA4B,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnD;IAEA;;;KAGC,GACD,YAAY;QACR,QAAQ;QACR,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,aAAa,QAAQ,GAAG,CAAC,WAAW;QACpC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QACxD,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QACtD,wBAAwB;QACxB,6BAA6B;QAC7B,uBAAuB;QACvB,wBAAwB;QACxB,eAAe,QAAQ,GAAG,CAAC,aAAa;QACxC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAC1C,mCAAmC,QAAQ,GAAG,CAAC,iCAAiC;QAChF,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,4BAA4B,QAAQ,GAAG,CAAC,0BAA0B;IACtE;IACA;;;KAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;KAGC,GACD,wBAAwB;AAC5B", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/utils/supabase/server.ts"], "sourcesContent": ["import { env } from '@/env';\r\nimport { createServerClient } from '@supabase/ssr';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function createClient() {\r\n    const cookieStore = await cookies();\r\n\r\n    // Create a server's supabase client with newly configured cookie,\r\n    // which could be used to maintain user's session\r\n    return createServerClient(\r\n        env.NEXT_PUBLIC_SUPABASE_URL,\r\n        env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n        {\r\n            cookies: {\r\n                getAll() {\r\n                    return cookieStore.getAll();\r\n                },\r\n                setAll(cookiesToSet) {\r\n                    try {\r\n                        cookiesToSet.forEach(({ name, value, options }) =>\r\n                            cookieStore.set(name, value, options),\r\n                        );\r\n                    } catch {\r\n                        // The `setAll` method was called from a Server Component.\r\n                        // This can be ignored if you have middleware refreshing\r\n                        // user sessions.\r\n                    }\r\n                },\r\n            },\r\n        },\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,eAAe;IAClB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,kEAAkE;IAClE,iDAAiD;IACjD,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACpB,mIAAA,CAAA,MAAG,CAAC,wBAAwB,EAC5B,mIAAA,CAAA,MAAG,CAAC,6BAA6B,EACjC;QACI,SAAS;YACL;gBACI,OAAO,YAAY,MAAM;YAC7B;YACA,QAAO,YAAY;gBACf,IAAI;oBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC1C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAErC,EAAE,OAAM;gBACJ,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACrB;YACJ;QACJ;IACJ;AAER", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/canvas.ts"], "sourcesContent": ["import type { Canvas } from '@onlook/models';\r\nimport type { UserCanvas as DbUserCanvas } from '../schema';\r\n\r\nexport const toCanvas = (dbUserCanvas: DbUserCanvas): Canvas => {\r\n    return {\r\n        id: dbUserCanvas.canvasId,\r\n        scale: Number(dbUserCanvas.scale),\r\n        position: {\r\n            x: Number(dbUserCanvas.x),\r\n            y: Number(dbUserCanvas.y),\r\n        },\r\n    };\r\n};\r\n\r\nexport const fromCanvas = (canvas: Canvas): Omit<DbUserCanvas, 'userId'> => {\r\n    return {\r\n        scale: canvas.scale.toString(),\r\n        x: canvas.position.x.toString(),\r\n        y: canvas.position.y.toString(),\r\n        canvasId: canvas.id,\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;;AAGO,MAAM,WAAW,CAAC;IACrB,OAAO;QACH,IAAI,aAAa,QAAQ;QACzB,OAAO,OAAO,aAAa,KAAK;QAChC,UAAU;YACN,GAAG,OAAO,aAAa,CAAC;YACxB,GAAG,OAAO,aAAa,CAAC;QAC5B;IACJ;AACJ;AAEO,MAAM,aAAa,CAAC;IACvB,OAAO;QACH,OAAO,OAAO,KAAK,CAAC,QAAQ;QAC5B,GAAG,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ;QAC7B,GAAG,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ;QAC7B,UAAU,OAAO,EAAE;IACvB;AACJ", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/conversation.ts"], "sourcesContent": ["import { type ChatConversation } from \"@onlook/models\";\r\nimport type { Conversation as DbConversation } from \"../schema\";\r\n\r\nexport const toConversation = (dbConversation: DbConversation): ChatConversation => {\r\n    return {\r\n        id: dbConversation.id,\r\n        displayName: dbConversation.displayName,\r\n        createdAt: dbConversation.createdAt.toISOString(),\r\n        updatedAt: dbConversation.updatedAt.toISOString(),\r\n        projectId: dbConversation.projectId,\r\n    }\r\n}\r\n\r\nexport const fromConversation = (conversation: ChatConversation): DbConversation => {\r\n    return {\r\n        id: conversation.id,\r\n        displayName: conversation.displayName,\r\n        createdAt: new Date(conversation.createdAt),\r\n        updatedAt: new Date(conversation.updatedAt),\r\n        projectId: conversation.projectId,\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAGO,MAAM,iBAAiB,CAAC;IAC3B,OAAO;QACH,IAAI,eAAe,EAAE;QACrB,aAAa,eAAe,WAAW;QACvC,WAAW,eAAe,SAAS,CAAC,WAAW;QAC/C,WAAW,eAAe,SAAS,CAAC,WAAW;QAC/C,WAAW,eAAe,SAAS;IACvC;AACJ;AAEO,MAAM,mBAAmB,CAAC;IAC7B,OAAO;QACH,IAAI,aAAa,EAAE;QACnB,aAAa,aAAa,WAAW;QACrC,WAAW,IAAI,KAAK,aAAa,SAAS;QAC1C,WAAW,IAAI,KAAK,aAAa,SAAS;QAC1C,WAAW,aAAa,SAAS;IACrC;AACJ", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/frame.ts"], "sourcesContent": ["import { FrameType, type WebFrame } from '@onlook/models';\r\nimport type { Frame as DbFrame } from '../schema';\r\n\r\nexport const toFrame = (dbFrame: DbFrame): WebFrame => {\r\n    return {\r\n        id: dbFrame.id,\r\n        url: dbFrame.url,\r\n        type: dbFrame.type as FrameType,\r\n        position: {\r\n            x: Number(dbFrame.x),\r\n            y: Number(dbFrame.y),\r\n        },\r\n        dimension: {\r\n            width: Number(dbFrame.width),\r\n            height: Number(dbFrame.height),\r\n        },\r\n    };\r\n};\r\n\r\nexport const fromFrame = (canvasId: string, frame: WebFrame): DbFrame => {\r\n    return {\r\n        id: frame.id,\r\n        url: frame.url,\r\n        type: frame.type as FrameType,\r\n        x: frame.position.x.toString(),\r\n        y: frame.position.y.toString(),\r\n        canvasId: canvasId,\r\n        width: frame.dimension.width.toString(),\r\n        height: frame.dimension.height.toString(),\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;;AAGO,MAAM,UAAU,CAAC;IACpB,OAAO;QACH,IAAI,QAAQ,EAAE;QACd,KAAK,QAAQ,GAAG;QAChB,MAAM,QAAQ,IAAI;QAClB,UAAU;YACN,GAAG,OAAO,QAAQ,CAAC;YACnB,GAAG,OAAO,QAAQ,CAAC;QACvB;QACA,WAAW;YACP,OAAO,OAAO,QAAQ,KAAK;YAC3B,QAAQ,OAAO,QAAQ,MAAM;QACjC;IACJ;AACJ;AAEO,MAAM,YAAY,CAAC,UAAkB;IACxC,OAAO;QACH,IAAI,MAAM,EAAE;QACZ,KAAK,MAAM,GAAG;QACd,MAAM,MAAM,IAAI;QAChB,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC,QAAQ;QAC5B,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC,QAAQ;QAC5B,UAAU;QACV,OAAO,MAAM,SAAS,CAAC,KAAK,CAAC,QAAQ;QACrC,QAAQ,MAAM,SAAS,CAAC,MAAM,CAAC,QAAQ;IAC3C;AACJ", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/actions/code.ts"], "sourcesContent": ["import {\r\n    type GroupContainer,\r\n    type InsertImageAction,\r\n    type PasteParams,\r\n    type RemoveImageAction,\r\n} from './action';\r\nimport { type ActionLocation, type IndexActionLocation } from './location';\r\nimport { type ActionTarget } from './target';\r\n\r\nexport enum CodeActionType {\r\n    MOVE = 'move',\r\n    INSERT = 'insert',\r\n    REMOVE = 'remove',\r\n    GROUP = 'group',\r\n    UNGROUP = 'ungroup',\r\n    INSERT_IMAGE = 'insert-image',\r\n    REMOVE_IMAGE = 'remove-image',\r\n}\r\n\r\nexport interface BaseCodeAction {\r\n    type: CodeActionType;\r\n    location: ActionLocation;\r\n    oid: string;\r\n}\r\n\r\nexport interface BaseCodeInsert extends BaseCodeAction {\r\n    type: CodeActionType.INSERT;\r\n    tagName: string;\r\n    attributes: Record<string, string>;\r\n    textContent: string | null;\r\n    pasteParams: PasteParams | null;\r\n    codeBlock: string | null;\r\n}\r\n\r\nexport interface CodeInsert extends BaseCodeInsert {\r\n    children: CodeInsert[];\r\n}\r\n\r\nexport interface CodeRemove {\r\n    type: CodeActionType.REMOVE;\r\n    oid: string;\r\n    codeBlock: string | null;\r\n}\r\n\r\nexport interface CodeStyle {\r\n    oid: string;\r\n    styles: Record<string, string>;\r\n}\r\n\r\nexport interface CodeEditText {\r\n    oid: string;\r\n    content: string;\r\n}\r\n\r\nexport interface CodeMove extends BaseCodeAction {\r\n    type: CodeActionType.MOVE;\r\n    location: IndexActionLocation;\r\n}\r\n\r\nexport interface BaseCodeGroup {\r\n    oid: string;\r\n    container: GroupContainer;\r\n    children: ActionTarget[];\r\n}\r\n\r\nexport interface CodeGroup extends BaseCodeGroup {\r\n    type: CodeActionType.GROUP;\r\n}\r\n\r\nexport interface CodeUngroup extends BaseCodeGroup {\r\n    type: CodeActionType.UNGROUP;\r\n}\r\n\r\nexport interface CodeInsertImage extends InsertImageAction {\r\n    type: CodeActionType.INSERT_IMAGE;\r\n    folderPath: string;\r\n}\r\n\r\nexport interface CodeRemoveImage extends RemoveImageAction {\r\n    type: CodeActionType.REMOVE_IMAGE;\r\n}\r\n\r\nexport type CodeAction =\r\n    | CodeMove\r\n    | CodeInsert\r\n    | CodeRemove\r\n    | CodeGroup\r\n    | CodeUngroup\r\n    | CodeInsertImage\r\n    | CodeRemoveImage;\r\n"], "names": [], "mappings": ";;;AASO,IAAA,AAAK,wCAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/actions/location.ts"], "sourcesContent": ["import { z } from 'zod';\r\n\r\nconst BaseActionLocationSchema = z.object({\r\n    type: z.enum(['prepend', 'append']),\r\n    targetDomId: z.string(),\r\n    targetOid: z.string().nullable(),\r\n});\r\n\r\nexport const IndexActionLocationSchema = BaseActionLocationSchema.extend({\r\n    type: z.literal('index'),\r\n    index: z.number(),\r\n    originalIndex: z.number(),\r\n});\r\n\r\nexport const ActionLocationSchema = z.discriminatedUnion('type', [\r\n    IndexActionLocationSchema,\r\n    BaseActionLocationSchema,\r\n]);\r\n\r\nexport type ActionLocation = z.infer<typeof ActionLocationSchema>;\r\nexport type IndexActionLocation = z.infer<typeof IndexActionLocationSchema>;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEA,MAAM,2BAA2B,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,MAAM,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;KAAS;IAClC,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,WAAW,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAClC;AAEO,MAAM,4BAA4B,yBAAyB,MAAM,CAAC;IACrE,MAAM,iLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM;IACf,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM;AAC3B;AAEO,MAAM,uBAAuB,iLAAA,CAAA,IAAC,CAAC,kBAAkB,CAAC,QAAQ;IAC7D;IACA;CACH", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/actions/index.ts"], "sourcesContent": ["export * from './action.ts';\r\nexport * from './code.ts';\r\nexport * from './location.ts';\r\nexport * from './target.ts';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/assets/index.ts"], "sourcesContent": ["interface UpdateResult {\r\n    success: boolean;\r\n    error?: string;\r\n}\r\n\r\ninterface ColorUpdate {\r\n    configPath: string;\r\n    cssPath: string;\r\n    configContent: string;\r\n    cssContent: string;\r\n}\r\n\r\ninterface ConfigUpdateResult {\r\n    keyUpdated: boolean;\r\n    valueUpdated: boolean;\r\n    output: string;\r\n}\r\n\r\ninterface ClassReplacement {\r\n    oldClass: string;\r\n    newClass: string;\r\n}\r\n\r\ninterface ThemeColors {\r\n    [key: string]: {\r\n        value: string;\r\n        line?: number;\r\n    };\r\n}\r\n\r\ninterface ColorValue {\r\n    name: string;\r\n    lightMode: string;\r\n    darkMode: string;\r\n    line?: {\r\n        config?: number;\r\n        css?: {\r\n            lightMode?: number;\r\n            darkMode?: number;\r\n        };\r\n    };\r\n}\r\n\r\ninterface ParsedColors {\r\n    [key: string]: ColorValue;\r\n}\r\n\r\ninterface ConfigResult {\r\n    cssContent: string;\r\n    cssPath: string;\r\n    configPath: string;\r\n    configContent: any;\r\n}\r\n\r\ninterface Font {\r\n    id: string;\r\n    family: string;\r\n    subsets: string[];\r\n    variable: string;\r\n    weight?: string[];\r\n    styles?: string[];\r\n    type: string;\r\n}\r\n\r\nexport enum SystemTheme {\r\n    LIGHT = 'light',\r\n    DARK = 'dark',\r\n    SYSTEM = 'system',\r\n}\r\n\r\nexport type {\r\n    ClassReplacement,\r\n    ColorUpdate,\r\n    ColorValue,\r\n    ConfigResult,\r\n    ConfigUpdateResult,\r\n    Font,\r\n    ParsedColors,\r\n    ThemeColors,\r\n    UpdateResult,\r\n};\r\n"], "names": [], "mappings": ";;;AAgEO,IAAA,AAAK,qCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/auth/index.ts"], "sourcesContent": ["export enum SignInMethod {\r\n    GITHUB = 'github',\r\n    GOOGLE = 'google',\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,sCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/message/context.ts"], "sourcesContent": ["export enum MessageContextType {\r\n    FILE = 'file',\r\n    HIGHLIGHT = 'highlight',\r\n    IMAGE = 'image',\r\n    ERROR = 'error',\r\n    PROJECT = 'project',\r\n}\r\n\r\ntype BaseMessageContext = {\r\n    type: MessageContextType;\r\n    content: string;\r\n    displayName: string;\r\n};\r\n\r\nexport type FileMessageContext = BaseMessageContext & {\r\n    type: MessageContextType.FILE;\r\n    path: string;\r\n};\r\n\r\nexport type HighlightMessageContext = BaseMessageContext & {\r\n    type: MessageContextType.HIGHLIGHT;\r\n    path: string;\r\n    start: number;\r\n    end: number;\r\n};\r\n\r\nexport type ImageMessageContext = BaseMessageContext & {\r\n    type: MessageContextType.IMAGE;\r\n    mimeType: string;\r\n};\r\n\r\nexport type ErrorMessageContext = BaseMessageContext & {\r\n    type: MessageContextType.ERROR;\r\n};\r\n\r\nexport type ProjectMessageContext = BaseMessageContext & {\r\n    type: MessageContextType.PROJECT;\r\n    path: string;\r\n};\r\n\r\nexport type ChatMessageContext =\r\n    | FileMessageContext\r\n    | HighlightMessageContext\r\n    | ImageMessageContext\r\n    | ErrorMessageContext\r\n    | ProjectMessageContext;\r\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,4CAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/message/message.ts"], "sourcesContent": ["import type { Message } from '@ai-sdk/react';\r\nimport type { TextPart } from 'ai';\r\nimport type { CodeDiff } from '../../code/index.ts';\r\nimport { type ChatMessageContext } from './context.ts';\r\n\r\nexport enum ChatMessageRole {\r\n    USER = 'user',\r\n    ASSISTANT = 'assistant',\r\n    SYSTEM = 'system',\r\n}\r\n\r\nexport interface UserChatMessage extends Message {\r\n    role: ChatMessageRole.USER;\r\n    context: ChatMessageContext[];\r\n    parts: TextPart[];\r\n    content: string;\r\n}\r\n\r\nexport interface AssistantChatMessage extends Message {\r\n    role: ChatMessageRole.ASSISTANT;\r\n    applied: boolean;\r\n    snapshots: ChatSnapshot;\r\n    parts: Message['parts'];\r\n    content: string;\r\n}\r\n\r\nexport type ChatSnapshot = Record<string, CodeDiff>;\r\n\r\nexport interface SystemChatMessage extends Message {\r\n    role: ChatMessageRole.SYSTEM;\r\n}\r\n\r\nexport type ChatMessage = UserChatMessage | AssistantChatMessage | SystemChatMessage;\r\n"], "names": [], "mappings": ";;;AAKO,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/message/index.ts"], "sourcesContent": ["export * from '../response.ts';\r\nexport * from './code.ts';\r\nexport * from './context.ts';\r\nexport * from './message.ts';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/request.ts"], "sourcesContent": ["import type { CoreMessage } from 'ai';\r\n\r\nexport enum StreamRequestType {\r\n    CHAT = 'chat',\r\n    CREATE = 'create',\r\n    ERROR_FIX = 'error-fix',\r\n    SUGGESTIONS = 'suggestions',\r\n    SUMMARY = 'summary',\r\n}\r\n\r\nexport type StreamRequest = {\r\n    messages: CoreMessage[];\r\n    systemPrompt: string;\r\n    requestType: StreamRequestType;\r\n    useAnalytics: boolean;\r\n};\r\n\r\nexport type StreamRequestV2 = {\r\n    messages: CoreMessage[];\r\n    requestType: StreamRequestType;\r\n    useAnalytics: boolean;\r\n};\r\n"], "names": [], "mappings": ";;;AAEO,IAAA,AAAK,2CAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/suggestion.ts"], "sourcesContent": ["import { z } from 'zod';\r\n\r\nexport interface ProjectSuggestions {\r\n    id: string;\r\n    projectId: string;\r\n    suggestions: ChatSuggestion[];\r\n}\r\n\r\nexport interface ChatSuggestion {\r\n    title: string;\r\n    prompt: string;\r\n}\r\n\r\nexport const ChatSuggestionSchema = z.object({\r\n    title: z\r\n        .string()\r\n        .describe(\r\n            'The display title of the suggestion. This will be shown to the user. Keep it concise but descriptive.',\r\n        ),\r\n    prompt: z\r\n        .string()\r\n        .describe(\r\n            'The prompt for the suggestion. This will be used to generate the suggestion. Make this as detailed and specific as possible.',\r\n        ),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAaO,MAAM,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,OAAO,iLAAA,CAAA,IAAC,CACH,MAAM,GACN,QAAQ,CACL;IAER,QAAQ,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,QAAQ,CACL;AAEZ", "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/summary.ts"], "sourcesContent": ["import { z } from 'zod';\r\n\r\nexport const ChatSummarySchema = z.object({\r\n    filesDiscussed: z\r\n        .array(z.string())\r\n        .describe('List of file paths mentioned in the conversation'),\r\n    projectContext: z\r\n        .string()\r\n        .describe('Summary of what the user is building and their overall goals'),\r\n    implementationDetails: z\r\n        .string()\r\n        .describe('Summary of key code decisions, patterns, and important implementation details'),\r\n    userPreferences: z\r\n        .string()\r\n        .describe('Specific preferences the user has expressed about implementation, design, etc.'),\r\n    currentStatus: z.string().describe('Current state of the project and any pending work'),\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,MAAM,oBAAoB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,gBAAgB,iLAAA,CAAA,IAAC,CACZ,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,IACd,QAAQ,CAAC;IACd,gBAAgB,iLAAA,CAAA,IAAC,CACZ,MAAM,GACN,QAAQ,CAAC;IACd,uBAAuB,iLAAA,CAAA,IAAC,CACnB,MAAM,GACN,QAAQ,CAAC;IACd,iBAAiB,iLAAA,CAAA,IAAC,CACb,MAAM,GACN,QAAQ,CAAC;IACd,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/chat/index.ts"], "sourcesContent": ["export * from './conversation/';\r\nexport * from './message/';\r\nexport * from './request.ts';\r\nexport * from './response.ts';\r\nexport * from './stream.ts';\r\nexport * from './suggestion.ts';\r\nexport * from './summary.ts';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/create/index.ts"], "sourcesContent": ["export enum CreateStage {\r\n    CLONING = 'cloning',\r\n    GIT_INIT = 'git_init',\r\n    INSTALLING = 'installing',\r\n    COMPLETE = 'complete',\r\n    ERROR = 'error',\r\n}\r\n\r\nexport enum VerifyStage {\r\n    CHECKING = 'checking',\r\n    NOT_INSTALLED = 'not_installed',\r\n    INSTALLED = 'installed',\r\n    ERROR = 'error',\r\n}\r\n\r\nexport enum SetupStage {\r\n    INSTALLING = 'installing',\r\n    CONFIGURING = 'configuring',\r\n    COMPLETE = 'complete',\r\n    ERROR = 'error',\r\n}\r\n\r\nexport interface CreateProjectResponse {\r\n    success: boolean;\r\n    error?: string;\r\n    response?: {\r\n        projectPath: string;\r\n        content: string;\r\n    };\r\n    cancelled?: boolean;\r\n}\r\n\r\nexport type CreateCallback = (stage: CreateStage, message: string) => void;\r\nexport type VerifyCallback = (stage: VerifyStage, message: string) => void;\r\nexport type SetupCallback = (stage: SetupStage, message: string) => void;\r\n"], "names": [], "mappings": ";;;;;AAAO,IAAA,AAAK,qCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,qCAAA;;;;;WAAA;;AAOL,IAAA,AAAK,oCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/editor/index.ts"], "sourcesContent": ["export interface WebviewMetadata {\r\n    id: string;\r\n    title: string;\r\n    src: string;\r\n}\r\n\r\nexport enum EditorMode {\r\n    DESIGN = 'design',\r\n    PREVIEW = 'preview',\r\n    PAN = 'pan',\r\n    INSERT_TEXT = 'insert-text',\r\n    INSERT_DIV = 'insert-div',\r\n    INSERT_IMAGE = 'insert-image',\r\n}\r\n\r\nexport enum EditorTabValue {\r\n    CHAT = 'chat',\r\n    DEV = 'dev',\r\n}\r\n\r\nexport enum SettingsTabValue {\r\n    SITE = 'site',\r\n    DOMAIN = 'domain',\r\n    PROJECT = 'project',\r\n    PREFERENCES = 'preferences',\r\n    VERSIONS = 'versions',\r\n    ADVANCED = 'advanced',\r\n}\r\n\r\nexport enum LeftPanelTabValue {\r\n    PAGES = 'pages',\r\n    LAYERS = 'layers',\r\n    COMPONENTS = 'components',\r\n    IMAGES = 'images',\r\n    WINDOWS = 'windows',\r\n    BRAND = 'brand',\r\n    APPS = 'apps',\r\n}\r\n\r\nexport enum BrandTabValue {\r\n    COLORS = 'colors',\r\n    FONTS = 'fonts',\r\n}\r\n\r\nexport enum MouseAction {\r\n    MOVE = 'move',\r\n    MOUSE_DOWN = 'click',\r\n    DOUBLE_CLICK = 'double-click',\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAMO,IAAA,AAAK,oCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,wCAAA;;;WAAA;;AAKL,IAAA,AAAK,0CAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,2CAAA;;;;;;;;WAAA;;AAUL,IAAA,AAAK,uCAAA;;;WAAA;;AAKL,IAAA,AAAK,qCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/element/layers.ts"], "sourcesContent": ["export enum DynamicType {\r\n    ARRAY = 'array',\r\n    CONDITIONAL = 'conditional',\r\n    UNKNOWN = 'unknown',\r\n}\r\n\r\nexport enum CoreElementType {\r\n    COMPONENT_ROOT = 'component-root',\r\n    BODY_TAG = 'body-tag',\r\n}\r\n\r\nexport interface LayerNode {\r\n    domId: string;\r\n    frameId: string;\r\n    instanceId: string | null;\r\n    oid: string | null;\r\n    textContent: string;\r\n    tagName: string;\r\n    isVisible: boolean;\r\n    dynamicType: DynamicType | null;\r\n    coreElementType: CoreElementType | null;\r\n    component: string | null;\r\n    children: string[] | null;\r\n    parent: string | null;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,qCAAA;;;;WAAA;;AAML,IAAA,AAAK,yCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/element/props.ts"], "sourcesContent": ["interface ParsedProps {\r\n    type: 'props';\r\n    props: NodeProps[];\r\n}\r\n\r\nexport enum PropsType {\r\n    String = 'string',\r\n    Number = 'number',\r\n    Boolean = 'boolean',\r\n    Object = 'object',\r\n    Array = 'array',\r\n    Code = 'code',\r\n}\r\n\r\nexport interface NodeProps {\r\n    key: any;\r\n    value: any;\r\n    type: PropsType;\r\n}\r\n\r\ninterface PropsParsingError {\r\n    type: 'error';\r\n    reason: string;\r\n}\r\n\r\nexport type PropsParsingResult = ParsedProps | PropsParsingError;\r\n"], "names": [], "mappings": ";;;AAKO,IAAA,AAAK,mCAAA;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/element/index.ts"], "sourcesContent": ["export * from './classes';\r\nexport * from './element';\r\nexport * from './layers';\r\nexport * from './templateNode';\r\nexport * from './props';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/ide/index.ts"], "sourcesContent": ["export enum IdeType {\r\n    VS_CODE = 'VSCode',\r\n    CURSOR = 'Cursor',\r\n    ZED = 'Zed',\r\n    WINDSURF = 'Windsurf',\r\n    ONLOOK = 'Onlook',\r\n}\r\n\r\nexport const DEFAULT_IDE = IdeType.ONLOOK;\r\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,iCAAA;;;;;;WAAA;;AAQL,MAAM", "debugId": null}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/llm/index.ts"], "sourcesContent": ["export enum LLMProvider {\r\n    ANTHROPIC = 'anthropic',\r\n    GOOGLE = 'google',\r\n}\r\n\r\nexport enum CLAUDE_MODELS {\r\n    SONNET_4 = 'claude-sonnet-4-20250514',\r\n    SONNET_3_7 = 'claude-3-7-sonnet-20250219',\r\n    HAIKU = 'claude-3-5-haiku-20241022',\r\n    GEMINI = 'gemini-2.0-flash',\r\n}\r\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,qCAAA;;;WAAA;;AAKL,IAAA,AAAK,uCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 864, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/project/domain.ts"], "sourcesContent": ["export enum DomainType {\r\n    BASE = 'base',\r\n    CUSTOM = 'custom',\r\n}\r\n\r\nexport interface ProjectDomain {\r\n    base: DomainSettings | null;\r\n    custom: DomainSettings | null;\r\n}\r\n\r\nexport interface DomainSettings {\r\n    url: string;\r\n    type: DomainType;\r\n    publishedAt?: string;\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,oCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/project/frame.ts"], "sourcesContent": ["import { Orientation, Theme } from '@onlook/constants';\r\nimport type { RectDimension, RectPosition } from './rect';\r\n\r\nexport enum FrameType {\r\n    WEB = 'web',\r\n}\r\n\r\nexport interface Frame {\r\n    id: string;\r\n    position: RectPosition;\r\n    type: FrameType;\r\n    dimension: RectDimension;\r\n}\r\n\r\nexport interface WebFrame extends Frame {\r\n    url: string;\r\n    type: FrameType.WEB;\r\n}\r\n\r\nexport interface WindowMetadata {\r\n    orientation: Orientation;\r\n    aspectRatioLocked: boolean;\r\n    device: string;\r\n    theme: Theme;\r\n    width: number;\r\n    height: number;\r\n}\r\n"], "names": [], "mappings": ";;;AAGO,IAAA,AAAK,mCAAA;;WAAA", "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 915, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/project/role.ts"], "sourcesContent": ["export enum ProjectRole {\r\n    OWNER = 'owner',\r\n    ADMIN = 'admin',\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,qCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/project/index.ts"], "sourcesContent": ["export * from './canvas';\r\nexport * from './command';\r\nexport * from './domain';\r\nexport * from './frame';\r\nexport * from './invitation';\r\nexport * from './project';\r\nexport * from './rect';\r\nexport * from './role';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/run/index.ts"], "sourcesContent": ["export enum RunState {\r\n    STOPPED = 'stopped',\r\n    SETTING_UP = 'setting-up',\r\n    RUNNING = 'running',\r\n    STOPPING = 'stopping',\r\n    ERROR = 'error',\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,kCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 993, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/style/index.ts"], "sourcesContent": ["export interface StyleChange {\r\n    value: string;\r\n    type: StyleChangeType;\r\n}\r\n\r\nexport enum StyleChangeType {\r\n    Value = 'value',\r\n    Custom = 'custom',\r\n    Remove = 'remove',\r\n}\r\n\r\nexport interface TailwindColor {\r\n    name: string;\r\n    originalKey: string;\r\n    lightColor: string;\r\n    darkColor?: string;\r\n    line?: {\r\n        config?: number;\r\n        css?: {\r\n            lightMode?: number;\r\n            darkMode?: number;\r\n        };\r\n    };\r\n    override?: boolean;\r\n}\r\n"], "names": [], "mappings": ";;;AAKO,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/user/index.ts"], "sourcesContent": ["export * from './settings';\r\nexport * from './user';\r\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/hosting/index.ts"], "sourcesContent": ["export enum PublishStatus {\r\n    UNPUBLISHED = 'unpublished',\r\n    LOADING = 'loading',\r\n    PUBLISHED = 'published',\r\n    ERROR = 'error',\r\n}\r\n\r\nexport interface PublishState {\r\n    status: PublishStatus;\r\n    message: string | null;\r\n}\r\n\r\nexport interface CustomDomain {\r\n    id: string;\r\n    user_id: string;\r\n    domain: string;\r\n    subdomains: string[];\r\n    created_at: string;\r\n    updated_at: string;\r\n}\r\n\r\nexport interface CreateDomainVerificationResponse {\r\n    success: boolean;\r\n    message?: string;\r\n    verificationCode?: string;\r\n}\r\n\r\nexport interface VerifyDomainResponse {\r\n    success: boolean;\r\n    message?: string;\r\n}\r\n\r\nexport interface PublishRequest {\r\n    buildScript: string;\r\n    urls: string[];\r\n    options?: PublishOptions;\r\n}\r\n\r\nexport interface PublishOptions {\r\n    skipBuild?: boolean;\r\n    skipBadge?: boolean;\r\n    buildFlags?: string;\r\n    envVars?: Record<string, string>;\r\n}\r\n\r\nexport interface UnpublishRequest {\r\n    urls: string[];\r\n}\r\n\r\nexport interface PublishResponse {\r\n    success: boolean;\r\n    message?: string;\r\n}\r\n\r\nexport interface GetOwnedDomainsResponse {\r\n    success: boolean;\r\n    message?: string;\r\n    domains?: string[];\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,uCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/models/src/index.ts"], "sourcesContent": ["export * from './actions/';\r\nexport * from './assets/';\r\nexport * from './auth/';\r\nexport * from './chat/';\r\nexport * from './code/';\r\nexport * from './create/';\r\nexport * from './editor/';\r\nexport * from './element/';\r\nexport * from './ide/';\r\nexport * from './llm/';\r\nexport * from './pages/';\r\nexport * from './project/';\r\nexport * from './run/';\r\nexport * from './style/';\r\nexport * from './user/';\r\nexport * from './hosting/';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/message.ts"], "sourcesContent": ["import { ChatMessageRole, type ChatMessage, type ChatMessageContext, type ChatSnapshot } from \"@onlook/models\";\r\nimport type { TextPart } from \"ai\";\r\nimport type { Message as DbMessage } from \"../schema\";\r\n\r\nexport const toMessage = (dbMessage: DbMessage): ChatMessage => {\r\n    if (dbMessage.role === ChatMessageRole.ASSISTANT) {\r\n        return {\r\n            id: dbMessage.id,\r\n            content: dbMessage.content,\r\n            role: dbMessage.role,\r\n            createdAt: dbMessage.createdAt,\r\n            applied: dbMessage.applied,\r\n            snapshots: dbMessage.snapshots,\r\n            parts: dbMessage.parts,\r\n        }\r\n    } else if (dbMessage.role === ChatMessageRole.USER) {\r\n        return {\r\n            id: dbMessage.id,\r\n            content: dbMessage.content,\r\n            role: dbMessage.role,\r\n            createdAt: dbMessage.createdAt,\r\n            context: dbMessage.context,\r\n            parts: dbMessage.parts as TextPart[],\r\n        }\r\n    } else {\r\n        return {\r\n            id: dbMessage.id,\r\n            content: dbMessage.content,\r\n            role: dbMessage.role as ChatMessageRole.SYSTEM,\r\n            createdAt: dbMessage.createdAt,\r\n        }\r\n    }\r\n}\r\n\r\nexport const fromMessage = (conversationId: string, message: ChatMessage): DbMessage => {\r\n    let snapshots: ChatSnapshot = {};\r\n    let context: ChatMessageContext[] = [];\r\n\r\n    if (message.role === ChatMessageRole.ASSISTANT) {\r\n        snapshots = message.snapshots;\r\n    }\r\n\r\n    if (message.role === ChatMessageRole.USER) {\r\n        context = message.context;\r\n    }\r\n\r\n    return {\r\n        id: message.id,\r\n        content: message.content,\r\n        role: message.role,\r\n        createdAt: message.createdAt ?? new Date(),\r\n        conversationId,\r\n        applied: message.role === ChatMessageRole.ASSISTANT ? message.applied ?? false : false,\r\n        snapshots,\r\n        context,\r\n        parts: message.parts\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAIO,MAAM,YAAY,CAAC;IACtB,IAAI,UAAU,IAAI,KAAK,uJAAA,CAAA,kBAAe,CAAC,SAAS,EAAE;QAC9C,OAAO;YACH,IAAI,UAAU,EAAE;YAChB,SAAS,UAAU,OAAO;YAC1B,MAAM,UAAU,IAAI;YACpB,WAAW,UAAU,SAAS;YAC9B,SAAS,UAAU,OAAO;YAC1B,WAAW,UAAU,SAAS;YAC9B,OAAO,UAAU,KAAK;QAC1B;IACJ,OAAO,IAAI,UAAU,IAAI,KAAK,uJAAA,CAAA,kBAAe,CAAC,IAAI,EAAE;QAChD,OAAO;YACH,IAAI,UAAU,EAAE;YAChB,SAAS,UAAU,OAAO;YAC1B,MAAM,UAAU,IAAI;YACpB,WAAW,UAAU,SAAS;YAC9B,SAAS,UAAU,OAAO;YAC1B,OAAO,UAAU,KAAK;QAC1B;IACJ,OAAO;QACH,OAAO;YACH,IAAI,UAAU,EAAE;YAChB,SAAS,UAAU,OAAO;YAC1B,MAAM,UAAU,IAAI;YACpB,WAAW,UAAU,SAAS;QAClC;IACJ;AACJ;AAEO,MAAM,cAAc,CAAC,gBAAwB;IAChD,IAAI,YAA0B,CAAC;IAC/B,IAAI,UAAgC,EAAE;IAEtC,IAAI,QAAQ,IAAI,KAAK,uJAAA,CAAA,kBAAe,CAAC,SAAS,EAAE;QAC5C,YAAY,QAAQ,SAAS;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,uJAAA,CAAA,kBAAe,CAAC,IAAI,EAAE;QACvC,UAAU,QAAQ,OAAO;IAC7B;IAEA,OAAO;QACH,IAAI,QAAQ,EAAE;QACd,SAAS,QAAQ,OAAO;QACxB,MAAM,QAAQ,IAAI;QAClB,WAAW,QAAQ,SAAS,IAAI,IAAI;QACpC;QACA,SAAS,QAAQ,IAAI,KAAK,uJAAA,CAAA,kBAAe,CAAC,SAAS,GAAG,QAAQ,OAAO,IAAI,QAAQ;QACjF;QACA;QACA,OAAO,QAAQ,KAAK;IACxB;AACJ", "debugId": null}}, {"offset": {"line": 1187, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/project.ts"], "sourcesContent": ["import type { PreviewImg, Project } from '@onlook/models';\r\nimport type { Project as DbProject } from '../schema';\r\n\r\nexport const toProject = (\r\n    dbProject: DbProject,\r\n): Project => {\r\n    return {\r\n        id: dbProject.id,\r\n        name: dbProject.name,\r\n        sandbox: {\r\n            id: dbProject.sandboxId,\r\n            url: dbProject.sandboxUrl,\r\n        },\r\n        metadata: {\r\n            createdAt: dbProject.createdAt.toISOString(),\r\n            updatedAt: dbProject.updatedAt.toISOString(),\r\n            previewImg: getPreviewImgFromDb(dbProject),\r\n            description: dbProject.description,\r\n        },\r\n        domains: null,\r\n        commands: null,\r\n        env: null,\r\n    };\r\n};\r\n\r\nexport const fromProject = (project: Project): DbProject => {\r\n    const { previewImgUrl, previewImgPath, previewImgBucket } = getPreviewImgFromModel(project.metadata.previewImg);\r\n    return {\r\n        id: project.id,\r\n        name: project.name,\r\n        sandboxId: project.sandbox.id,\r\n        sandboxUrl: project.sandbox.url,\r\n        createdAt: new Date(project.metadata.createdAt),\r\n        updatedAt: new Date(project.metadata.updatedAt),\r\n        description: project.metadata.description,\r\n        previewImgUrl,\r\n        previewImgPath,\r\n        previewImgBucket,\r\n    };\r\n};\r\n\r\nfunction getPreviewImgFromDb(dbProject: DbProject): PreviewImg | null {\r\n    let previewImg: PreviewImg | null = null;\r\n    if (dbProject.previewImgUrl) {\r\n        previewImg = {\r\n            type: 'url',\r\n            url: dbProject.previewImgUrl,\r\n        };\r\n    } else if (dbProject.previewImgPath && dbProject.previewImgBucket) {\r\n        previewImg = {\r\n            type: 'storage',\r\n            storagePath: {\r\n                bucket: dbProject.previewImgBucket,\r\n                path: dbProject.previewImgPath,\r\n            },\r\n        };\r\n    }\r\n    return previewImg;\r\n}\r\n\r\nfunction getPreviewImgFromModel(previewImg: PreviewImg | null): { previewImgUrl: string | null, previewImgPath: string | null, previewImgBucket: string | null } {\r\n    let res: {\r\n        previewImgUrl: string | null,\r\n        previewImgPath: string | null,\r\n        previewImgBucket: string | null,\r\n    } = {\r\n        previewImgUrl: null,\r\n        previewImgPath: null,\r\n        previewImgBucket: null,\r\n    };\r\n\r\n    if (!previewImg) {\r\n        return res;\r\n    }\r\n\r\n    if (previewImg.type === 'url' && previewImg.url) {\r\n        res.previewImgUrl = previewImg.url;\r\n    } else if (previewImg.type === 'storage' && previewImg.storagePath && previewImg.storagePath.path && previewImg.storagePath.bucket) {\r\n        res.previewImgPath = previewImg.storagePath.path;\r\n        res.previewImgBucket = previewImg.storagePath.bucket;\r\n    }\r\n    return res;\r\n}"], "names": [], "mappings": ";;;;AAGO,MAAM,YAAY,CACrB;IAEA,OAAO;QACH,IAAI,UAAU,EAAE;QAChB,MAAM,UAAU,IAAI;QACpB,SAAS;YACL,IAAI,UAAU,SAAS;YACvB,KAAK,UAAU,UAAU;QAC7B;QACA,UAAU;YACN,WAAW,UAAU,SAAS,CAAC,WAAW;YAC1C,WAAW,UAAU,SAAS,CAAC,WAAW;YAC1C,YAAY,oBAAoB;YAChC,aAAa,UAAU,WAAW;QACtC;QACA,SAAS;QACT,UAAU;QACV,KAAK;IACT;AACJ;AAEO,MAAM,cAAc,CAAC;IACxB,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG,uBAAuB,QAAQ,QAAQ,CAAC,UAAU;IAC9G,OAAO;QACH,IAAI,QAAQ,EAAE;QACd,MAAM,QAAQ,IAAI;QAClB,WAAW,QAAQ,OAAO,CAAC,EAAE;QAC7B,YAAY,QAAQ,OAAO,CAAC,GAAG;QAC/B,WAAW,IAAI,KAAK,QAAQ,QAAQ,CAAC,SAAS;QAC9C,WAAW,IAAI,KAAK,QAAQ,QAAQ,CAAC,SAAS;QAC9C,aAAa,QAAQ,QAAQ,CAAC,WAAW;QACzC;QACA;QACA;IACJ;AACJ;AAEA,SAAS,oBAAoB,SAAoB;IAC7C,IAAI,aAAgC;IACpC,IAAI,UAAU,aAAa,EAAE;QACzB,aAAa;YACT,MAAM;YACN,KAAK,UAAU,aAAa;QAChC;IACJ,OAAO,IAAI,UAAU,cAAc,IAAI,UAAU,gBAAgB,EAAE;QAC/D,aAAa;YACT,MAAM;YACN,aAAa;gBACT,QAAQ,UAAU,gBAAgB;gBAClC,MAAM,UAAU,cAAc;YAClC;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,uBAAuB,UAA6B;IACzD,IAAI,MAIA;QACA,eAAe;QACf,gBAAgB;QAChB,kBAAkB;IACtB;IAEA,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IAEA,IAAI,WAAW,IAAI,KAAK,SAAS,WAAW,GAAG,EAAE;QAC7C,IAAI,aAAa,GAAG,WAAW,GAAG;IACtC,OAAO,IAAI,WAAW,IAAI,KAAK,aAAa,WAAW,WAAW,IAAI,WAAW,WAAW,CAAC,IAAI,IAAI,WAAW,WAAW,CAAC,MAAM,EAAE;QAChI,IAAI,cAAc,GAAG,WAAW,WAAW,CAAC,IAAI;QAChD,IAAI,gBAAgB,GAAG,WAAW,WAAW,CAAC,MAAM;IACxD;IACA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/user.ts"], "sourcesContent": ["import { DefaultSettings } from '@onlook/constants';\r\nimport type { UserMetadata, UserSettings } from '@onlook/models';\r\nimport { get } from 'lodash';\r\nimport type { AuthUser, UserSettings as DbUserSettings } from '../schema';\r\n\r\nexport const toUserSettings = (settings: DbUserSettings): UserSettings => {\r\n    return {\r\n        id: settings.id,\r\n        chat: {\r\n            autoApplyCode: settings.autoApplyCode ?? DefaultSettings.CHAT_SETTINGS.autoApplyCode,\r\n            expandCodeBlocks:\r\n                settings.expandCodeBlocks ?? DefaultSettings.CHAT_SETTINGS.expandCodeBlocks,\r\n            showSuggestions:\r\n                settings.showSuggestions ?? DefaultSettings.CHAT_SETTINGS.showSuggestions,\r\n            showMiniChat: settings.showMiniChat ?? DefaultSettings.CHAT_SETTINGS.showMiniChat,\r\n        },\r\n    };\r\n};\r\n\r\nexport const fromUserSettings = (userId: string, settings: UserSettings): DbUserSettings => {\r\n    return {\r\n        id: settings.id,\r\n        userId,\r\n        autoApplyCode: settings.chat.autoApplyCode,\r\n        expandCodeBlocks: settings.chat.expandCodeBlocks,\r\n        showSuggestions: settings.chat.showSuggestions,\r\n        showMiniChat: settings.chat.showMiniChat,\r\n    };\r\n};\r\n\r\nexport const fromAuthUser = (authUser: AuthUser): UserMetadata => {\r\n    return {\r\n        id: authUser.id,\r\n        name: get(authUser.rawUserMetaData, 'full_name'),\r\n        email: authUser.email,\r\n        avatarUrl: get(authUser.rawUserMetaData, 'avatar_url'),\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAEA;;;AAGO,MAAM,iBAAiB,CAAC;IAC3B,OAAO;QACH,IAAI,SAAS,EAAE;QACf,MAAM;YACF,eAAe,SAAS,aAAa,IAAI,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,aAAa;YACpF,kBACI,SAAS,gBAAgB,IAAI,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,gBAAgB;YAC/E,iBACI,SAAS,eAAe,IAAI,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,eAAe;YAC7E,cAAc,SAAS,YAAY,IAAI,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,YAAY;QACrF;IACJ;AACJ;AAEO,MAAM,mBAAmB,CAAC,QAAgB;IAC7C,OAAO;QACH,IAAI,SAAS,EAAE;QACf;QACA,eAAe,SAAS,IAAI,CAAC,aAAa;QAC1C,kBAAkB,SAAS,IAAI,CAAC,gBAAgB;QAChD,iBAAiB,SAAS,IAAI,CAAC,eAAe;QAC9C,cAAc,SAAS,IAAI,CAAC,YAAY;IAC5C;AACJ;AAEO,MAAM,eAAe,CAAC;IACzB,OAAO;QACH,IAAI,SAAS,EAAE;QACf,MAAM,CAAA,GAAA,6HAAA,CAAA,UAAG,AAAD,EAAE,SAAS,eAAe,EAAE;QACpC,OAAO,SAAS,KAAK;QACrB,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAG,AAAD,EAAE,SAAS,eAAe,EAAE;IAC7C;AACJ", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/dto/index.ts"], "sourcesContent": ["export * from './canvas';\r\nexport * from './conversation';\r\nexport * from './frame';\r\nexport * from './message';\r\nexport * from './project';\r\nexport * from './user';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1344, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/supabase/user.ts"], "sourcesContent": ["import { jsonb, pgSchema, text, timestamp, uuid } from 'drizzle-orm/pg-core';\r\n\r\nconst authSchema = pgSchema('auth');\r\n\r\nexport const authUsers = authSchema.table('users', {\r\n    id: uuid('id').primaryKey(),\r\n    email: text('email').notNull(),\r\n    emailConfirmedAt: timestamp('email_confirmed_at'),\r\n    rawUserMetaData: jsonb('raw_user_meta_data'),\r\n});\r\n\r\nexport type AuthUser = typeof authUsers.$inferSelect;\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;;AAEA,MAAM,aAAa,CAAA,GAAA,sJAAA,CAAA,WAAQ,AAAD,EAAE;AAErB,MAAM,YAAY,WAAW,KAAK,CAAC,SAAS;IAC/C,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,OAAO,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,kBAAkB,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE;IAC5B,iBAAiB,CAAA,GAAA,gKAAA,CAAA,QAAK,AAAD,EAAE;AAC3B", "debugId": null}}, {"offset": {"line": 1366, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/supabase/index.ts"], "sourcesContent": ["export * from './user';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/user/user-canvas.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { numeric, pgTable, primaryKey, uuid } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema, createUpdateSchema } from 'drizzle-zod';\r\nimport { canvases } from '../../schema';\r\nimport { users } from './user';\r\n\r\nexport const userCanvases = pgTable(\r\n    'user_canvases',\r\n    {\r\n        userId: uuid('user_id')\r\n            .notNull()\r\n            .references(() => users.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        canvasId: uuid('canvas_id')\r\n            .notNull()\r\n            .references(() => canvases.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        scale: numeric('scale').notNull(),\r\n        x: numeric('x').notNull(),\r\n        y: numeric('y').notNull(),\r\n    },\r\n    (table) => [primaryKey({ columns: [table.userId, table.canvasId] })],\r\n).enableRLS();\r\n\r\nexport const userCanvasInsertSchema = createInsertSchema(userCanvases);\r\nexport const userCanvasUpdateSchema = createUpdateSchema(userCanvases);\r\n\r\nexport type UserCanvas = typeof userCanvases.$inferSelect;\r\nexport type NewUserCanvas = typeof userCanvases.$inferInsert;\r\n\r\nexport const userCanvasesRelations = relations(userCanvases, ({ one }) => ({\r\n    user: one(users, {\r\n        fields: [userCanvases.userId],\r\n        references: [users.id],\r\n    }),\r\n    canvas: one(canvases, {\r\n        fields: [userCanvases.canvasId],\r\n        references: [canvases.id],\r\n    }),\r\n}));\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;;;;;;AAEO,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAC9B,iBACA;IACI,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACR,OAAO,GACP,UAAU,CAAC,IAAM,+IAAA,CAAA,QAAK,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC3E,UAAU,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,aACV,OAAO,GACP,UAAU,CAAC,IAAM,8JAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC9E,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO;IAC/B,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,KAAK,OAAO;IACvB,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,KAAK,OAAO;AAC3B,GACA,CAAC,QAAU;QAAC,CAAA,GAAA,+JAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,MAAM;gBAAE,MAAM,QAAQ;aAAC;QAAC;KAAG,EACtE,SAAS;AAEJ,MAAM,yBAAyB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAClD,MAAM,yBAAyB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAKlD,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,cAAc,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACvE,MAAM,IAAI,+IAAA,CAAA,QAAK,EAAE;YACb,QAAQ;gBAAC,aAAa,MAAM;aAAC;YAC7B,YAAY;gBAAC,+IAAA,CAAA,QAAK,CAAC,EAAE;aAAC;QAC1B;QACA,QAAQ,IAAI,8JAAA,CAAA,WAAQ,EAAE;YAClB,QAAQ;gBAAC,aAAa,QAAQ;aAAC;YAC/B,YAAY;gBAAC,8JAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 1450, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/user/user-project.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgEnum, pgTable, primaryKey, timestamp, uuid } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema } from 'drizzle-zod';\r\nimport { projects } from '../project';\r\nimport { users } from './user';\r\nimport { ProjectRole } from '@onlook/models';\r\n\r\nexport const projectRole = pgEnum('project_role', ProjectRole);\r\n\r\nexport const userProjects = pgTable(\r\n    'user_projects',\r\n    {\r\n        userId: uuid('user_id')\r\n            .notNull()\r\n            .references(() => users.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        projectId: uuid('project_id')\r\n            .notNull()\r\n            .references(() => projects.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),\r\n        role: projectRole('role').notNull(),\r\n    },\r\n    (table) => [primaryKey({ columns: [table.userId, table.projectId] })],\r\n).enableRLS();\r\n\r\nexport const userProjectsRelations = relations(userProjects, ({ one }) => ({\r\n    user: one(users, {\r\n        fields: [userProjects.userId],\r\n        references: [users.id],\r\n    }),\r\n    project: one(projects, {\r\n        fields: [userProjects.projectId],\r\n        references: [projects.id],\r\n    }),\r\n}));\r\n\r\nexport const userProjectInsertSchema = createInsertSchema(userProjects);\r\nexport type UserProject = typeof userProjects.$inferSelect;\r\nexport type NewUserProject = typeof userProjects.$inferInsert;\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAAA;;;;;;;AAEO,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,gBAAgB,4IAAA,CAAA,cAAW;AAEtD,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAC9B,iBACA;IACI,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACR,OAAO,GACP,UAAU,CAAC,IAAM,+IAAA,CAAA,QAAK,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC3E,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC9E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU;IACrE,MAAM,YAAY,QAAQ,OAAO;AACrC,GACA,CAAC,QAAU;QAAC,CAAA,GAAA,+JAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,MAAM;gBAAE,MAAM,SAAS;aAAC;QAAC;KAAG,EACvE,SAAS;AAEJ,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,cAAc,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACvE,MAAM,IAAI,+IAAA,CAAA,QAAK,EAAE;YACb,QAAQ;gBAAC,aAAa,MAAM;aAAC;YAC7B,YAAY;gBAAC,+IAAA,CAAA,QAAK,CAAC,EAAE;aAAC;QAC1B;QACA,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,aAAa,SAAS;aAAC;YAChC,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;IACJ,CAAC;AAEM,MAAM,0BAA0B,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1521, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/user/user.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgTable, uuid } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema } from 'drizzle-zod';\r\nimport { authUsers } from '../supabase';\r\nimport { userSettings } from './settings';\r\nimport { userCanvases } from './user-canvas';\r\nimport { userProjects } from './user-project';\r\n\r\nexport const users = pgTable('users', {\r\n    id: uuid('id')\r\n        .primaryKey()\r\n        .references(() => authUsers.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n}).enableRLS();\r\n\r\nexport const usersRelations = relations(users, ({ many, one }) => ({\r\n    userCanvases: many(userCanvases),\r\n    userProjects: many(userProjects),\r\n    userSettings: one(userSettings),\r\n    authUser: one(authUsers),\r\n}));\r\n\r\nexport const userInsertSchema = createInsertSchema(users);\r\nexport type User = typeof users.$inferSelect;\r\nexport type NewUser = typeof users.$inferInsert;\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;AAEO,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,SAAS;IAClC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MACJ,UAAU,GACV,UAAU,CAAC,IAAM,mJAAA,CAAA,YAAS,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;AACnF,GAAG,SAAS;AAEL,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,GAAK,CAAC;QAC/D,cAAc,KAAK,yJAAA,CAAA,eAAY;QAC/B,cAAc,KAAK,0JAAA,CAAA,eAAY;QAC/B,cAAc,IAAI,mJAAA,CAAA,eAAY;QAC9B,UAAU,IAAI,mJAAA,CAAA,YAAS;IAC3B,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1561, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/user/settings.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { boolean, pgTable, uuid } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema } from 'drizzle-zod';\r\nimport { users } from './user';\r\n\r\nexport const userSettings = pgTable(\"user_settings\", {\r\n    id: uuid(\"id\")\r\n        .primaryKey(),\r\n    userId: uuid(\"user_id\")\r\n        .notNull()\r\n        .references(() => users.id, { onDelete: \"cascade\", onUpdate: \"cascade\" })\r\n        .unique(),\r\n    autoApplyCode: boolean(\"auto_apply_code\").notNull().default(true),\r\n    expandCodeBlocks: boolean(\"expand_code_blocks\").notNull().default(true),\r\n    showSuggestions: boolean(\"show_suggestions\").notNull().default(true),\r\n    showMiniChat: boolean(\"show_mini_chat\").notNull().default(true),\r\n}).enableRLS();\r\n\r\nexport const userSettingsRelations = relations(userSettings, ({ one }) => ({\r\n    user: one(users, {\r\n        fields: [userSettings.userId],\r\n        references: [users.id],\r\n    }),\r\n}));\r\n\r\nexport const userSettingsInsertSchema = createInsertSchema(userSettings);\r\nexport type UserSettings = typeof userSettings.$inferSelect;\r\nexport type NewUserSettings = typeof userSettings.$inferInsert;\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;AAEO,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;IACjD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MACJ,UAAU;IACf,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACR,OAAO,GACP,UAAU,CAAC,IAAM,+IAAA,CAAA,QAAK,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU,GACtE,MAAM;IACX,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB,OAAO,GAAG,OAAO,CAAC;IAC5D,kBAAkB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB,OAAO,GAAG,OAAO,CAAC;IAClE,iBAAiB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB,OAAO,GAAG,OAAO,CAAC;IAC/D,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO,GAAG,OAAO,CAAC;AAC9D,GAAG,SAAS;AAEL,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,cAAc,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACvE,MAAM,IAAI,+IAAA,CAAA,QAAK,EAAE;YACb,QAAQ;gBAAC,aAAa,MAAM;aAAC;YAC7B,YAAY;gBAAC,+IAAA,CAAA,QAAK,CAAC,EAAE;aAAC;QAC1B;IACJ,CAAC;AAEM,MAAM,2BAA2B,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/user/index.ts"], "sourcesContent": ["export * from './settings';\r\nexport * from './user';\r\nexport * from './user-canvas';\r\nexport * from './user-project';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1631, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/chat/message.ts"], "sourcesContent": ["import { ChatMessageRole, type ChatMessageContext, type ChatSnapshot } from \"@onlook/models\";\r\nimport type { Message as AiMessage } from \"ai\";\r\nimport { relations } from \"drizzle-orm\";\r\nimport { boolean, jsonb, pgEnum, pgTable, text, timestamp, uuid } from \"drizzle-orm/pg-core\";\r\nimport { createInsertSchema } from \"drizzle-zod\";\r\nimport { conversations } from \"./conversation\";\r\n\r\nexport const CONVERSATION_MESSAGe_RELATION_NAME = 'conversation_messages';\r\nexport const messageRole = pgEnum(\"role\", ChatMessageRole);\r\n\r\nexport const messages = pgTable(\"messages\", {\r\n    id: uuid(\"id\").primaryKey().defaultRandom(),\r\n    conversationId: uuid(\"conversation_id\")\r\n        .notNull()\r\n        .references(() => conversations.id, { onDelete: \"cascade\", onUpdate: \"cascade\" }),\r\n    content: text(\"content\").notNull(),\r\n    createdAt: timestamp(\"created_at\", { withTimezone: true }).defaultNow().notNull(),\r\n    role: messageRole(\"role\").notNull(),\r\n    applied: boolean(\"applied\").default(false).notNull(),\r\n    snapshots: jsonb(\"snapshots\").$type<ChatSnapshot>().default({}).notNull(),\r\n    context: jsonb(\"context\").$type<ChatMessageContext[]>().default([]).notNull(),\r\n    parts: jsonb(\"parts\").$type<AiMessage['parts']>().default([]).notNull(),\r\n}).enableRLS();\r\n\r\nexport const messageInsertSchema = createInsertSchema(messages);\r\n\r\nexport const messageRelations = relations(messages, ({ one }) => ({\r\n    conversation: one(conversations, {\r\n        fields: [messages.conversationId],\r\n        references: [conversations.id],\r\n        relationName: CONVERSATION_MESSAGe_RELATION_NAME,\r\n    }),\r\n}));\r\n\r\nexport type Message = typeof messages.$inferSelect;\r\nexport type NewMessage = typeof messages.$inferInsert;\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAEO,MAAM,qCAAqC;AAC3C,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,uJAAA,CAAA,kBAAe;AAElD,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IACxC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,gBAAgB,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,mBAChB,OAAO,GACP,UAAU,CAAC,IAAM,kKAAA,CAAA,gBAAa,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IACnF,SAAS,CAAA,GAAA,+JAA<PERSON>,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,MAAM,YAAY,QAAQ,OAAO;IACjC,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,CAAC,OAAO,OAAO;IAClD,WAAW,CAAA,GAAA,gKAAA,CAAA,QAAK,AAAD,EAAE,aAAa,KAAK,GAAiB,OAAO,CAAC,CAAC,GAAG,OAAO;IACvE,SAAS,CAAA,GAAA,gKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,KAAK,GAAyB,OAAO,CAAC,EAAE,EAAE,OAAO;IAC3E,OAAO,CAAA,GAAA,gKAAA,CAAA,QAAK,AAAD,EAAE,SAAS,KAAK,GAAuB,OAAO,CAAC,EAAE,EAAE,OAAO;AACzE,GAAG,SAAS;AAEL,MAAM,sBAAsB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAE/C,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC9D,cAAc,IAAI,kKAAA,CAAA,gBAAa,EAAE;YAC7B,QAAQ;gBAAC,SAAS,cAAc;aAAC;YACjC,YAAY;gBAAC,kKAAA,CAAA,gBAAa,CAAC,EAAE;aAAC;YAC9B,cAAc;QAClB;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/chat/conversation.ts"], "sourcesContent": ["import { relations } from \"drizzle-orm\";\r\nimport { pgTable, timestamp, uuid, varchar } from \"drizzle-orm/pg-core\";\r\nimport { createInsertSchema } from \"drizzle-zod\";\r\nimport { projects } from \"../project\";\r\nimport { CONVERSATION_MESSAGe_RELATION_NAME, messages } from \"./message\";\r\n\r\nexport const PROJECT_CONVERSATION_RELATION_NAME = \"project_conversations\";\r\n\r\nexport const conversations = pgTable(\"conversations\", {\r\n    id: uuid(\"id\").primaryKey().defaultRandom(),\r\n    projectId: uuid(\"project_id\")\r\n        .notNull()\r\n        .references(() => projects.id, { onDelete: \"cascade\", onUpdate: \"cascade\" }),\r\n    displayName: varchar(\"display_name\"),\r\n    createdAt: timestamp(\"created_at\", { withTimezone: true }).defaultNow().notNull(),\r\n    updatedAt: timestamp(\"updated_at\", { withTimezone: true }).defaultNow().notNull(),\r\n}).enableRLS();\r\n\r\nexport const conversationInsertSchema = createInsertSchema(conversations);\r\n\r\nexport const conversationRelations = relations(conversations, ({ one, many }) => ({\r\n    project: one(projects, {\r\n        fields: [conversations.projectId],\r\n        references: [projects.id],\r\n        relationName: PROJECT_CONVERSATION_RELATION_NAME,\r\n    }),\r\n    messages: many(messages, {\r\n        relationName: CONVERSATION_MESSAGe_RELATION_NAME,\r\n    }),\r\n}));\r\n\r\nexport type Conversation = typeof conversations.$inferSelect;\r\nexport type NewConversation = typeof conversations.$inferInsert;"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAEO,MAAM,qCAAqC;AAE3C,MAAM,gBAAgB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;IAClD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC9E,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;IACrB,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;AACnF,GAAG,SAAS;AAEL,MAAM,2BAA2B,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAEpD,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,eAAe,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QAC9E,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,cAAc,SAAS;aAAC;YACjC,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;YACzB,cAAc;QAClB;QACA,UAAU,KAAK,6JAAA,CAAA,WAAQ,EAAE;YACrB,cAAc,6JAAA,CAAA,qCAAkC;QACpD;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 1746, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/invitation.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgEnum, pgTable, text, timestamp, uuid, varchar, unique } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema, createUpdateSchema } from 'drizzle-zod';\r\nimport { projectRole, users } from '../user';\r\nimport { projects } from './project';\r\n\r\nexport const projectInvitations = pgTable(\r\n    'project_invitations',\r\n    {\r\n        id: uuid('id').primaryKey().defaultRandom(),\r\n        projectId: uuid('project_id')\r\n            .notNull()\r\n            .references(() => projects.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        inviterId: uuid('inviter_id')\r\n            .notNull()\r\n            .references(() => users.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n        inviteeEmail: varchar('invitee_email').notNull(),\r\n        token: varchar('token').notNull().unique(),\r\n        role: projectRole('role').notNull(),\r\n        expiresAt: timestamp('expires_at', { withTimezone: true }).notNull(),\r\n        createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),\r\n        updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),\r\n    },\r\n    (table) => ({\r\n        uniqueEmailProject: unique().on(table.inviteeEmail, table.projectId),\r\n    }),\r\n).enableRLS();\r\n\r\nexport const projectInvitationInsertSchema = createInsertSchema(projectInvitations);\r\nexport const projectInvitationUpdateSchema = createUpdateSchema(projectInvitations);\r\n\r\nexport type ProjectInvitation = typeof projectInvitations.$inferSelect;\r\nexport type NewProjectInvitation = typeof projectInvitations.$inferInsert;\r\n\r\nexport const projectInvitationRelations = relations(projectInvitations, ({ one }) => ({\r\n    project: one(projects, {\r\n        fields: [projectInvitations.projectId],\r\n        references: [projects.id],\r\n    }),\r\n    inviter: one(users, {\r\n        fields: [projectInvitations.inviterId],\r\n        references: [users.id],\r\n        relationName: 'inviter',\r\n    }),\r\n}));\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAEO,MAAM,qBAAqB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EACpC,uBACA;IACI,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC9E,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,+IAAA,CAAA,QAAK,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC3E,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,OAAO;IAC9C,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACxC,MAAM,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,OAAO;IACjC,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,OAAO;IAClE,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;AACnF,GACA,CAAC,QAAU,CAAC;QACR,oBAAoB,CAAA,GAAA,oKAAA,CAAA,SAAM,AAAD,IAAI,EAAE,CAAC,MAAM,YAAY,EAAE,MAAM,SAAS;IACvE,CAAC,GACH,SAAS;AAEJ,MAAM,gCAAgC,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AACzD,MAAM,gCAAgC,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAKzD,MAAM,6BAA6B,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAClF,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,mBAAmB,SAAS;aAAC;YACtC,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;QACA,SAAS,IAAI,+IAAA,CAAA,QAAK,EAAE;YAChB,QAAQ;gBAAC,mBAAmB,SAAS;aAAC;YACtC,YAAY;gBAAC,+IAAA,CAAA,QAAK,CAAC,EAAE;aAAC;YACtB,cAAc;QAClB;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/project.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';\r\nimport { createInsertSchema } from 'drizzle-zod';\r\nimport { userProjects } from '../user';\r\nimport { canvases } from './canvas';\r\nimport { conversations, PROJECT_CONVERSATION_RELATION_NAME } from './chat/conversation';\r\nimport { projectInvitations } from './invitation';\r\n\r\nexport const projects = pgTable('projects', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n\r\n    // metadata\r\n    name: varchar('name').notNull(),\r\n    description: text('description'),\r\n    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),\r\n    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),\r\n\r\n    // preview image\r\n    previewImgUrl: varchar('preview_img_url'),\r\n    previewImgPath: varchar('preview_img_path'),\r\n    previewImgBucket: varchar('preview_img_bucket'),\r\n\r\n    // sandbox\r\n    sandboxId: varchar('sandbox_id').notNull(),\r\n    sandboxUrl: varchar('sandbox_url').notNull(),\r\n}).enableRLS();\r\n\r\nexport const projectInsertSchema = createInsertSchema(projects);\r\n\r\nexport const projectRelations = relations(projects, ({ one, many }) => ({\r\n    canvas: one(canvases, {\r\n        fields: [projects.id],\r\n        references: [canvases.projectId],\r\n    }),\r\n    userProjects: many(userProjects),\r\n    conversations: many(conversations, {\r\n        relationName: PROJECT_CONVERSATION_RELATION_NAME,\r\n    }),\r\n    projectInvitations: many(projectInvitations),\r\n}));\r\n\r\nexport type Project = typeof projects.$inferSelect;\r\nexport type NewProject = typeof projects.$inferInsert;\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;;;;;;AAEO,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IACxC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IAEzC,WAAW;IACX,MAAM,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;IAC7B,aAAa,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IAClB,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAC/E,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,cAAc;IAAK,GAAG,UAAU,GAAG,OAAO;IAE/E,gBAAgB;IAChB,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;IACvB,gBAAgB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;IACxB,kBAAkB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;IAE1B,UAAU;IACV,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO;IACxC,YAAY,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO;AAC9C,GAAG,SAAS;AAEL,MAAM,sBAAsB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAE/C,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACpE,QAAQ,IAAI,8JAAA,CAAA,WAAQ,EAAE;YAClB,QAAQ;gBAAC,SAAS,EAAE;aAAC;YACrB,YAAY;gBAAC,8JAAA,CAAA,WAAQ,CAAC,SAAS;aAAC;QACpC;QACA,cAAc,KAAK,0JAAA,CAAA,eAAY;QAC/B,eAAe,KAAK,kKAAA,CAAA,gBAAa,EAAE;YAC/B,cAAc,kKAAA,CAAA,qCAAkC;QACpD;QACA,oBAAoB,KAAK,wJAAA,CAAA,qBAAkB;IAC/C,CAAC", "debugId": null}}, {"offset": {"line": 1886, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/canvas/frame.ts"], "sourcesContent": ["import { FrameType } from \"@onlook/models\";\r\nimport { relations } from \"drizzle-orm\";\r\nimport { numeric, pgEnum, pgTable, uuid, varchar } from \"drizzle-orm/pg-core\";\r\nimport { canvases } from \"./canvas\";\r\nimport { createInsertSchema, createUpdateSchema } from \"drizzle-zod\";\r\n\r\nexport const frameType = pgEnum(\"frame_type\", FrameType);\r\n\r\nexport const frames = pgTable(\"frames\", {\r\n    id: uuid(\"id\").primaryKey().defaultRandom(),\r\n    canvasId: uuid(\"canvas_id\")\r\n        .notNull()\r\n        .references(() => canvases.id, { onDelete: \"cascade\", onUpdate: \"cascade\" }),\r\n    type: frameType(\"type\").notNull(),\r\n    url: varchar(\"url\").notNull(),\r\n\r\n    x: numeric(\"x\").notNull(),\r\n    y: numeric(\"y\").notNull(),\r\n\r\n    width: numeric(\"width\").notNull(),\r\n    height: numeric(\"height\").notNull(),\r\n}).enableRLS();\r\n\r\nexport const frameInsertSchema = createInsertSchema(frames);\r\nexport const frameUpdateSchema = createUpdateSchema(frames);\r\n\r\nexport type Frame = typeof frames.$inferSelect;\r\nexport type NewFrame = typeof frames.$inferInsert;\r\n\r\nexport const frameRelations = relations(frames, ({ one }) => ({\r\n    canvas: one(canvases, {\r\n        fields: [frames.canvasId],\r\n        references: [canvases.id],\r\n    }),\r\n}));"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAEO,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,cAAc,6IAAA,CAAA,YAAS;AAEhD,MAAM,SAAS,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,UAAU;IACpC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,UAAU,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,aACV,OAAO,GACP,UAAU,CAAC,IAAM,8JAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IAC9E,MAAM,UAAU,QAAQ,OAAO;IAC/B,KAAK,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;IAE3B,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,KAAK,OAAO;IACvB,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,KAAK,OAAO;IAEvB,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO;IAC/B,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;AACrC,GAAG,SAAS;AAEL,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAC7C,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAK7C,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAC1D,QAAQ,IAAI,8JAAA,CAAA,WAAQ,EAAE;YAClB,QAAQ;gBAAC,OAAO,QAAQ;aAAC;YACzB,YAAY;gBAAC,8JAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 1940, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/canvas/canvas.ts"], "sourcesContent": ["import { relations } from 'drizzle-orm';\r\nimport { pgTable, uuid } from 'drizzle-orm/pg-core';\r\nimport { projects } from '../project';\r\nimport { frames } from './frame';\r\nimport { createUpdateSchema } from 'drizzle-zod';\r\nimport { userCanvases } from '../../user';\r\n\r\nexport const canvases = pgTable('canvas', {\r\n    id: uuid('id').primaryKey().defaultRandom(),\r\n    projectId: uuid('project_id')\r\n        .notNull()\r\n        .references(() => projects.id, { onDelete: 'cascade', onUpdate: 'cascade' }),\r\n}).enableRLS();\r\n\r\nexport const canvasUpdateSchema = createUpdateSchema(canvases);\r\n\r\nexport type Canvas = typeof canvases.$inferSelect;\r\nexport type NewCanvas = typeof canvases.$inferInsert;\r\n\r\nexport const canvasRelations = relations(canvases, ({ one, many }) => ({\r\n    frames: many(frames),\r\n    userCanvases: many(userCanvases),\r\n    project: one(projects, {\r\n        fields: [canvases.projectId],\r\n        references: [projects.id],\r\n    }),\r\n}));\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;;AAEO,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,UAAU;IACtC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,aAAa;IACzC,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cACX,OAAO,GACP,UAAU,CAAC,IAAM,qJAAA,CAAA,WAAQ,CAAC,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;AAClF,GAAG,SAAS;AAEL,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;AAK9C,MAAM,kBAAkB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;QACnE,QAAQ,KAAK,6JAAA,CAAA,SAAM;QACnB,cAAc,KAAK,yJAAA,CAAA,eAAY;QAC/B,SAAS,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACnB,QAAQ;gBAAC,SAAS,SAAS;aAAC;YAC5B,YAAY;gBAAC,qJAAA,CAAA,WAAQ,CAAC,EAAE;aAAC;QAC7B;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 1985, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/canvas/index.ts"], "sourcesContent": ["export * from './canvas';\r\nexport * from './frame';\r\n\r\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 2006, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/chat/index.ts"], "sourcesContent": ["export * from './conversation';\r\nexport * from './message';\r\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 2027, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/project/index.ts"], "sourcesContent": ["export * from './canvas';\r\nexport * from './chat';\r\nexport * from './invitation';\r\nexport * from './project';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2054, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/schema/index.ts"], "sourcesContent": ["export * from './project';\r\nexport * from './supabase';\r\nexport * from './user';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2078, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/seed/constants.ts"], "sourcesContent": ["export const SEED_USER = {\r\n    EMAIL: '<EMAIL>',\r\n    PASSWORD: 'test',\r\n    ID: '2585ea6b-6303-4f21-977c-62af2f5a21f5',\r\n} "], "names": [], "mappings": ";;;AAAO,MAAM,YAAY;IACrB,OAAO;IACP,UAAU;IACV,IAAI;AACR", "debugId": null}}, {"offset": {"line": 2092, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/packages/db/src/index.ts"], "sourcesContent": ["export * from './dto';\r\nexport * from './schema';\r\nexport * from './seed/constants';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2116, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/login/actions.tsx"], "sourcesContent": ["'use server';\r\n\r\nimport { Routes } from '@/utils/constants';\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { SEED_USER } from '@onlook/db';\r\nimport { SignInMethod } from '@onlook/models';\r\nimport { headers } from 'next/headers';\r\nimport { redirect } from 'next/navigation';\r\n\r\nexport async function login(provider: SignInMethod) {\r\n    const supabase = await createClient();\r\n    const origin = (await headers()).get('origin');\r\n\r\n    // If already session, redirect\r\n    const {\r\n        data: { session },\r\n    } = await supabase.auth.getSession();\r\n    if (session) {\r\n        redirect('/');\r\n    }\r\n\r\n    // Start OAuth flow\r\n    // Note: User object will be created in the auth callback route if it doesn't exist\r\n    const { data, error } = await supabase.auth.signInWithOAuth({\r\n        provider,\r\n        options: {\r\n            redirectTo: `${origin}/auth/callback`,\r\n        },\r\n    });\r\n\r\n    if (error) {\r\n        redirect('/error');\r\n    }\r\n\r\n    redirect(data.url);\r\n}\r\n\r\nexport async function devLogin() {\r\n    if (process.env.NODE_ENV !== 'development') {\r\n        throw new Error('Dev login is only available in development mode');\r\n    }\r\n\r\n    const supabase = await createClient();\r\n\r\n    const {\r\n        data: { session },\r\n    } = await supabase.auth.getSession();\r\n    if (session) {\r\n        redirect('/');\r\n    }\r\n\r\n    const { data, error } = await supabase.auth.signInWithPassword({\r\n        email: SEED_USER.EMAIL,\r\n        password: SEED_USER.PASSWORD,\r\n    });\r\n\r\n    if (error) {\r\n        console.error('Error signing in with password:', error);\r\n        throw new Error('Error signing in with password');\r\n    }\r\n\r\n    redirect(Routes.HOME);\r\n}"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAAA;;;;;;;;;AAEO,eAAe,MAAM,QAAsB;IAC9C,MAAM,WAAW,MAAM,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD;IAClC,MAAM,SAAS,CAAC,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,GAAG,EAAE,GAAG,CAAC;IAErC,+BAA+B;IAC/B,MAAM,EACF,MAAM,EAAE,OAAO,EAAE,EACpB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAClC,IAAI,SAAS;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACb;IAEA,mBAAmB;IACnB,mFAAmF;IACnF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;QACxD;QACA,SAAS;YACL,YAAY,GAAG,OAAO,cAAc,CAAC;QACzC;IACJ;IAEA,IAAI,OAAO;QACP,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACb;IAEA,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,GAAG;AACrB;AAEO,eAAe;IAClB,uCAA4C;;IAE5C;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACF,MAAM,EAAE,OAAO,EAAE,EACpB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAClC,IAAI,SAAS;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACb;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;QAC3D,OAAO,0IAAA,CAAA,YAAS,CAAC,KAAK;QACtB,UAAU,0IAAA,CAAA,YAAS,CAAC,QAAQ;IAChC;IAEA,IAAI,OAAO;QACP,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM,IAAI,MAAM;IACpB;IAEA,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,2JAAA,CAAA,SAAM,CAAC,IAAI;AACxB;;;IArDsB;IA4BA;;AA5BA,+OAAA;AA4BA,+OAAA", "debugId": null}}, {"offset": {"line": 2190, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/.next-internal/server/app/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {devLogin as '00e1d0c967bce021073b78c14855b4307fdfa6ca6c'} from 'ACTIONS_MODULE0'\nexport {login as '405218b8a67497de663f04930c2b88e7a261d33a4e'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2245, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/apps/web/client/src/app/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/app/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 2259, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/oss/onlook/apps/web/client/src/app/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/apps/web/client/src/app/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/client/src/app/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 2273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}