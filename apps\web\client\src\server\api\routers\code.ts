import { applyCodeChange } from '@onlook/ai';
import { z } from 'zod';
import { createTR<PERSON>Router, protectedProcedure } from '../trpc';

export const codeRouter = createTRPCRouter({
    applyDiff: protectedProcedure
        .input(z.object({ originalCode: z.string(), updateSnippet: z.string() }))
        .mutation(async ({ ctx, input }): Promise<{ result: string | null, error: string | null }> => {
            try {
                const result = await applyCodeChange(input.originalCode, input.updateSnippet);
                if (!result) {
                    throw new Error('Failed to apply code change. Please try again.');
                }
                return {
                    result,
                    error: null,
                };
            } catch (error) {
                console.error('Failed to apply code change', error);
                return {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    result: null,
                };
            }
        }),
});