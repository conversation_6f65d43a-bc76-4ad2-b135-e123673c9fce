{"name": "@onlook/db", "description": "Drizzle database schema", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint .", "typecheck": "tsc --noEmit", "db:gen": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:migrate": "drizzle-kit migrate", "db:seed": "bun src/seed/seed.ts", "db:drop": "drizzle-kit drop"}, "dependencies": {"dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "drizzle-zod": "^0.7.1", "pg": "^8.11.3", "postgres": "^3.4.5", "uuid": "^11.1.0"}, "devDependencies": {"drizzle-kit": "^0.31.1", "typescript": "^5.8.2"}}