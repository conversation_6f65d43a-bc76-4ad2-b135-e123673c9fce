import type React from 'react';

interface H6IconProps {
    className?: string;
    letterClassName?: string;
    levelClassName?: string;
    [key: string]: any;
}

const H6Icon: React.FC<H6IconProps> = ({
    className,
    letterClassName,
    levelClassName,
    ...props
}) => (
    <svg
        width="15"
        height="15"
        viewBox="0 0 15 15"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        {...props}
    >
        <path
            className={letterClassName}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.30469 2.50078C8.30469 2.25225 8.50616 2.05078 8.75469 2.05078H12.2547C12.5032 2.05078 12.7047 2.25225 12.7047 2.50078C12.7047 2.74931 12.5032 2.95078 12.2547 2.95078H11.0503V4.00039C11.0336 4.00013 11.0168 4 11 4C10.6368 4 10.2848 4.06052 9.95034 4.17393V2.95078H8.75469C8.50616 2.95078 8.30469 2.74931 8.30469 2.50078ZM7.31541 7.05062H5.05034V2.95078H6.25469C6.50322 2.95078 6.70469 2.74931 6.70469 2.50078C6.70469 2.25225 6.50322 2.05078 6.25469 2.05078H2.75469C2.50616 2.05078 2.30469 2.25225 2.30469 2.50078C2.30469 2.74931 2.50616 2.95078 2.75469 2.95078H3.95034V12.0508H2.75469C2.50616 12.0508 2.30469 12.2523 2.30469 12.5008C2.30469 12.7493 2.50616 12.9508 2.75469 12.9508H6.25469C6.50322 12.9508 6.70469 12.7493 6.70469 12.5008C6.70469 12.2523 6.50322 12.0508 6.25469 12.0508H5.05034V7.95062H7.08824C7.14151 7.63886 7.21802 7.33787 7.31541 7.05062Z"
        />
        <path
            className={levelClassName}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.441 5.84145C11.0744 5.77894 10.6333 5.90169 10.2171 6.31756C9.36711 7.16688 8.95691 8.53682 9.4887 10.956C9.54205 11.1987 9.38854 11.4387 9.1458 11.4921C8.90307 11.5455 8.66304 11.3919 8.60968 11.1492C8.04335 8.57287 8.42734 6.83361 9.58094 5.68092C10.1655 5.09678 10.889 4.83433 11.5923 4.95426C12.2948 5.07406 12.8917 5.56083 13.2545 6.30145C13.3638 6.52465 13.2715 6.7942 13.0483 6.90353C12.8251 7.01285 12.5556 6.92054 12.4462 6.69735C12.1867 6.16742 11.8083 5.90409 11.441 5.84145Z"
        />
        <path
            className={levelClassName}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10.221 6.31323C10.6399 5.89674 11.0602 5.78044 11.4102 5.84122C11.7676 5.90329 12.1536 6.16774 12.4557 6.71644C12.5756 6.93415 12.8492 7.01347 13.067 6.89361C13.2847 6.77374 13.364 6.50008 13.2441 6.28237C12.8467 5.56052 12.2574 5.07488 11.5642 4.9545C10.8649 4.83305 10.1614 5.10092 9.58094 5.68092L9.57185 5.6898C8.47458 6.8483 8.2505 8.43483 8.60259 11.1113C8.635 11.3577 8.86103 11.5312 9.10744 11.4987C9.35384 11.4663 9.52732 11.2403 9.4949 10.9939C9.14996 8.37182 9.42535 7.15657 10.221 6.31323Z"
        />
        <path
            className={levelClassName}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9.95551 9.27541C9.64589 9.61067 9.44992 10.0549 9.44992 10.501C9.44992 11.0918 9.63967 11.5249 9.9105 11.8073C10.1817 12.0901 10.5616 12.251 10.9999 12.251C11.4383 12.251 11.8181 12.0901 12.0893 11.8073C12.3602 11.5249 12.5499 11.0918 12.5499 10.501C12.5499 9.91019 12.3602 9.47734 12.0894 9.19514C11.8182 8.91249 11.4384 8.75175 10.9999 8.75175C10.6553 8.75175 10.2679 8.93716 9.95551 9.27541ZM9.29433 8.6648C9.73195 8.19095 10.3446 7.85175 10.9999 7.85175C11.666 7.85175 12.2862 8.10026 12.7388 8.57204C13.1919 9.04427 13.4499 9.71103 13.4499 10.501C13.4499 11.2909 13.192 11.9578 12.7389 12.4302C12.2863 12.9022 11.6662 13.151 10.9999 13.151C10.3337 13.151 9.71353 12.9022 9.26092 12.4302C8.80789 11.9578 8.54992 11.2909 8.54992 10.501C8.54992 9.79706 8.85395 9.14165 9.29433 8.6648Z"
        />
        <path
            className={levelClassName}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10.9999 8.75175C10.2287 8.75175 9.49992 9.53811 9.49992 10.501C9.49992 11.095 9.68618 11.5295 9.94976 11.8114C10.213 12.0929 10.5791 12.251 10.9999 12.251C11.4207 12.251 11.7869 12.0929 12.0501 11.8114C12.3137 11.5295 12.4999 11.095 12.4999 10.501C12.4999 9.90695 12.3137 9.4727 12.0502 9.19101C11.787 8.90973 11.4208 8.75175 10.9999 8.75175ZM8.59992 10.501C8.59992 9.16382 9.61721 7.85175 10.9999 7.85175C11.656 7.85175 12.2648 8.10302 12.7074 8.57617C13.1496 9.04891 13.3999 9.71427 13.3999 10.501C13.3999 11.2877 13.1497 11.9532 12.7075 12.4261C12.2649 12.8994 11.6561 13.151 10.9999 13.151C10.3437 13.151 9.73493 12.8994 9.29236 12.4261C8.85019 11.9532 8.59992 11.2877 8.59992 10.501Z"
        />
    </svg>
);

export default H6Icon;
