---
title: Appendix
description: Appendix for developer setup
---

## Set up environment variables manually (optional)

##### 1. In `apps/web/client`
Create a copy of the `.env.example` file under the `apps/web/client` directory and name it `.env`. Fill in the values with your own API keys.

It should look like this:

```properties
# ------------- Required Keys -------------

# Supabase - Enables our backend such as database and auth
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY="<Fill in from content after running supabase start>"

# Drizzle - Our ORM for accessing the database
SUPABASE_DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# Anthropic - Enables AI chat. Other providers may be supported in the future.
ANTHROPIC_API_KEY="<Your api key from https://console.anthropic.com/settings/keys>"

# Codesandbox - Used to host user apps. Other providers may be supported in the future. May be optional in the future.
CSB_API_KEY="<Your api key from https://codesandbox.io/t/api>"

# Fast apply model providers to reliably resolve code changes. Either will work since we will fall back.
# Option 1: MorphLLM
MORPH_API_KEY="<Your api key from https://morphllm.com/dashboard>"
# Option 2: Relace
RELACE_API_KEY="<Your api key from https://app.relace.ai/settings/api-keys>"

# ------------- Optional Keys -------------

# PostHog - Enables analytics for the app
NEXT_PUBLIC_POSTHOG_KEY="<Your PostHog API key from https://posthog.com/docs/libraries/next-js>"
NEXT_PUBLIC_POSTHOG_HOST="<Your PostHog region https://us.i.posthog.com>"

# Resend - Enables inviting other users to the project using emails
RESEND_API_KEY="<Your api key from https://resend.com/api-keys>"
```

##### 2. In `packages/db`

Create a copy of the `.env.example` file under the `packages/db` directory and name it `.env`. Fill in the values with the values from the output of the `bun backend:start` command.

```properties
SUPABASE_DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
SUPABASE_URL=http://localhost:54321
SUPABASE_SERVICE_ROLE_KEY=<Your service role key from Supabase>
```
