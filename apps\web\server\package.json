{"name": "@onlook/web-server", "module": "index.ts", "type": "module", "private": true, "scripts": {"dev": "bun run --watch src/index.ts", "build": "bun build src/index.ts --outdir dist --target bun", "start": "bun dist/index.js", "preview": "bun run build && bun run start"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@fastify/websocket": "^11.0.2", "@trpc/server": "^11.0.0", "fastify": "^5.2.2", "zod": "^3.24.2", "@onlook/rpc": "*"}}