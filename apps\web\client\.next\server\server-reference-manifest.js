self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00e1d0c967bce021073b78c14855b4307fdfa6ca6c\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/apps/web/client/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/project/[id]/page\": {\n          \"moduleId\": \"[project]/apps/web/client/.next-internal/server/app/project/[id]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/project/[id]/page\": \"action-browser\"\n      }\n    },\n    \"405218b8a67497de663f04930c2b88e7a261d33a4e\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/apps/web/client/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/project/[id]/page\": {\n          \"moduleId\": \"[project]/apps/web/client/.next-internal/server/app/project/[id]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/project/[id]/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"KVeR4tqK3NZdEsroTwj9ZMkh+iVQvy58fbzb3fepOPw=\"\n}"