# Web project

Structure

- Client - Next.js client that is served as app front-end
- Server - The control server that will be used to interact with the template
  app
- Template - The example app that will be editable
- Shared - All the shared packages for the web project
- Preload - The script that gets injected into Template app. This allows
  communnicating directly with the app DOM through iframe
