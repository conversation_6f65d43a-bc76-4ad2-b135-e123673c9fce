---
title: Figma to Onlook
description: Export Figma designs directly into Onlook
---

# Figma to Onlook

Export Figma designs directly into Onlook. No developers needed. This guide shows you how to structure your design, export it, and turn it into a working React application with Onlook.

![Figma to Onlook Banner](/images/figma-to-onlook-banner.png)
*Transform your Figma designs into working code with Onlook*

## Why This Integration Changes Everything

Designers no longer have to stop at mockups. With Onlook, you can:

- Transform Figma designs into clean, structured React components
- Make your designs interactive and functional
- Connect your UI to real data sources
- Deploy your work as a fully functional application

## Step-by-Step: Figma to React App

### 1. Structure Your Figma Design

For smooth importing, your Figma file needs to be well-organized. Here's how to prepare:

#### Use Auto-Layout

- Apply Auto-Layout to all parent containers
- Use proper padding, spacing, and resizing rules
- Set horizontal/vertical resizing for responsive layouts

#### Name Your Layers Clearly

- Use descriptive names like `Header`, `CTA Button`, `Nav Bar`—not `Frame 23`
- Avoid symbols and nested clutter
- Group similar components logically

![Layer Naming](/images/layer-naming.png)
*Properly named layers make for better code generation*

#### Build Reusable Components

- Use consistent styling (fonts, colors, etc.)
- Turn repeated elements into Figma components

#### Think in Design Systems

- Apply global styles
- Use a shared component library if available

### 2. Import into Onlook

1. Export your Figma design as SVG or PNG
2. Open Onlook and create a new project
3. Import your design assets
4. Use Onlook's tools to recreate the design as React components

### 3. Refine in Onlook

This is where the magic happens—turning static designs into real, usable apps.

1. Use AI prompts to improve your components
2. Preview in real-time
3. Add interactions and functionality
4. Connect to data sources if needed

### 4. Deploy Your App

When you're ready:
- Push your app live with one click
- Share it with your team or users
- Iterate based on feedback

## Resources

- [Figma Auto-Layout Tips](https://help.figma.com/hc/en-us/articles/360040451373-Create-dynamic-designs-with-Auto-layout)
- [Onlook for Designers](https://onlook.com/for-designers)
- [From Figma to Code](https://github.com/onlook-dev/onlook/wiki/From-Figma-to-Code)

## Got Feedback?

We're building this for *you*. Tag us, DM us, or share your experience—we're listening.
