import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import Script from 'next/script';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
    title: 'My New App',
    description: 'Generated by Onlook',
};

// Set to false for deployed template
const useLocalPreload = true;
const preloadSrc = useLocalPreload
    ? `http://localhost:8083/?${Math.random()}`
    : 'https://cdn.jsdelivr.net/gh/onlook-dev/onlook@main/apps/web/preload/dist/index.js';
const isProd = process.env.NODE_ENV === 'production';
export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
    return (
        <html lang="en" data-oid="si2j4vl">
            <head>
                {/* Don't use preload in production */}
                {!isProd && (
                    <Script
                        type="module"
                        src={preloadSrc}
                        crossOrigin="anonymous"
                        strategy="beforeInteractive"
                    />
                )}
            </head>
            <body className={inter.className} data-oid="mwz9mme">
                {children}
            </body>
        </html>
    );
}
