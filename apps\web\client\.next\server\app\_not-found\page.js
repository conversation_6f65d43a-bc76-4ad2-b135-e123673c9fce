const CHUNK_PUBLIC_PATH = "server/app/_not-found/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_a17f26a9._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__292a8cba._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_client_messages_417f089b._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_d7c9bc58._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__5067c3cd._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_client_src_app_not-found_tsx_510eaecb._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_b04db032._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@supabase_node-fetch_lib_index_b7fdf747.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__9387e6ac._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_90fc8477._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod_dist_esm_cde6975c._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_tr46_1a859af0._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ws_34fa8cd4._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@supabase_auth-js_dist_module_2a35e6ef._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_drizzle-orm_224d1747._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_a464ec56._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/client/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \"[project]/apps/web/client/src/app/login/actions.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { MODULE_0 => \"[project]/apps/web/client/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/web/client/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/apps/web/client/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { MODULE_0 => \"[project]/apps/web/client/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/web/client/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/apps/web/client/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
