module.exports = {

"[project]/node_modules/@ai-sdk/provider/dist/index.mjs [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/errors/ai-sdk-error.ts
__turbopack_context__.s({
    "AISDKError": (()=>AISDKError),
    "APICallError": (()=>APICallError),
    "EmptyResponseBodyError": (()=>EmptyResponseBodyError),
    "InvalidArgumentError": (()=>InvalidArgumentError),
    "InvalidPromptError": (()=>InvalidPromptError),
    "InvalidResponseDataError": (()=>InvalidResponseDataError),
    "JSONParseError": (()=>JSONParseError),
    "LoadAPIKeyError": (()=>LoadAPIKeyError),
    "LoadSettingError": (()=>LoadSettingError),
    "NoContentGeneratedError": (()=>NoContentGeneratedError),
    "NoSuchModelError": (()=>NoSuchModelError),
    "TooManyEmbeddingValuesForCallError": (()=>TooManyEmbeddingValuesForCallError),
    "TypeValidationError": (()=>TypeValidationError),
    "UnsupportedFunctionalityError": (()=>UnsupportedFunctionalityError),
    "getErrorMessage": (()=>getErrorMessage),
    "isJSONArray": (()=>isJSONArray),
    "isJSONObject": (()=>isJSONObject),
    "isJSONValue": (()=>isJSONValue)
});
var marker = "vercel.ai.error";
var symbol = Symbol.for(marker);
var _a;
var _AISDKError = class _AISDKError extends Error {
    /**
   * Creates an AI SDK Error.
   *
   * @param {Object} params - The parameters for creating the error.
   * @param {string} params.name - The name of the error.
   * @param {string} params.message - The error message.
   * @param {unknown} [params.cause] - The underlying cause of the error.
   */ constructor({ name: name14, message, cause }){
        super(message);
        this[_a] = true;
        this.name = name14;
        this.cause = cause;
    }
    /**
   * Checks if the given error is an AI SDK Error.
   * @param {unknown} error - The error to check.
   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.
   */ static isInstance(error) {
        return _AISDKError.hasMarker(error, marker);
    }
    static hasMarker(error, marker15) {
        const markerSymbol = Symbol.for(marker15);
        return error != null && typeof error === "object" && markerSymbol in error && typeof error[markerSymbol] === "boolean" && error[markerSymbol] === true;
    }
};
_a = symbol;
var AISDKError = _AISDKError;
// src/errors/api-call-error.ts
var name = "AI_APICallError";
var marker2 = `vercel.ai.error.${name}`;
var symbol2 = Symbol.for(marker2);
var _a2;
var APICallError = class extends AISDKError {
    constructor({ message, url, requestBodyValues, statusCode, responseHeaders, responseBody, cause, isRetryable = statusCode != null && (statusCode === 408 || // request timeout
    statusCode === 409 || // conflict
    statusCode === 429 || // too many requests
    statusCode >= 500), // server error
    data }){
        super({
            name,
            message,
            cause
        });
        this[_a2] = true;
        this.url = url;
        this.requestBodyValues = requestBodyValues;
        this.statusCode = statusCode;
        this.responseHeaders = responseHeaders;
        this.responseBody = responseBody;
        this.isRetryable = isRetryable;
        this.data = data;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker2);
    }
};
_a2 = symbol2;
// src/errors/empty-response-body-error.ts
var name2 = "AI_EmptyResponseBodyError";
var marker3 = `vercel.ai.error.${name2}`;
var symbol3 = Symbol.for(marker3);
var _a3;
var EmptyResponseBodyError = class extends AISDKError {
    // used in isInstance
    constructor({ message = "Empty response body" } = {}){
        super({
            name: name2,
            message
        });
        this[_a3] = true;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker3);
    }
};
_a3 = symbol3;
// src/errors/get-error-message.ts
function getErrorMessage(error) {
    if (error == null) {
        return "unknown error";
    }
    if (typeof error === "string") {
        return error;
    }
    if (error instanceof Error) {
        return error.message;
    }
    return JSON.stringify(error);
}
// src/errors/invalid-argument-error.ts
var name3 = "AI_InvalidArgumentError";
var marker4 = `vercel.ai.error.${name3}`;
var symbol4 = Symbol.for(marker4);
var _a4;
var InvalidArgumentError = class extends AISDKError {
    constructor({ message, cause, argument }){
        super({
            name: name3,
            message,
            cause
        });
        this[_a4] = true;
        this.argument = argument;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker4);
    }
};
_a4 = symbol4;
// src/errors/invalid-prompt-error.ts
var name4 = "AI_InvalidPromptError";
var marker5 = `vercel.ai.error.${name4}`;
var symbol5 = Symbol.for(marker5);
var _a5;
var InvalidPromptError = class extends AISDKError {
    constructor({ prompt, message, cause }){
        super({
            name: name4,
            message: `Invalid prompt: ${message}`,
            cause
        });
        this[_a5] = true;
        this.prompt = prompt;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker5);
    }
};
_a5 = symbol5;
// src/errors/invalid-response-data-error.ts
var name5 = "AI_InvalidResponseDataError";
var marker6 = `vercel.ai.error.${name5}`;
var symbol6 = Symbol.for(marker6);
var _a6;
var InvalidResponseDataError = class extends AISDKError {
    constructor({ data, message = `Invalid response data: ${JSON.stringify(data)}.` }){
        super({
            name: name5,
            message
        });
        this[_a6] = true;
        this.data = data;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker6);
    }
};
_a6 = symbol6;
// src/errors/json-parse-error.ts
var name6 = "AI_JSONParseError";
var marker7 = `vercel.ai.error.${name6}`;
var symbol7 = Symbol.for(marker7);
var _a7;
var JSONParseError = class extends AISDKError {
    constructor({ text, cause }){
        super({
            name: name6,
            message: `JSON parsing failed: Text: ${text}.
Error message: ${getErrorMessage(cause)}`,
            cause
        });
        this[_a7] = true;
        this.text = text;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker7);
    }
};
_a7 = symbol7;
// src/errors/load-api-key-error.ts
var name7 = "AI_LoadAPIKeyError";
var marker8 = `vercel.ai.error.${name7}`;
var symbol8 = Symbol.for(marker8);
var _a8;
var LoadAPIKeyError = class extends AISDKError {
    // used in isInstance
    constructor({ message }){
        super({
            name: name7,
            message
        });
        this[_a8] = true;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker8);
    }
};
_a8 = symbol8;
// src/errors/load-setting-error.ts
var name8 = "AI_LoadSettingError";
var marker9 = `vercel.ai.error.${name8}`;
var symbol9 = Symbol.for(marker9);
var _a9;
var LoadSettingError = class extends AISDKError {
    // used in isInstance
    constructor({ message }){
        super({
            name: name8,
            message
        });
        this[_a9] = true;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker9);
    }
};
_a9 = symbol9;
// src/errors/no-content-generated-error.ts
var name9 = "AI_NoContentGeneratedError";
var marker10 = `vercel.ai.error.${name9}`;
var symbol10 = Symbol.for(marker10);
var _a10;
var NoContentGeneratedError = class extends AISDKError {
    // used in isInstance
    constructor({ message = "No content generated." } = {}){
        super({
            name: name9,
            message
        });
        this[_a10] = true;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker10);
    }
};
_a10 = symbol10;
// src/errors/no-such-model-error.ts
var name10 = "AI_NoSuchModelError";
var marker11 = `vercel.ai.error.${name10}`;
var symbol11 = Symbol.for(marker11);
var _a11;
var NoSuchModelError = class extends AISDKError {
    constructor({ errorName = name10, modelId, modelType, message = `No such ${modelType}: ${modelId}` }){
        super({
            name: errorName,
            message
        });
        this[_a11] = true;
        this.modelId = modelId;
        this.modelType = modelType;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker11);
    }
};
_a11 = symbol11;
// src/errors/too-many-embedding-values-for-call-error.ts
var name11 = "AI_TooManyEmbeddingValuesForCallError";
var marker12 = `vercel.ai.error.${name11}`;
var symbol12 = Symbol.for(marker12);
var _a12;
var TooManyEmbeddingValuesForCallError = class extends AISDKError {
    constructor(options){
        super({
            name: name11,
            message: `Too many values for a single embedding call. The ${options.provider} model "${options.modelId}" can only embed up to ${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`
        });
        this[_a12] = true;
        this.provider = options.provider;
        this.modelId = options.modelId;
        this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;
        this.values = options.values;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker12);
    }
};
_a12 = symbol12;
// src/errors/type-validation-error.ts
var name12 = "AI_TypeValidationError";
var marker13 = `vercel.ai.error.${name12}`;
var symbol13 = Symbol.for(marker13);
var _a13;
var _TypeValidationError = class _TypeValidationError extends AISDKError {
    constructor({ value, cause }){
        super({
            name: name12,
            message: `Type validation failed: Value: ${JSON.stringify(value)}.
Error message: ${getErrorMessage(cause)}`,
            cause
        });
        this[_a13] = true;
        this.value = value;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker13);
    }
    /**
   * Wraps an error into a TypeValidationError.
   * If the cause is already a TypeValidationError with the same value, it returns the cause.
   * Otherwise, it creates a new TypeValidationError.
   *
   * @param {Object} params - The parameters for wrapping the error.
   * @param {unknown} params.value - The value that failed validation.
   * @param {unknown} params.cause - The original error or cause of the validation failure.
   * @returns {TypeValidationError} A TypeValidationError instance.
   */ static wrap({ value, cause }) {
        return _TypeValidationError.isInstance(cause) && cause.value === value ? cause : new _TypeValidationError({
            value,
            cause
        });
    }
};
_a13 = symbol13;
var TypeValidationError = _TypeValidationError;
// src/errors/unsupported-functionality-error.ts
var name13 = "AI_UnsupportedFunctionalityError";
var marker14 = `vercel.ai.error.${name13}`;
var symbol14 = Symbol.for(marker14);
var _a14;
var UnsupportedFunctionalityError = class extends AISDKError {
    constructor({ functionality, message = `'${functionality}' functionality not supported.` }){
        super({
            name: name13,
            message
        });
        this[_a14] = true;
        this.functionality = functionality;
    }
    static isInstance(error) {
        return AISDKError.hasMarker(error, marker14);
    }
};
_a14 = symbol14;
// src/json-value/is-json.ts
function isJSONValue(value) {
    if (value === null || typeof value === "string" || typeof value === "number" || typeof value === "boolean") {
        return true;
    }
    if (Array.isArray(value)) {
        return value.every(isJSONValue);
    }
    if (typeof value === "object") {
        return Object.entries(value).every(([key, val])=>typeof key === "string" && isJSONValue(val));
    }
    return false;
}
function isJSONArray(value) {
    return Array.isArray(value) && value.every(isJSONValue);
}
function isJSONObject(value) {
    return value != null && typeof value === "object" && Object.entries(value).every(([key, val])=>typeof key === "string" && isJSONValue(val));
}
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/@ai-sdk/provider-utils/node_modules/nanoid/non-secure/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "customAlphabet": (()=>customAlphabet),
    "nanoid": (()=>nanoid)
});
let urlAlphabet = 'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict';
let customAlphabet = (alphabet, defaultSize = 21)=>{
    return (size = defaultSize)=>{
        let id = '';
        let i = size | 0;
        while(i--){
            id += alphabet[Math.random() * alphabet.length | 0];
        }
        return id;
    };
};
let nanoid = (size = 21)=>{
    let id = '';
    let i = size | 0;
    while(i--){
        id += urlAlphabet[Math.random() * 64 | 0];
    }
    return id;
};
;
}}),
"[project]/node_modules/@ai-sdk/provider-utils/node_modules/secure-json-parse/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const hasBuffer = typeof Buffer !== 'undefined';
const suspectProtoRx = /"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/;
const suspectConstructorRx = /"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;
function _parse(text, reviver, options) {
    // Normalize arguments
    if (options == null) {
        if (reviver !== null && typeof reviver === 'object') {
            options = reviver;
            reviver = undefined;
        }
    }
    if (hasBuffer && Buffer.isBuffer(text)) {
        text = text.toString();
    }
    // BOM checker
    if (text && text.charCodeAt(0) === 0xFEFF) {
        text = text.slice(1);
    }
    // Parse normally, allowing exceptions
    const obj = JSON.parse(text, reviver);
    // Ignore null and non-objects
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    const protoAction = options && options.protoAction || 'error';
    const constructorAction = options && options.constructorAction || 'error';
    // options: 'error' (default) / 'remove' / 'ignore'
    if (protoAction === 'ignore' && constructorAction === 'ignore') {
        return obj;
    }
    if (protoAction !== 'ignore' && constructorAction !== 'ignore') {
        if (suspectProtoRx.test(text) === false && suspectConstructorRx.test(text) === false) {
            return obj;
        }
    } else if (protoAction !== 'ignore' && constructorAction === 'ignore') {
        if (suspectProtoRx.test(text) === false) {
            return obj;
        }
    } else {
        if (suspectConstructorRx.test(text) === false) {
            return obj;
        }
    }
    // Scan result for proto keys
    return filter(obj, {
        protoAction,
        constructorAction,
        safe: options && options.safe
    });
}
function filter(obj, { protoAction = 'error', constructorAction = 'error', safe } = {}) {
    let next = [
        obj
    ];
    while(next.length){
        const nodes = next;
        next = [];
        for (const node of nodes){
            if (protoAction !== 'ignore' && Object.prototype.hasOwnProperty.call(node, '__proto__')) {
                if (safe === true) {
                    return null;
                } else if (protoAction === 'error') {
                    throw new SyntaxError('Object contains forbidden prototype property');
                }
                delete node.__proto__ // eslint-disable-line no-proto
                ;
            }
            if (constructorAction !== 'ignore' && Object.prototype.hasOwnProperty.call(node, 'constructor') && Object.prototype.hasOwnProperty.call(node.constructor, 'prototype')) {
                if (safe === true) {
                    return null;
                } else if (constructorAction === 'error') {
                    throw new SyntaxError('Object contains forbidden prototype property');
                }
                delete node.constructor;
            }
            for(const key in node){
                const value = node[key];
                if (value && typeof value === 'object') {
                    next.push(value);
                }
            }
        }
    }
    return obj;
}
function parse(text, reviver, options) {
    const stackTraceLimit = Error.stackTraceLimit;
    Error.stackTraceLimit = 0;
    try {
        return _parse(text, reviver, options);
    } finally{
        Error.stackTraceLimit = stackTraceLimit;
    }
}
function safeParse(text, reviver) {
    const stackTraceLimit = Error.stackTraceLimit;
    Error.stackTraceLimit = 0;
    try {
        return _parse(text, reviver, {
            safe: true
        });
    } catch (_e) {
        return null;
    } finally{
        Error.stackTraceLimit = stackTraceLimit;
    }
}
module.exports = parse;
module.exports.default = parse;
module.exports.parse = parse;
module.exports.safeParse = safeParse;
module.exports.scan = filter;
}}),
"[project]/node_modules/@ai-sdk/provider-utils/dist/index.mjs [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/combine-headers.ts
__turbopack_context__.s({
    "asValidator": (()=>asValidator),
    "combineHeaders": (()=>combineHeaders),
    "convertAsyncIteratorToReadableStream": (()=>convertAsyncIteratorToReadableStream),
    "convertBase64ToUint8Array": (()=>convertBase64ToUint8Array),
    "convertUint8ArrayToBase64": (()=>convertUint8ArrayToBase64),
    "createBinaryResponseHandler": (()=>createBinaryResponseHandler),
    "createEventSourceParserStream": (()=>createEventSourceParserStream),
    "createEventSourceResponseHandler": (()=>createEventSourceResponseHandler),
    "createIdGenerator": (()=>createIdGenerator),
    "createJsonErrorResponseHandler": (()=>createJsonErrorResponseHandler),
    "createJsonResponseHandler": (()=>createJsonResponseHandler),
    "createJsonStreamResponseHandler": (()=>createJsonStreamResponseHandler),
    "createStatusCodeErrorResponseHandler": (()=>createStatusCodeErrorResponseHandler),
    "delay": (()=>delay),
    "extractResponseHeaders": (()=>extractResponseHeaders),
    "generateId": (()=>generateId),
    "getErrorMessage": (()=>getErrorMessage),
    "getFromApi": (()=>getFromApi),
    "isAbortError": (()=>isAbortError),
    "isParsableJson": (()=>isParsableJson),
    "isValidator": (()=>isValidator),
    "loadApiKey": (()=>loadApiKey),
    "loadOptionalSetting": (()=>loadOptionalSetting),
    "loadSetting": (()=>loadSetting),
    "parseJSON": (()=>parseJSON),
    "parseProviderOptions": (()=>parseProviderOptions),
    "postFormDataToApi": (()=>postFormDataToApi),
    "postJsonToApi": (()=>postJsonToApi),
    "postToApi": (()=>postToApi),
    "removeUndefinedEntries": (()=>removeUndefinedEntries),
    "resolve": (()=>resolve),
    "safeParseJSON": (()=>safeParseJSON),
    "safeValidateTypes": (()=>safeValidateTypes),
    "validateTypes": (()=>validateTypes),
    "validator": (()=>validator),
    "validatorSymbol": (()=>validatorSymbol),
    "withoutTrailingSlash": (()=>withoutTrailingSlash),
    "zodValidator": (()=>zodValidator)
});
// src/generate-id.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ai-sdk/provider/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$node_modules$2f$nanoid$2f$non$2d$secure$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ai-sdk/provider-utils/node_modules/nanoid/non-secure/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$node_modules$2f$secure$2d$json$2d$parse$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ai-sdk/provider-utils/node_modules/secure-json-parse/index.js [app-route] (ecmascript)");
function combineHeaders(...headers) {
    return headers.reduce((combinedHeaders, currentHeaders)=>({
            ...combinedHeaders,
            ...currentHeaders != null ? currentHeaders : {}
        }), {});
}
// src/convert-async-iterator-to-readable-stream.ts
function convertAsyncIteratorToReadableStream(iterator) {
    return new ReadableStream({
        /**
     * Called when the consumer wants to pull more data from the stream.
     *
     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.
     * @returns {Promise<void>}
     */ async pull (controller) {
            try {
                const { value, done } = await iterator.next();
                if (done) {
                    controller.close();
                } else {
                    controller.enqueue(value);
                }
            } catch (error) {
                controller.error(error);
            }
        },
        /**
     * Called when the consumer cancels the stream.
     */ cancel () {}
    });
}
// src/delay.ts
async function delay(delayInMs) {
    return delayInMs == null ? Promise.resolve() : new Promise((resolve2)=>setTimeout(resolve2, delayInMs));
}
// src/event-source-parser-stream.ts
function createEventSourceParserStream() {
    let buffer = "";
    let event = void 0;
    let data = [];
    let lastEventId = void 0;
    let retry = void 0;
    function parseLine(line, controller) {
        if (line === "") {
            dispatchEvent(controller);
            return;
        }
        if (line.startsWith(":")) {
            return;
        }
        const colonIndex = line.indexOf(":");
        if (colonIndex === -1) {
            handleField(line, "");
            return;
        }
        const field = line.slice(0, colonIndex);
        const valueStart = colonIndex + 1;
        const value = valueStart < line.length && line[valueStart] === " " ? line.slice(valueStart + 1) : line.slice(valueStart);
        handleField(field, value);
    }
    function dispatchEvent(controller) {
        if (data.length > 0) {
            controller.enqueue({
                event,
                data: data.join("\n"),
                id: lastEventId,
                retry
            });
            data = [];
            event = void 0;
            retry = void 0;
        }
    }
    function handleField(field, value) {
        switch(field){
            case "event":
                event = value;
                break;
            case "data":
                data.push(value);
                break;
            case "id":
                lastEventId = value;
                break;
            case "retry":
                const parsedRetry = parseInt(value, 10);
                if (!isNaN(parsedRetry)) {
                    retry = parsedRetry;
                }
                break;
        }
    }
    return new TransformStream({
        transform (chunk, controller) {
            const { lines, incompleteLine } = splitLines(buffer, chunk);
            buffer = incompleteLine;
            for(let i = 0; i < lines.length; i++){
                parseLine(lines[i], controller);
            }
        },
        flush (controller) {
            parseLine(buffer, controller);
            dispatchEvent(controller);
        }
    });
}
function splitLines(buffer, chunk) {
    const lines = [];
    let currentLine = buffer;
    for(let i = 0; i < chunk.length;){
        const char = chunk[i++];
        if (char === "\n") {
            lines.push(currentLine);
            currentLine = "";
        } else if (char === "\r") {
            lines.push(currentLine);
            currentLine = "";
            if (chunk[i] === "\n") {
                i++;
            }
        } else {
            currentLine += char;
        }
    }
    return {
        lines,
        incompleteLine: currentLine
    };
}
// src/extract-response-headers.ts
function extractResponseHeaders(response) {
    const headers = {};
    response.headers.forEach((value, key)=>{
        headers[key] = value;
    });
    return headers;
}
;
;
var createIdGenerator = ({ prefix, size: defaultSize = 16, alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz", separator = "-" } = {})=>{
    const generator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$node_modules$2f$nanoid$2f$non$2d$secure$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customAlphabet"])(alphabet, defaultSize);
    if (prefix == null) {
        return generator;
    }
    if (alphabet.includes(separator)) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InvalidArgumentError"]({
            argument: "separator",
            message: `The separator "${separator}" must not be part of the alphabet "${alphabet}".`
        });
    }
    return (size)=>`${prefix}${separator}${generator(size)}`;
};
var generateId = createIdGenerator();
// src/get-error-message.ts
function getErrorMessage(error) {
    if (error == null) {
        return "unknown error";
    }
    if (typeof error === "string") {
        return error;
    }
    if (error instanceof Error) {
        return error.message;
    }
    return JSON.stringify(error);
}
;
// src/remove-undefined-entries.ts
function removeUndefinedEntries(record) {
    return Object.fromEntries(Object.entries(record).filter(([_key, value])=>value != null));
}
// src/is-abort-error.ts
function isAbortError(error) {
    return error instanceof Error && (error.name === "AbortError" || error.name === "TimeoutError");
}
// src/get-from-api.ts
var getOriginalFetch = ()=>globalThis.fetch;
var getFromApi = async ({ url, headers = {}, successfulResponseHandler, failedResponseHandler, abortSignal, fetch = getOriginalFetch() })=>{
    try {
        const response = await fetch(url, {
            method: "GET",
            headers: removeUndefinedEntries(headers),
            signal: abortSignal
        });
        const responseHeaders = extractResponseHeaders(response);
        if (!response.ok) {
            let errorInformation;
            try {
                errorInformation = await failedResponseHandler({
                    response,
                    url,
                    requestBodyValues: {}
                });
            } catch (error) {
                if (isAbortError(error) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"].isInstance(error)) {
                    throw error;
                }
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                    message: "Failed to process error response",
                    cause: error,
                    statusCode: response.status,
                    url,
                    responseHeaders,
                    requestBodyValues: {}
                });
            }
            throw errorInformation.value;
        }
        try {
            return await successfulResponseHandler({
                response,
                url,
                requestBodyValues: {}
            });
        } catch (error) {
            if (error instanceof Error) {
                if (isAbortError(error) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"].isInstance(error)) {
                    throw error;
                }
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                message: "Failed to process successful response",
                cause: error,
                statusCode: response.status,
                url,
                responseHeaders,
                requestBodyValues: {}
            });
        }
    } catch (error) {
        if (isAbortError(error)) {
            throw error;
        }
        if (error instanceof TypeError && error.message === "fetch failed") {
            const cause = error.cause;
            if (cause != null) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                    message: `Cannot connect to API: ${cause.message}`,
                    cause,
                    url,
                    isRetryable: true,
                    requestBodyValues: {}
                });
            }
        }
        throw error;
    }
};
;
function loadApiKey({ apiKey, environmentVariableName, apiKeyParameterName = "apiKey", description }) {
    if (typeof apiKey === "string") {
        return apiKey;
    }
    if (apiKey != null) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LoadAPIKeyError"]({
            message: `${description} API key must be a string.`
        });
    }
    if (typeof process === "undefined") {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LoadAPIKeyError"]({
            message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`
        });
    }
    apiKey = process.env[environmentVariableName];
    if (apiKey == null) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LoadAPIKeyError"]({
            message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`
        });
    }
    if (typeof apiKey !== "string") {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LoadAPIKeyError"]({
            message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`
        });
    }
    return apiKey;
}
// src/load-optional-setting.ts
function loadOptionalSetting({ settingValue, environmentVariableName }) {
    if (typeof settingValue === "string") {
        return settingValue;
    }
    if (settingValue != null || typeof process === "undefined") {
        return void 0;
    }
    settingValue = process.env[environmentVariableName];
    if (settingValue == null || typeof settingValue !== "string") {
        return void 0;
    }
    return settingValue;
}
;
function loadSetting({ settingValue, environmentVariableName, settingName, description }) {
    if (typeof settingValue === "string") {
        return settingValue;
    }
    if (settingValue != null) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LoadSettingError"]({
            message: `${description} setting must be a string.`
        });
    }
    if (typeof process === "undefined") {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LoadSettingError"]({
            message: `${description} setting is missing. Pass it using the '${settingName}' parameter. Environment variables is not supported in this environment.`
        });
    }
    settingValue = process.env[environmentVariableName];
    if (settingValue == null) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LoadSettingError"]({
            message: `${description} setting is missing. Pass it using the '${settingName}' parameter or the ${environmentVariableName} environment variable.`
        });
    }
    if (typeof settingValue !== "string") {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LoadSettingError"]({
            message: `${description} setting must be a string. The value of the ${environmentVariableName} environment variable is not a string.`
        });
    }
    return settingValue;
}
;
;
;
// src/validator.ts
var validatorSymbol = Symbol.for("vercel.ai.validator");
function validator(validate) {
    return {
        [validatorSymbol]: true,
        validate
    };
}
function isValidator(value) {
    return typeof value === "object" && value !== null && validatorSymbol in value && value[validatorSymbol] === true && "validate" in value;
}
function asValidator(value) {
    return isValidator(value) ? value : zodValidator(value);
}
function zodValidator(zodSchema) {
    return validator((value)=>{
        const result = zodSchema.safeParse(value);
        return result.success ? {
            success: true,
            value: result.data
        } : {
            success: false,
            error: result.error
        };
    });
}
// src/validate-types.ts
function validateTypes({ value, schema: inputSchema }) {
    const result = safeValidateTypes({
        value,
        schema: inputSchema
    });
    if (!result.success) {
        throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TypeValidationError"].wrap({
            value,
            cause: result.error
        });
    }
    return result.value;
}
function safeValidateTypes({ value, schema }) {
    const validator2 = asValidator(schema);
    try {
        if (validator2.validate == null) {
            return {
                success: true,
                value
            };
        }
        const result = validator2.validate(value);
        if (result.success) {
            return result;
        }
        return {
            success: false,
            error: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TypeValidationError"].wrap({
                value,
                cause: result.error
            })
        };
    } catch (error) {
        return {
            success: false,
            error: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TypeValidationError"].wrap({
                value,
                cause: error
            })
        };
    }
}
// src/parse-json.ts
function parseJSON({ text, schema }) {
    try {
        const value = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$node_modules$2f$secure$2d$json$2d$parse$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].parse(text);
        if (schema == null) {
            return value;
        }
        return validateTypes({
            value,
            schema
        });
    } catch (error) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["JSONParseError"].isInstance(error) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TypeValidationError"].isInstance(error)) {
            throw error;
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["JSONParseError"]({
            text,
            cause: error
        });
    }
}
function safeParseJSON({ text, schema }) {
    try {
        const value = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$node_modules$2f$secure$2d$json$2d$parse$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].parse(text);
        if (schema == null) {
            return {
                success: true,
                value,
                rawValue: value
            };
        }
        const validationResult = safeValidateTypes({
            value,
            schema
        });
        return validationResult.success ? {
            ...validationResult,
            rawValue: value
        } : validationResult;
    } catch (error) {
        return {
            success: false,
            error: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["JSONParseError"].isInstance(error) ? error : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["JSONParseError"]({
                text,
                cause: error
            })
        };
    }
}
function isParsableJson(input) {
    try {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$node_modules$2f$secure$2d$json$2d$parse$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].parse(input);
        return true;
    } catch (e) {
        return false;
    }
}
;
function parseProviderOptions({ provider, providerOptions, schema }) {
    if ((providerOptions == null ? void 0 : providerOptions[provider]) == null) {
        return void 0;
    }
    const parsedProviderOptions = safeValidateTypes({
        value: providerOptions[provider],
        schema
    });
    if (!parsedProviderOptions.success) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InvalidArgumentError"]({
            argument: "providerOptions",
            message: `invalid ${provider} provider options`,
            cause: parsedProviderOptions.error
        });
    }
    return parsedProviderOptions.value;
}
;
var getOriginalFetch2 = ()=>globalThis.fetch;
var postJsonToApi = async ({ url, headers, body, failedResponseHandler, successfulResponseHandler, abortSignal, fetch })=>postToApi({
        url,
        headers: {
            "Content-Type": "application/json",
            ...headers
        },
        body: {
            content: JSON.stringify(body),
            values: body
        },
        failedResponseHandler,
        successfulResponseHandler,
        abortSignal,
        fetch
    });
var postFormDataToApi = async ({ url, headers, formData, failedResponseHandler, successfulResponseHandler, abortSignal, fetch })=>postToApi({
        url,
        headers,
        body: {
            content: formData,
            values: Object.fromEntries(formData.entries())
        },
        failedResponseHandler,
        successfulResponseHandler,
        abortSignal,
        fetch
    });
var postToApi = async ({ url, headers = {}, body, successfulResponseHandler, failedResponseHandler, abortSignal, fetch = getOriginalFetch2() })=>{
    try {
        const response = await fetch(url, {
            method: "POST",
            headers: removeUndefinedEntries(headers),
            body: body.content,
            signal: abortSignal
        });
        const responseHeaders = extractResponseHeaders(response);
        if (!response.ok) {
            let errorInformation;
            try {
                errorInformation = await failedResponseHandler({
                    response,
                    url,
                    requestBodyValues: body.values
                });
            } catch (error) {
                if (isAbortError(error) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"].isInstance(error)) {
                    throw error;
                }
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                    message: "Failed to process error response",
                    cause: error,
                    statusCode: response.status,
                    url,
                    responseHeaders,
                    requestBodyValues: body.values
                });
            }
            throw errorInformation.value;
        }
        try {
            return await successfulResponseHandler({
                response,
                url,
                requestBodyValues: body.values
            });
        } catch (error) {
            if (error instanceof Error) {
                if (isAbortError(error) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"].isInstance(error)) {
                    throw error;
                }
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                message: "Failed to process successful response",
                cause: error,
                statusCode: response.status,
                url,
                responseHeaders,
                requestBodyValues: body.values
            });
        }
    } catch (error) {
        if (isAbortError(error)) {
            throw error;
        }
        if (error instanceof TypeError && error.message === "fetch failed") {
            const cause = error.cause;
            if (cause != null) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                    message: `Cannot connect to API: ${cause.message}`,
                    cause,
                    url,
                    requestBodyValues: body.values,
                    isRetryable: true
                });
            }
        }
        throw error;
    }
};
// src/resolve.ts
async function resolve(value) {
    if (typeof value === "function") {
        value = value();
    }
    return Promise.resolve(value);
}
;
var createJsonErrorResponseHandler = ({ errorSchema, errorToMessage, isRetryable })=>async ({ response, url, requestBodyValues })=>{
        const responseBody = await response.text();
        const responseHeaders = extractResponseHeaders(response);
        if (responseBody.trim() === "") {
            return {
                responseHeaders,
                value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                    message: response.statusText,
                    url,
                    requestBodyValues,
                    statusCode: response.status,
                    responseHeaders,
                    responseBody,
                    isRetryable: isRetryable == null ? void 0 : isRetryable(response)
                })
            };
        }
        try {
            const parsedError = parseJSON({
                text: responseBody,
                schema: errorSchema
            });
            return {
                responseHeaders,
                value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                    message: errorToMessage(parsedError),
                    url,
                    requestBodyValues,
                    statusCode: response.status,
                    responseHeaders,
                    responseBody,
                    data: parsedError,
                    isRetryable: isRetryable == null ? void 0 : isRetryable(response, parsedError)
                })
            };
        } catch (parseError) {
            return {
                responseHeaders,
                value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                    message: response.statusText,
                    url,
                    requestBodyValues,
                    statusCode: response.status,
                    responseHeaders,
                    responseBody,
                    isRetryable: isRetryable == null ? void 0 : isRetryable(response)
                })
            };
        }
    };
var createEventSourceResponseHandler = (chunkSchema)=>async ({ response })=>{
        const responseHeaders = extractResponseHeaders(response);
        if (response.body == null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EmptyResponseBodyError"]({});
        }
        return {
            responseHeaders,
            value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(createEventSourceParserStream()).pipeThrough(new TransformStream({
                transform ({ data }, controller) {
                    if (data === "[DONE]") {
                        return;
                    }
                    controller.enqueue(safeParseJSON({
                        text: data,
                        schema: chunkSchema
                    }));
                }
            }))
        };
    };
var createJsonStreamResponseHandler = (chunkSchema)=>async ({ response })=>{
        const responseHeaders = extractResponseHeaders(response);
        if (response.body == null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EmptyResponseBodyError"]({});
        }
        let buffer = "";
        return {
            responseHeaders,
            value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(new TransformStream({
                transform (chunkText, controller) {
                    if (chunkText.endsWith("\n")) {
                        controller.enqueue(safeParseJSON({
                            text: buffer + chunkText,
                            schema: chunkSchema
                        }));
                        buffer = "";
                    } else {
                        buffer += chunkText;
                    }
                }
            }))
        };
    };
var createJsonResponseHandler = (responseSchema)=>async ({ response, url, requestBodyValues })=>{
        const responseBody = await response.text();
        const parsedResult = safeParseJSON({
            text: responseBody,
            schema: responseSchema
        });
        const responseHeaders = extractResponseHeaders(response);
        if (!parsedResult.success) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                message: "Invalid JSON response",
                cause: parsedResult.error,
                statusCode: response.status,
                responseHeaders,
                responseBody,
                url,
                requestBodyValues
            });
        }
        return {
            responseHeaders,
            value: parsedResult.value,
            rawValue: parsedResult.rawValue
        };
    };
var createBinaryResponseHandler = ()=>async ({ response, url, requestBodyValues })=>{
        const responseHeaders = extractResponseHeaders(response);
        if (!response.body) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                message: "Response body is empty",
                url,
                requestBodyValues,
                statusCode: response.status,
                responseHeaders,
                responseBody: void 0
            });
        }
        try {
            const buffer = await response.arrayBuffer();
            return {
                responseHeaders,
                value: new Uint8Array(buffer)
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                message: "Failed to read response as array buffer",
                url,
                requestBodyValues,
                statusCode: response.status,
                responseHeaders,
                responseBody: void 0,
                cause: error
            });
        }
    };
var createStatusCodeErrorResponseHandler = ()=>async ({ response, url, requestBodyValues })=>{
        const responseHeaders = extractResponseHeaders(response);
        const responseBody = await response.text();
        return {
            responseHeaders,
            value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APICallError"]({
                message: response.statusText,
                url,
                requestBodyValues,
                statusCode: response.status,
                responseHeaders,
                responseBody
            })
        };
    };
// src/uint8-utils.ts
var { btoa, atob } = globalThis;
function convertBase64ToUint8Array(base64String) {
    const base64Url = base64String.replace(/-/g, "+").replace(/_/g, "/");
    const latin1string = atob(base64Url);
    return Uint8Array.from(latin1string, (byte)=>byte.codePointAt(0));
}
function convertUint8ArrayToBase64(array) {
    let latin1string = "";
    for(let i = 0; i < array.length; i++){
        latin1string += String.fromCodePoint(array[i]);
    }
    return btoa(latin1string);
}
// src/without-trailing-slash.ts
function withoutTrailingSlash(url) {
    return url == null ? void 0 : url.replace(/\/$/, "");
}
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/@ai-sdk/anthropic/dist/index.mjs [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/anthropic-provider.ts
__turbopack_context__.s({
    "anthropic": (()=>anthropic),
    "createAnthropic": (()=>createAnthropic)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ai-sdk/provider/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ai-sdk/provider-utils/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
;
;
;
;
;
;
;
var anthropicErrorDataSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("error"),
    error: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        message: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    })
});
var anthropicFailedResponseHandler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createJsonErrorResponseHandler"])({
    errorSchema: anthropicErrorDataSchema,
    errorToMessage: (data)=>data.error.message
});
;
function prepareTools(mode) {
    var _a;
    const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;
    const toolWarnings = [];
    const betas = /* @__PURE__ */ new Set();
    if (tools == null) {
        return {
            tools: void 0,
            tool_choice: void 0,
            toolWarnings,
            betas
        };
    }
    const anthropicTools2 = [];
    for (const tool of tools){
        switch(tool.type){
            case "function":
                anthropicTools2.push({
                    name: tool.name,
                    description: tool.description,
                    input_schema: tool.parameters
                });
                break;
            case "provider-defined":
                switch(tool.id){
                    case "anthropic.computer_20250124":
                        betas.add("computer-use-2025-01-24");
                        anthropicTools2.push({
                            name: tool.name,
                            type: "computer_20250124",
                            display_width_px: tool.args.displayWidthPx,
                            display_height_px: tool.args.displayHeightPx,
                            display_number: tool.args.displayNumber
                        });
                        break;
                    case "anthropic.computer_20241022":
                        betas.add("computer-use-2024-10-22");
                        anthropicTools2.push({
                            name: tool.name,
                            type: "computer_20241022",
                            display_width_px: tool.args.displayWidthPx,
                            display_height_px: tool.args.displayHeightPx,
                            display_number: tool.args.displayNumber
                        });
                        break;
                    case "anthropic.text_editor_20250124":
                        betas.add("computer-use-2025-01-24");
                        anthropicTools2.push({
                            name: tool.name,
                            type: "text_editor_20250124"
                        });
                        break;
                    case "anthropic.text_editor_20241022":
                        betas.add("computer-use-2024-10-22");
                        anthropicTools2.push({
                            name: tool.name,
                            type: "text_editor_20241022"
                        });
                        break;
                    case "anthropic.bash_20250124":
                        betas.add("computer-use-2025-01-24");
                        anthropicTools2.push({
                            name: tool.name,
                            type: "bash_20250124"
                        });
                        break;
                    case "anthropic.bash_20241022":
                        betas.add("computer-use-2024-10-22");
                        anthropicTools2.push({
                            name: tool.name,
                            type: "bash_20241022"
                        });
                        break;
                    default:
                        toolWarnings.push({
                            type: "unsupported-tool",
                            tool
                        });
                        break;
                }
                break;
            default:
                toolWarnings.push({
                    type: "unsupported-tool",
                    tool
                });
                break;
        }
    }
    const toolChoice = mode.toolChoice;
    if (toolChoice == null) {
        return {
            tools: anthropicTools2,
            tool_choice: void 0,
            toolWarnings,
            betas
        };
    }
    const type = toolChoice.type;
    switch(type){
        case "auto":
            return {
                tools: anthropicTools2,
                tool_choice: {
                    type: "auto"
                },
                toolWarnings,
                betas
            };
        case "required":
            return {
                tools: anthropicTools2,
                tool_choice: {
                    type: "any"
                },
                toolWarnings,
                betas
            };
        case "none":
            return {
                tools: void 0,
                tool_choice: void 0,
                toolWarnings,
                betas
            };
        case "tool":
            return {
                tools: anthropicTools2,
                tool_choice: {
                    type: "tool",
                    name: toolChoice.toolName
                },
                toolWarnings,
                betas
            };
        default:
            {
                const _exhaustiveCheck = type;
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UnsupportedFunctionalityError"]({
                    functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`
                });
            }
    }
}
;
;
function convertToAnthropicMessagesPrompt({ prompt, sendReasoning, warnings }) {
    var _a, _b, _c, _d;
    const betas = /* @__PURE__ */ new Set();
    const blocks = groupIntoBlocks(prompt);
    let system = void 0;
    const messages = [];
    function getCacheControl(providerMetadata) {
        var _a2;
        const anthropic2 = providerMetadata == null ? void 0 : providerMetadata.anthropic;
        const cacheControlValue = (_a2 = anthropic2 == null ? void 0 : anthropic2.cacheControl) != null ? _a2 : anthropic2 == null ? void 0 : anthropic2.cache_control;
        return cacheControlValue;
    }
    for(let i = 0; i < blocks.length; i++){
        const block = blocks[i];
        const isLastBlock = i === blocks.length - 1;
        const type = block.type;
        switch(type){
            case "system":
                {
                    if (system != null) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UnsupportedFunctionalityError"]({
                            functionality: "Multiple system messages that are separated by user/assistant messages"
                        });
                    }
                    system = block.messages.map(({ content, providerMetadata })=>({
                            type: "text",
                            text: content,
                            cache_control: getCacheControl(providerMetadata)
                        }));
                    break;
                }
            case "user":
                {
                    const anthropicContent = [];
                    for (const message of block.messages){
                        const { role, content } = message;
                        switch(role){
                            case "user":
                                {
                                    for(let j = 0; j < content.length; j++){
                                        const part = content[j];
                                        const isLastPart = j === content.length - 1;
                                        const cacheControl = (_a = getCacheControl(part.providerMetadata)) != null ? _a : isLastPart ? getCacheControl(message.providerMetadata) : void 0;
                                        switch(part.type){
                                            case "text":
                                                {
                                                    anthropicContent.push({
                                                        type: "text",
                                                        text: part.text,
                                                        cache_control: cacheControl
                                                    });
                                                    break;
                                                }
                                            case "image":
                                                {
                                                    anthropicContent.push({
                                                        type: "image",
                                                        source: part.image instanceof URL ? {
                                                            type: "url",
                                                            url: part.image.toString()
                                                        } : {
                                                            type: "base64",
                                                            media_type: (_b = part.mimeType) != null ? _b : "image/jpeg",
                                                            data: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["convertUint8ArrayToBase64"])(part.image)
                                                        },
                                                        cache_control: cacheControl
                                                    });
                                                    break;
                                                }
                                            case "file":
                                                {
                                                    if (part.mimeType !== "application/pdf") {
                                                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UnsupportedFunctionalityError"]({
                                                            functionality: "Non-PDF files in user messages"
                                                        });
                                                    }
                                                    betas.add("pdfs-2024-09-25");
                                                    anthropicContent.push({
                                                        type: "document",
                                                        source: part.data instanceof URL ? {
                                                            type: "url",
                                                            url: part.data.toString()
                                                        } : {
                                                            type: "base64",
                                                            media_type: "application/pdf",
                                                            data: part.data
                                                        },
                                                        cache_control: cacheControl
                                                    });
                                                    break;
                                                }
                                        }
                                    }
                                    break;
                                }
                            case "tool":
                                {
                                    for(let i2 = 0; i2 < content.length; i2++){
                                        const part = content[i2];
                                        const isLastPart = i2 === content.length - 1;
                                        const cacheControl = (_c = getCacheControl(part.providerMetadata)) != null ? _c : isLastPart ? getCacheControl(message.providerMetadata) : void 0;
                                        const toolResultContent = part.content != null ? part.content.map((part2)=>{
                                            var _a2;
                                            switch(part2.type){
                                                case "text":
                                                    return {
                                                        type: "text",
                                                        text: part2.text,
                                                        cache_control: void 0
                                                    };
                                                case "image":
                                                    return {
                                                        type: "image",
                                                        source: {
                                                            type: "base64",
                                                            media_type: (_a2 = part2.mimeType) != null ? _a2 : "image/jpeg",
                                                            data: part2.data
                                                        },
                                                        cache_control: void 0
                                                    };
                                            }
                                        }) : JSON.stringify(part.result);
                                        anthropicContent.push({
                                            type: "tool_result",
                                            tool_use_id: part.toolCallId,
                                            content: toolResultContent,
                                            is_error: part.isError,
                                            cache_control: cacheControl
                                        });
                                    }
                                    break;
                                }
                            default:
                                {
                                    const _exhaustiveCheck = role;
                                    throw new Error(`Unsupported role: ${_exhaustiveCheck}`);
                                }
                        }
                    }
                    messages.push({
                        role: "user",
                        content: anthropicContent
                    });
                    break;
                }
            case "assistant":
                {
                    const anthropicContent = [];
                    for(let j = 0; j < block.messages.length; j++){
                        const message = block.messages[j];
                        const isLastMessage = j === block.messages.length - 1;
                        const { content } = message;
                        for(let k = 0; k < content.length; k++){
                            const part = content[k];
                            const isLastContentPart = k === content.length - 1;
                            const cacheControl = (_d = getCacheControl(part.providerMetadata)) != null ? _d : isLastContentPart ? getCacheControl(message.providerMetadata) : void 0;
                            switch(part.type){
                                case "text":
                                    {
                                        anthropicContent.push({
                                            type: "text",
                                            text: // trim the last text part if it's the last message in the block
                                            // because Anthropic does not allow trailing whitespace
                                            // in pre-filled assistant responses
                                            isLastBlock && isLastMessage && isLastContentPart ? part.text.trim() : part.text,
                                            cache_control: cacheControl
                                        });
                                        break;
                                    }
                                case "reasoning":
                                    {
                                        if (sendReasoning) {
                                            anthropicContent.push({
                                                type: "thinking",
                                                thinking: part.text,
                                                signature: part.signature,
                                                cache_control: cacheControl
                                            });
                                        } else {
                                            warnings.push({
                                                type: "other",
                                                message: "sending reasoning content is disabled for this model"
                                            });
                                        }
                                        break;
                                    }
                                case "redacted-reasoning":
                                    {
                                        anthropicContent.push({
                                            type: "redacted_thinking",
                                            data: part.data,
                                            cache_control: cacheControl
                                        });
                                        break;
                                    }
                                case "tool-call":
                                    {
                                        anthropicContent.push({
                                            type: "tool_use",
                                            id: part.toolCallId,
                                            name: part.toolName,
                                            input: part.args,
                                            cache_control: cacheControl
                                        });
                                        break;
                                    }
                            }
                        }
                    }
                    messages.push({
                        role: "assistant",
                        content: anthropicContent
                    });
                    break;
                }
            default:
                {
                    const _exhaustiveCheck = type;
                    throw new Error(`Unsupported type: ${_exhaustiveCheck}`);
                }
        }
    }
    return {
        prompt: {
            system,
            messages
        },
        betas
    };
}
function groupIntoBlocks(prompt) {
    const blocks = [];
    let currentBlock = void 0;
    for (const message of prompt){
        const { role } = message;
        switch(role){
            case "system":
                {
                    if ((currentBlock == null ? void 0 : currentBlock.type) !== "system") {
                        currentBlock = {
                            type: "system",
                            messages: []
                        };
                        blocks.push(currentBlock);
                    }
                    currentBlock.messages.push(message);
                    break;
                }
            case "assistant":
                {
                    if ((currentBlock == null ? void 0 : currentBlock.type) !== "assistant") {
                        currentBlock = {
                            type: "assistant",
                            messages: []
                        };
                        blocks.push(currentBlock);
                    }
                    currentBlock.messages.push(message);
                    break;
                }
            case "user":
                {
                    if ((currentBlock == null ? void 0 : currentBlock.type) !== "user") {
                        currentBlock = {
                            type: "user",
                            messages: []
                        };
                        blocks.push(currentBlock);
                    }
                    currentBlock.messages.push(message);
                    break;
                }
            case "tool":
                {
                    if ((currentBlock == null ? void 0 : currentBlock.type) !== "user") {
                        currentBlock = {
                            type: "user",
                            messages: []
                        };
                        blocks.push(currentBlock);
                    }
                    currentBlock.messages.push(message);
                    break;
                }
            default:
                {
                    const _exhaustiveCheck = role;
                    throw new Error(`Unsupported role: ${_exhaustiveCheck}`);
                }
        }
    }
    return blocks;
}
// src/map-anthropic-stop-reason.ts
function mapAnthropicStopReason(finishReason) {
    switch(finishReason){
        case "end_turn":
        case "stop_sequence":
            return "stop";
        case "tool_use":
            return "tool-calls";
        case "max_tokens":
            return "length";
        default:
            return "unknown";
    }
}
// src/anthropic-messages-language-model.ts
var AnthropicMessagesLanguageModel = class {
    constructor(modelId, settings, config){
        this.specificationVersion = "v1";
        this.defaultObjectGenerationMode = "tool";
        this.modelId = modelId;
        this.settings = settings;
        this.config = config;
    }
    supportsUrl(url) {
        return url.protocol === "https:";
    }
    get provider() {
        return this.config.provider;
    }
    get supportsImageUrls() {
        return this.config.supportsImageUrls;
    }
    async getArgs({ mode, prompt, maxTokens = 4096, // 4096: max model output tokens TODO update default in v5
    temperature, topP, topK, frequencyPenalty, presencePenalty, stopSequences, responseFormat, seed, providerMetadata: providerOptions }) {
        var _a, _b, _c;
        const type = mode.type;
        const warnings = [];
        if (frequencyPenalty != null) {
            warnings.push({
                type: "unsupported-setting",
                setting: "frequencyPenalty"
            });
        }
        if (presencePenalty != null) {
            warnings.push({
                type: "unsupported-setting",
                setting: "presencePenalty"
            });
        }
        if (seed != null) {
            warnings.push({
                type: "unsupported-setting",
                setting: "seed"
            });
        }
        if (responseFormat != null && responseFormat.type !== "text") {
            warnings.push({
                type: "unsupported-setting",
                setting: "responseFormat",
                details: "JSON response format is not supported."
            });
        }
        const { prompt: messagesPrompt, betas: messagesBetas } = convertToAnthropicMessagesPrompt({
            prompt,
            sendReasoning: (_a = this.settings.sendReasoning) != null ? _a : true,
            warnings
        });
        const anthropicOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseProviderOptions"])({
            provider: "anthropic",
            providerOptions,
            schema: anthropicProviderOptionsSchema
        });
        const isThinking = ((_b = anthropicOptions == null ? void 0 : anthropicOptions.thinking) == null ? void 0 : _b.type) === "enabled";
        const thinkingBudget = (_c = anthropicOptions == null ? void 0 : anthropicOptions.thinking) == null ? void 0 : _c.budgetTokens;
        const baseArgs = {
            // model id:
            model: this.modelId,
            // standardized settings:
            max_tokens: maxTokens,
            temperature,
            top_k: topK,
            top_p: topP,
            stop_sequences: stopSequences,
            // provider specific settings:
            ...isThinking && {
                thinking: {
                    type: "enabled",
                    budget_tokens: thinkingBudget
                }
            },
            // prompt:
            system: messagesPrompt.system,
            messages: messagesPrompt.messages
        };
        if (isThinking) {
            if (thinkingBudget == null) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UnsupportedFunctionalityError"]({
                    functionality: "thinking requires a budget"
                });
            }
            if (baseArgs.temperature != null) {
                baseArgs.temperature = void 0;
                warnings.push({
                    type: "unsupported-setting",
                    setting: "temperature",
                    details: "temperature is not supported when thinking is enabled"
                });
            }
            if (topK != null) {
                baseArgs.top_k = void 0;
                warnings.push({
                    type: "unsupported-setting",
                    setting: "topK",
                    details: "topK is not supported when thinking is enabled"
                });
            }
            if (topP != null) {
                baseArgs.top_p = void 0;
                warnings.push({
                    type: "unsupported-setting",
                    setting: "topP",
                    details: "topP is not supported when thinking is enabled"
                });
            }
            baseArgs.max_tokens = maxTokens + thinkingBudget;
        }
        switch(type){
            case "regular":
                {
                    const { tools, tool_choice, toolWarnings, betas: toolsBetas } = prepareTools(mode);
                    return {
                        args: {
                            ...baseArgs,
                            tools,
                            tool_choice
                        },
                        warnings: [
                            ...warnings,
                            ...toolWarnings
                        ],
                        betas: /* @__PURE__ */ new Set([
                            ...messagesBetas,
                            ...toolsBetas
                        ])
                    };
                }
            case "object-json":
                {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UnsupportedFunctionalityError"]({
                        functionality: "json-mode object generation"
                    });
                }
            case "object-tool":
                {
                    const { name, description, parameters } = mode.tool;
                    return {
                        args: {
                            ...baseArgs,
                            tools: [
                                {
                                    name,
                                    description,
                                    input_schema: parameters
                                }
                            ],
                            tool_choice: {
                                type: "tool",
                                name
                            }
                        },
                        warnings,
                        betas: messagesBetas
                    };
                }
            default:
                {
                    const _exhaustiveCheck = type;
                    throw new Error(`Unsupported type: ${_exhaustiveCheck}`);
                }
        }
    }
    async getHeaders({ betas, headers }) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["combineHeaders"])(await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolve"])(this.config.headers), betas.size > 0 ? {
            "anthropic-beta": Array.from(betas).join(",")
        } : {}, headers);
    }
    buildRequestUrl(isStreaming) {
        var _a, _b, _c;
        return (_c = (_b = (_a = this.config).buildRequestUrl) == null ? void 0 : _b.call(_a, this.config.baseURL, isStreaming)) != null ? _c : `${this.config.baseURL}/messages`;
    }
    transformRequestBody(args) {
        var _a, _b, _c;
        return (_c = (_b = (_a = this.config).transformRequestBody) == null ? void 0 : _b.call(_a, args)) != null ? _c : args;
    }
    async doGenerate(options) {
        var _a, _b, _c, _d;
        const { args, warnings, betas } = await this.getArgs(options);
        const { responseHeaders, value: response, rawValue: rawResponse } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["postJsonToApi"])({
            url: this.buildRequestUrl(false),
            headers: await this.getHeaders({
                betas,
                headers: options.headers
            }),
            body: this.transformRequestBody(args),
            failedResponseHandler: anthropicFailedResponseHandler,
            successfulResponseHandler: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createJsonResponseHandler"])(anthropicMessagesResponseSchema),
            abortSignal: options.abortSignal,
            fetch: this.config.fetch
        });
        const { messages: rawPrompt, ...rawSettings } = args;
        let text = "";
        for (const content of response.content){
            if (content.type === "text") {
                text += content.text;
            }
        }
        let toolCalls = void 0;
        if (response.content.some((content)=>content.type === "tool_use")) {
            toolCalls = [];
            for (const content of response.content){
                if (content.type === "tool_use") {
                    toolCalls.push({
                        toolCallType: "function",
                        toolCallId: content.id,
                        toolName: content.name,
                        args: JSON.stringify(content.input)
                    });
                }
            }
        }
        const reasoning = response.content.filter((content)=>content.type === "redacted_thinking" || content.type === "thinking").map((content)=>content.type === "thinking" ? {
                type: "text",
                text: content.thinking,
                signature: content.signature
            } : {
                type: "redacted",
                data: content.data
            });
        return {
            text,
            reasoning: reasoning.length > 0 ? reasoning : void 0,
            toolCalls,
            finishReason: mapAnthropicStopReason(response.stop_reason),
            usage: {
                promptTokens: response.usage.input_tokens,
                completionTokens: response.usage.output_tokens
            },
            rawCall: {
                rawPrompt,
                rawSettings
            },
            rawResponse: {
                headers: responseHeaders,
                body: rawResponse
            },
            response: {
                id: (_a = response.id) != null ? _a : void 0,
                modelId: (_b = response.model) != null ? _b : void 0
            },
            warnings,
            providerMetadata: {
                anthropic: {
                    cacheCreationInputTokens: (_c = response.usage.cache_creation_input_tokens) != null ? _c : null,
                    cacheReadInputTokens: (_d = response.usage.cache_read_input_tokens) != null ? _d : null
                }
            },
            request: {
                body: JSON.stringify(args)
            }
        };
    }
    async doStream(options) {
        const { args, warnings, betas } = await this.getArgs(options);
        const body = {
            ...args,
            stream: true
        };
        const { responseHeaders, value: response } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["postJsonToApi"])({
            url: this.buildRequestUrl(true),
            headers: await this.getHeaders({
                betas,
                headers: options.headers
            }),
            body: this.transformRequestBody(body),
            failedResponseHandler: anthropicFailedResponseHandler,
            successfulResponseHandler: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createEventSourceResponseHandler"])(anthropicMessagesChunkSchema),
            abortSignal: options.abortSignal,
            fetch: this.config.fetch
        });
        const { messages: rawPrompt, ...rawSettings } = args;
        let finishReason = "unknown";
        const usage = {
            promptTokens: Number.NaN,
            completionTokens: Number.NaN
        };
        const toolCallContentBlocks = {};
        let providerMetadata = void 0;
        let blockType = void 0;
        return {
            stream: response.pipeThrough(new TransformStream({
                transform (chunk, controller) {
                    var _a, _b, _c, _d;
                    if (!chunk.success) {
                        controller.enqueue({
                            type: "error",
                            error: chunk.error
                        });
                        return;
                    }
                    const value = chunk.value;
                    switch(value.type){
                        case "ping":
                            {
                                return;
                            }
                        case "content_block_start":
                            {
                                const contentBlockType = value.content_block.type;
                                blockType = contentBlockType;
                                switch(contentBlockType){
                                    case "text":
                                    case "thinking":
                                        {
                                            return;
                                        }
                                    case "redacted_thinking":
                                        {
                                            controller.enqueue({
                                                type: "redacted-reasoning",
                                                data: value.content_block.data
                                            });
                                            return;
                                        }
                                    case "tool_use":
                                        {
                                            toolCallContentBlocks[value.index] = {
                                                toolCallId: value.content_block.id,
                                                toolName: value.content_block.name,
                                                jsonText: ""
                                            };
                                            return;
                                        }
                                    default:
                                        {
                                            const _exhaustiveCheck = contentBlockType;
                                            throw new Error(`Unsupported content block type: ${_exhaustiveCheck}`);
                                        }
                                }
                            }
                        case "content_block_stop":
                            {
                                if (toolCallContentBlocks[value.index] != null) {
                                    const contentBlock = toolCallContentBlocks[value.index];
                                    controller.enqueue({
                                        type: "tool-call",
                                        toolCallType: "function",
                                        toolCallId: contentBlock.toolCallId,
                                        toolName: contentBlock.toolName,
                                        args: contentBlock.jsonText
                                    });
                                    delete toolCallContentBlocks[value.index];
                                }
                                blockType = void 0;
                                return;
                            }
                        case "content_block_delta":
                            {
                                const deltaType = value.delta.type;
                                switch(deltaType){
                                    case "text_delta":
                                        {
                                            controller.enqueue({
                                                type: "text-delta",
                                                textDelta: value.delta.text
                                            });
                                            return;
                                        }
                                    case "thinking_delta":
                                        {
                                            controller.enqueue({
                                                type: "reasoning",
                                                textDelta: value.delta.thinking
                                            });
                                            return;
                                        }
                                    case "signature_delta":
                                        {
                                            if (blockType === "thinking") {
                                                controller.enqueue({
                                                    type: "reasoning-signature",
                                                    signature: value.delta.signature
                                                });
                                            }
                                            return;
                                        }
                                    case "input_json_delta":
                                        {
                                            const contentBlock = toolCallContentBlocks[value.index];
                                            controller.enqueue({
                                                type: "tool-call-delta",
                                                toolCallType: "function",
                                                toolCallId: contentBlock.toolCallId,
                                                toolName: contentBlock.toolName,
                                                argsTextDelta: value.delta.partial_json
                                            });
                                            contentBlock.jsonText += value.delta.partial_json;
                                            return;
                                        }
                                    default:
                                        {
                                            const _exhaustiveCheck = deltaType;
                                            throw new Error(`Unsupported delta type: ${_exhaustiveCheck}`);
                                        }
                                }
                            }
                        case "message_start":
                            {
                                usage.promptTokens = value.message.usage.input_tokens;
                                usage.completionTokens = value.message.usage.output_tokens;
                                providerMetadata = {
                                    anthropic: {
                                        cacheCreationInputTokens: (_a = value.message.usage.cache_creation_input_tokens) != null ? _a : null,
                                        cacheReadInputTokens: (_b = value.message.usage.cache_read_input_tokens) != null ? _b : null
                                    }
                                };
                                controller.enqueue({
                                    type: "response-metadata",
                                    id: (_c = value.message.id) != null ? _c : void 0,
                                    modelId: (_d = value.message.model) != null ? _d : void 0
                                });
                                return;
                            }
                        case "message_delta":
                            {
                                usage.completionTokens = value.usage.output_tokens;
                                finishReason = mapAnthropicStopReason(value.delta.stop_reason);
                                return;
                            }
                        case "message_stop":
                            {
                                controller.enqueue({
                                    type: "finish",
                                    finishReason,
                                    usage,
                                    providerMetadata
                                });
                                return;
                            }
                        case "error":
                            {
                                controller.enqueue({
                                    type: "error",
                                    error: value.error
                                });
                                return;
                            }
                        default:
                            {
                                const _exhaustiveCheck = value;
                                throw new Error(`Unsupported chunk type: ${_exhaustiveCheck}`);
                            }
                    }
                }
            })),
            rawCall: {
                rawPrompt,
                rawSettings
            },
            rawResponse: {
                headers: responseHeaders
            },
            warnings,
            request: {
                body: JSON.stringify(body)
            }
        };
    }
};
var anthropicMessagesResponseSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("message"),
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullish(),
    model: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullish(),
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].discriminatedUnion("type", [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("text"),
            text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
        }),
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("thinking"),
            thinking: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
            signature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
        }),
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("redacted_thinking"),
            data: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
        }),
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("tool_use"),
            id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
            name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
            input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].unknown()
        })
    ])),
    stop_reason: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullish(),
    usage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        input_tokens: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        output_tokens: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        cache_creation_input_tokens: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullish(),
        cache_read_input_tokens: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullish()
    })
});
var anthropicMessagesChunkSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].discriminatedUnion("type", [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("message_start"),
        message: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullish(),
            model: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullish(),
            usage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
                input_tokens: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
                output_tokens: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
                cache_creation_input_tokens: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullish(),
                cache_read_input_tokens: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullish()
            })
        })
    }),
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("content_block_start"),
        index: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        content_block: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].discriminatedUnion("type", [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("text"),
                text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("thinking"),
                thinking: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("tool_use"),
                id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
                name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("redacted_thinking"),
                data: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
            })
        ])
    }),
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("content_block_delta"),
        index: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        delta: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].discriminatedUnion("type", [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("input_json_delta"),
                partial_json: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("text_delta"),
                text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("thinking_delta"),
                thinking: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("signature_delta"),
                signature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
            })
        ])
    }),
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("content_block_stop"),
        index: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    }),
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("error"),
        error: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
            message: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
        })
    }),
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("message_delta"),
        delta: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            stop_reason: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullish()
        }),
        usage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            output_tokens: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
        })
    }),
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("message_stop")
    }),
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("ping")
    })
]);
var anthropicProviderOptionsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    thinking: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].union([
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("enabled"),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal("disabled")
        ]),
        budgetTokens: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
    }).optional()
});
;
var Bash20241022Parameters = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    command: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    restart: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional()
});
function bashTool_20241022(options = {}) {
    return {
        type: "provider-defined",
        id: "anthropic.bash_20241022",
        args: {},
        parameters: Bash20241022Parameters,
        execute: options.execute,
        experimental_toToolResultContent: options.experimental_toToolResultContent
    };
}
var Bash20250124Parameters = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    command: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    restart: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional()
});
function bashTool_20250124(options = {}) {
    return {
        type: "provider-defined",
        id: "anthropic.bash_20250124",
        args: {},
        parameters: Bash20250124Parameters,
        execute: options.execute,
        experimental_toToolResultContent: options.experimental_toToolResultContent
    };
}
var TextEditor20241022Parameters = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    command: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "view",
        "create",
        "str_replace",
        "insert",
        "undo_edit"
    ]),
    path: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    file_text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    insert_line: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().optional(),
    new_str: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    old_str: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    view_range: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int()).optional()
});
function textEditorTool_20241022(options = {}) {
    return {
        type: "provider-defined",
        id: "anthropic.text_editor_20241022",
        args: {},
        parameters: TextEditor20241022Parameters,
        execute: options.execute,
        experimental_toToolResultContent: options.experimental_toToolResultContent
    };
}
var TextEditor20250124Parameters = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    command: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "view",
        "create",
        "str_replace",
        "insert",
        "undo_edit"
    ]),
    path: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    file_text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    insert_line: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int().optional(),
    new_str: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    old_str: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    view_range: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int()).optional()
});
function textEditorTool_20250124(options = {}) {
    return {
        type: "provider-defined",
        id: "anthropic.text_editor_20250124",
        args: {},
        parameters: TextEditor20250124Parameters,
        execute: options.execute,
        experimental_toToolResultContent: options.experimental_toToolResultContent
    };
}
var Computer20241022Parameters = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    action: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "key",
        "type",
        "mouse_move",
        "left_click",
        "left_click_drag",
        "right_click",
        "middle_click",
        "double_click",
        "screenshot",
        "cursor_position"
    ]),
    coordinate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int()).optional(),
    text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
});
function computerTool_20241022(options) {
    return {
        type: "provider-defined",
        id: "anthropic.computer_20241022",
        args: {
            displayWidthPx: options.displayWidthPx,
            displayHeightPx: options.displayHeightPx,
            displayNumber: options.displayNumber
        },
        parameters: Computer20241022Parameters,
        execute: options.execute,
        experimental_toToolResultContent: options.experimental_toToolResultContent
    };
}
var Computer20250124Parameters = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    action: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "key",
        "hold_key",
        "type",
        "cursor_position",
        "mouse_move",
        "left_mouse_down",
        "left_mouse_up",
        "left_click",
        "left_click_drag",
        "right_click",
        "middle_click",
        "double_click",
        "triple_click",
        "scroll",
        "wait",
        "screenshot"
    ]),
    coordinate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].tuple([
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int(),
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int()
    ]).optional(),
    duration: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    scroll_amount: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    scroll_direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "up",
        "down",
        "left",
        "right"
    ]).optional(),
    start_coordinate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].tuple([
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int(),
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().int()
    ]).optional(),
    text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
});
function computerTool_20250124(options) {
    return {
        type: "provider-defined",
        id: "anthropic.computer_20250124",
        args: {
            displayWidthPx: options.displayWidthPx,
            displayHeightPx: options.displayHeightPx,
            displayNumber: options.displayNumber
        },
        parameters: Computer20250124Parameters,
        execute: options.execute,
        experimental_toToolResultContent: options.experimental_toToolResultContent
    };
}
var anthropicTools = {
    bash_20241022: bashTool_20241022,
    bash_20250124: bashTool_20250124,
    textEditor_20241022: textEditorTool_20241022,
    textEditor_20250124: textEditorTool_20250124,
    computer_20241022: computerTool_20241022,
    computer_20250124: computerTool_20250124
};
// src/anthropic-provider.ts
function createAnthropic(options = {}) {
    var _a;
    const baseURL = (_a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withoutTrailingSlash"])(options.baseURL)) != null ? _a : "https://api.anthropic.com/v1";
    const getHeaders = ()=>({
            "anthropic-version": "2023-06-01",
            "x-api-key": (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["loadApiKey"])({
                apiKey: options.apiKey,
                environmentVariableName: "ANTHROPIC_API_KEY",
                description: "Anthropic"
            }),
            ...options.headers
        });
    const createChatModel = (modelId, settings = {})=>new AnthropicMessagesLanguageModel(modelId, settings, {
            provider: "anthropic.messages",
            baseURL,
            headers: getHeaders,
            fetch: options.fetch,
            supportsImageUrls: true
        });
    const provider = function(modelId, settings) {
        if (new.target) {
            throw new Error("The Anthropic model function cannot be called with the new keyword.");
        }
        return createChatModel(modelId, settings);
    };
    provider.languageModel = createChatModel;
    provider.chat = createChatModel;
    provider.messages = createChatModel;
    provider.textEmbeddingModel = (modelId)=>{
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NoSuchModelError"]({
            modelId,
            modelType: "textEmbeddingModel"
        });
    };
    provider.tools = anthropicTools;
    return provider;
}
var anthropic = createAnthropic();
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/@ai-sdk/ui-utils/dist/index.mjs [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/index.ts
__turbopack_context__.s({
    "asSchema": (()=>asSchema),
    "callChatApi": (()=>callChatApi),
    "callCompletionApi": (()=>callCompletionApi),
    "extractMaxToolInvocationStep": (()=>extractMaxToolInvocationStep),
    "fillMessageParts": (()=>fillMessageParts),
    "formatAssistantStreamPart": (()=>formatAssistantStreamPart),
    "formatDataStreamPart": (()=>formatDataStreamPart),
    "getMessageParts": (()=>getMessageParts),
    "getTextFromDataUrl": (()=>getTextFromDataUrl),
    "isAssistantMessageWithCompletedToolCalls": (()=>isAssistantMessageWithCompletedToolCalls),
    "isDeepEqualData": (()=>isDeepEqualData),
    "jsonSchema": (()=>jsonSchema),
    "parseAssistantStreamPart": (()=>parseAssistantStreamPart),
    "parseDataStreamPart": (()=>parseDataStreamPart),
    "parsePartialJson": (()=>parsePartialJson),
    "prepareAttachmentsForRequest": (()=>prepareAttachmentsForRequest),
    "processAssistantStream": (()=>processAssistantStream),
    "processDataStream": (()=>processDataStream),
    "processTextStream": (()=>processTextStream),
    "shouldResubmitMessages": (()=>shouldResubmitMessages),
    "updateToolCallResult": (()=>updateToolCallResult),
    "zodSchema": (()=>zodSchema)
});
// src/process-chat-response.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ai-sdk/provider-utils/dist/index.mjs [app-route] (ecmascript)");
// src/zod-schema.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2d$to$2d$json$2d$schema$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod-to-json-schema/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2d$to$2d$json$2d$schema$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/zod-to-json-schema/dist/esm/index.js [app-route] (ecmascript) <locals>");
;
// src/assistant-stream-parts.ts
var textStreamPart = {
    code: "0",
    name: "text",
    parse: (value)=>{
        if (typeof value !== "string") {
            throw new Error('"text" parts expect a string value.');
        }
        return {
            type: "text",
            value
        };
    }
};
var errorStreamPart = {
    code: "3",
    name: "error",
    parse: (value)=>{
        if (typeof value !== "string") {
            throw new Error('"error" parts expect a string value.');
        }
        return {
            type: "error",
            value
        };
    }
};
var assistantMessageStreamPart = {
    code: "4",
    name: "assistant_message",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("id" in value) || !("role" in value) || !("content" in value) || typeof value.id !== "string" || typeof value.role !== "string" || value.role !== "assistant" || !Array.isArray(value.content) || !value.content.every((item)=>item != null && typeof item === "object" && "type" in item && item.type === "text" && "text" in item && item.text != null && typeof item.text === "object" && "value" in item.text && typeof item.text.value === "string")) {
            throw new Error('"assistant_message" parts expect an object with an "id", "role", and "content" property.');
        }
        return {
            type: "assistant_message",
            value
        };
    }
};
var assistantControlDataStreamPart = {
    code: "5",
    name: "assistant_control_data",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("threadId" in value) || !("messageId" in value) || typeof value.threadId !== "string" || typeof value.messageId !== "string") {
            throw new Error('"assistant_control_data" parts expect an object with a "threadId" and "messageId" property.');
        }
        return {
            type: "assistant_control_data",
            value: {
                threadId: value.threadId,
                messageId: value.messageId
            }
        };
    }
};
var dataMessageStreamPart = {
    code: "6",
    name: "data_message",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("role" in value) || !("data" in value) || typeof value.role !== "string" || value.role !== "data") {
            throw new Error('"data_message" parts expect an object with a "role" and "data" property.');
        }
        return {
            type: "data_message",
            value
        };
    }
};
var assistantStreamParts = [
    textStreamPart,
    errorStreamPart,
    assistantMessageStreamPart,
    assistantControlDataStreamPart,
    dataMessageStreamPart
];
var assistantStreamPartsByCode = {
    [textStreamPart.code]: textStreamPart,
    [errorStreamPart.code]: errorStreamPart,
    [assistantMessageStreamPart.code]: assistantMessageStreamPart,
    [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,
    [dataMessageStreamPart.code]: dataMessageStreamPart
};
var StreamStringPrefixes = {
    [textStreamPart.name]: textStreamPart.code,
    [errorStreamPart.name]: errorStreamPart.code,
    [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,
    [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,
    [dataMessageStreamPart.name]: dataMessageStreamPart.code
};
var validCodes = assistantStreamParts.map((part)=>part.code);
var parseAssistantStreamPart = (line)=>{
    const firstSeparatorIndex = line.indexOf(":");
    if (firstSeparatorIndex === -1) {
        throw new Error("Failed to parse stream string. No separator found.");
    }
    const prefix = line.slice(0, firstSeparatorIndex);
    if (!validCodes.includes(prefix)) {
        throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);
    }
    const code = prefix;
    const textValue = line.slice(firstSeparatorIndex + 1);
    const jsonValue = JSON.parse(textValue);
    return assistantStreamPartsByCode[code].parse(jsonValue);
};
function formatAssistantStreamPart(type, value) {
    const streamPart = assistantStreamParts.find((part)=>part.name === type);
    if (!streamPart) {
        throw new Error(`Invalid stream part type: ${type}`);
    }
    return `${streamPart.code}:${JSON.stringify(value)}
`;
}
;
// src/duplicated/usage.ts
function calculateLanguageModelUsage({ promptTokens, completionTokens }) {
    return {
        promptTokens,
        completionTokens,
        totalTokens: promptTokens + completionTokens
    };
}
;
// src/fix-json.ts
function fixJson(input) {
    const stack = [
        "ROOT"
    ];
    let lastValidIndex = -1;
    let literalStart = null;
    function processValueStart(char, i, swapState) {
        {
            switch(char){
                case '"':
                    {
                        lastValidIndex = i;
                        stack.pop();
                        stack.push(swapState);
                        stack.push("INSIDE_STRING");
                        break;
                    }
                case "f":
                case "t":
                case "n":
                    {
                        lastValidIndex = i;
                        literalStart = i;
                        stack.pop();
                        stack.push(swapState);
                        stack.push("INSIDE_LITERAL");
                        break;
                    }
                case "-":
                    {
                        stack.pop();
                        stack.push(swapState);
                        stack.push("INSIDE_NUMBER");
                        break;
                    }
                case "0":
                case "1":
                case "2":
                case "3":
                case "4":
                case "5":
                case "6":
                case "7":
                case "8":
                case "9":
                    {
                        lastValidIndex = i;
                        stack.pop();
                        stack.push(swapState);
                        stack.push("INSIDE_NUMBER");
                        break;
                    }
                case "{":
                    {
                        lastValidIndex = i;
                        stack.pop();
                        stack.push(swapState);
                        stack.push("INSIDE_OBJECT_START");
                        break;
                    }
                case "[":
                    {
                        lastValidIndex = i;
                        stack.pop();
                        stack.push(swapState);
                        stack.push("INSIDE_ARRAY_START");
                        break;
                    }
            }
        }
    }
    function processAfterObjectValue(char, i) {
        switch(char){
            case ",":
                {
                    stack.pop();
                    stack.push("INSIDE_OBJECT_AFTER_COMMA");
                    break;
                }
            case "}":
                {
                    lastValidIndex = i;
                    stack.pop();
                    break;
                }
        }
    }
    function processAfterArrayValue(char, i) {
        switch(char){
            case ",":
                {
                    stack.pop();
                    stack.push("INSIDE_ARRAY_AFTER_COMMA");
                    break;
                }
            case "]":
                {
                    lastValidIndex = i;
                    stack.pop();
                    break;
                }
        }
    }
    for(let i = 0; i < input.length; i++){
        const char = input[i];
        const currentState = stack[stack.length - 1];
        switch(currentState){
            case "ROOT":
                processValueStart(char, i, "FINISH");
                break;
            case "INSIDE_OBJECT_START":
                {
                    switch(char){
                        case '"':
                            {
                                stack.pop();
                                stack.push("INSIDE_OBJECT_KEY");
                                break;
                            }
                        case "}":
                            {
                                lastValidIndex = i;
                                stack.pop();
                                break;
                            }
                    }
                    break;
                }
            case "INSIDE_OBJECT_AFTER_COMMA":
                {
                    switch(char){
                        case '"':
                            {
                                stack.pop();
                                stack.push("INSIDE_OBJECT_KEY");
                                break;
                            }
                    }
                    break;
                }
            case "INSIDE_OBJECT_KEY":
                {
                    switch(char){
                        case '"':
                            {
                                stack.pop();
                                stack.push("INSIDE_OBJECT_AFTER_KEY");
                                break;
                            }
                    }
                    break;
                }
            case "INSIDE_OBJECT_AFTER_KEY":
                {
                    switch(char){
                        case ":":
                            {
                                stack.pop();
                                stack.push("INSIDE_OBJECT_BEFORE_VALUE");
                                break;
                            }
                    }
                    break;
                }
            case "INSIDE_OBJECT_BEFORE_VALUE":
                {
                    processValueStart(char, i, "INSIDE_OBJECT_AFTER_VALUE");
                    break;
                }
            case "INSIDE_OBJECT_AFTER_VALUE":
                {
                    processAfterObjectValue(char, i);
                    break;
                }
            case "INSIDE_STRING":
                {
                    switch(char){
                        case '"':
                            {
                                stack.pop();
                                lastValidIndex = i;
                                break;
                            }
                        case "\\":
                            {
                                stack.push("INSIDE_STRING_ESCAPE");
                                break;
                            }
                        default:
                            {
                                lastValidIndex = i;
                            }
                    }
                    break;
                }
            case "INSIDE_ARRAY_START":
                {
                    switch(char){
                        case "]":
                            {
                                lastValidIndex = i;
                                stack.pop();
                                break;
                            }
                        default:
                            {
                                lastValidIndex = i;
                                processValueStart(char, i, "INSIDE_ARRAY_AFTER_VALUE");
                                break;
                            }
                    }
                    break;
                }
            case "INSIDE_ARRAY_AFTER_VALUE":
                {
                    switch(char){
                        case ",":
                            {
                                stack.pop();
                                stack.push("INSIDE_ARRAY_AFTER_COMMA");
                                break;
                            }
                        case "]":
                            {
                                lastValidIndex = i;
                                stack.pop();
                                break;
                            }
                        default:
                            {
                                lastValidIndex = i;
                                break;
                            }
                    }
                    break;
                }
            case "INSIDE_ARRAY_AFTER_COMMA":
                {
                    processValueStart(char, i, "INSIDE_ARRAY_AFTER_VALUE");
                    break;
                }
            case "INSIDE_STRING_ESCAPE":
                {
                    stack.pop();
                    lastValidIndex = i;
                    break;
                }
            case "INSIDE_NUMBER":
                {
                    switch(char){
                        case "0":
                        case "1":
                        case "2":
                        case "3":
                        case "4":
                        case "5":
                        case "6":
                        case "7":
                        case "8":
                        case "9":
                            {
                                lastValidIndex = i;
                                break;
                            }
                        case "e":
                        case "E":
                        case "-":
                        case ".":
                            {
                                break;
                            }
                        case ",":
                            {
                                stack.pop();
                                if (stack[stack.length - 1] === "INSIDE_ARRAY_AFTER_VALUE") {
                                    processAfterArrayValue(char, i);
                                }
                                if (stack[stack.length - 1] === "INSIDE_OBJECT_AFTER_VALUE") {
                                    processAfterObjectValue(char, i);
                                }
                                break;
                            }
                        case "}":
                            {
                                stack.pop();
                                if (stack[stack.length - 1] === "INSIDE_OBJECT_AFTER_VALUE") {
                                    processAfterObjectValue(char, i);
                                }
                                break;
                            }
                        case "]":
                            {
                                stack.pop();
                                if (stack[stack.length - 1] === "INSIDE_ARRAY_AFTER_VALUE") {
                                    processAfterArrayValue(char, i);
                                }
                                break;
                            }
                        default:
                            {
                                stack.pop();
                                break;
                            }
                    }
                    break;
                }
            case "INSIDE_LITERAL":
                {
                    const partialLiteral = input.substring(literalStart, i + 1);
                    if (!"false".startsWith(partialLiteral) && !"true".startsWith(partialLiteral) && !"null".startsWith(partialLiteral)) {
                        stack.pop();
                        if (stack[stack.length - 1] === "INSIDE_OBJECT_AFTER_VALUE") {
                            processAfterObjectValue(char, i);
                        } else if (stack[stack.length - 1] === "INSIDE_ARRAY_AFTER_VALUE") {
                            processAfterArrayValue(char, i);
                        }
                    } else {
                        lastValidIndex = i;
                    }
                    break;
                }
        }
    }
    let result = input.slice(0, lastValidIndex + 1);
    for(let i = stack.length - 1; i >= 0; i--){
        const state = stack[i];
        switch(state){
            case "INSIDE_STRING":
                {
                    result += '"';
                    break;
                }
            case "INSIDE_OBJECT_KEY":
            case "INSIDE_OBJECT_AFTER_KEY":
            case "INSIDE_OBJECT_AFTER_COMMA":
            case "INSIDE_OBJECT_START":
            case "INSIDE_OBJECT_BEFORE_VALUE":
            case "INSIDE_OBJECT_AFTER_VALUE":
                {
                    result += "}";
                    break;
                }
            case "INSIDE_ARRAY_START":
            case "INSIDE_ARRAY_AFTER_COMMA":
            case "INSIDE_ARRAY_AFTER_VALUE":
                {
                    result += "]";
                    break;
                }
            case "INSIDE_LITERAL":
                {
                    const partialLiteral = input.substring(literalStart, input.length);
                    if ("true".startsWith(partialLiteral)) {
                        result += "true".slice(partialLiteral.length);
                    } else if ("false".startsWith(partialLiteral)) {
                        result += "false".slice(partialLiteral.length);
                    } else if ("null".startsWith(partialLiteral)) {
                        result += "null".slice(partialLiteral.length);
                    }
                }
        }
    }
    return result;
}
// src/parse-partial-json.ts
function parsePartialJson(jsonText) {
    if (jsonText === void 0) {
        return {
            value: void 0,
            state: "undefined-input"
        };
    }
    let result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["safeParseJSON"])({
        text: jsonText
    });
    if (result.success) {
        return {
            value: result.value,
            state: "successful-parse"
        };
    }
    result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["safeParseJSON"])({
        text: fixJson(jsonText)
    });
    if (result.success) {
        return {
            value: result.value,
            state: "repaired-parse"
        };
    }
    return {
        value: void 0,
        state: "failed-parse"
    };
}
// src/data-stream-parts.ts
var textStreamPart2 = {
    code: "0",
    name: "text",
    parse: (value)=>{
        if (typeof value !== "string") {
            throw new Error('"text" parts expect a string value.');
        }
        return {
            type: "text",
            value
        };
    }
};
var dataStreamPart = {
    code: "2",
    name: "data",
    parse: (value)=>{
        if (!Array.isArray(value)) {
            throw new Error('"data" parts expect an array value.');
        }
        return {
            type: "data",
            value
        };
    }
};
var errorStreamPart2 = {
    code: "3",
    name: "error",
    parse: (value)=>{
        if (typeof value !== "string") {
            throw new Error('"error" parts expect a string value.');
        }
        return {
            type: "error",
            value
        };
    }
};
var messageAnnotationsStreamPart = {
    code: "8",
    name: "message_annotations",
    parse: (value)=>{
        if (!Array.isArray(value)) {
            throw new Error('"message_annotations" parts expect an array value.');
        }
        return {
            type: "message_annotations",
            value
        };
    }
};
var toolCallStreamPart = {
    code: "9",
    name: "tool_call",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("toolCallId" in value) || typeof value.toolCallId !== "string" || !("toolName" in value) || typeof value.toolName !== "string" || !("args" in value) || typeof value.args !== "object") {
            throw new Error('"tool_call" parts expect an object with a "toolCallId", "toolName", and "args" property.');
        }
        return {
            type: "tool_call",
            value
        };
    }
};
var toolResultStreamPart = {
    code: "a",
    name: "tool_result",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("toolCallId" in value) || typeof value.toolCallId !== "string" || !("result" in value)) {
            throw new Error('"tool_result" parts expect an object with a "toolCallId" and a "result" property.');
        }
        return {
            type: "tool_result",
            value
        };
    }
};
var toolCallStreamingStartStreamPart = {
    code: "b",
    name: "tool_call_streaming_start",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("toolCallId" in value) || typeof value.toolCallId !== "string" || !("toolName" in value) || typeof value.toolName !== "string") {
            throw new Error('"tool_call_streaming_start" parts expect an object with a "toolCallId" and "toolName" property.');
        }
        return {
            type: "tool_call_streaming_start",
            value
        };
    }
};
var toolCallDeltaStreamPart = {
    code: "c",
    name: "tool_call_delta",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("toolCallId" in value) || typeof value.toolCallId !== "string" || !("argsTextDelta" in value) || typeof value.argsTextDelta !== "string") {
            throw new Error('"tool_call_delta" parts expect an object with a "toolCallId" and "argsTextDelta" property.');
        }
        return {
            type: "tool_call_delta",
            value
        };
    }
};
var finishMessageStreamPart = {
    code: "d",
    name: "finish_message",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("finishReason" in value) || typeof value.finishReason !== "string") {
            throw new Error('"finish_message" parts expect an object with a "finishReason" property.');
        }
        const result = {
            finishReason: value.finishReason
        };
        if ("usage" in value && value.usage != null && typeof value.usage === "object" && "promptTokens" in value.usage && "completionTokens" in value.usage) {
            result.usage = {
                promptTokens: typeof value.usage.promptTokens === "number" ? value.usage.promptTokens : Number.NaN,
                completionTokens: typeof value.usage.completionTokens === "number" ? value.usage.completionTokens : Number.NaN
            };
        }
        return {
            type: "finish_message",
            value: result
        };
    }
};
var finishStepStreamPart = {
    code: "e",
    name: "finish_step",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("finishReason" in value) || typeof value.finishReason !== "string") {
            throw new Error('"finish_step" parts expect an object with a "finishReason" property.');
        }
        const result = {
            finishReason: value.finishReason,
            isContinued: false
        };
        if ("usage" in value && value.usage != null && typeof value.usage === "object" && "promptTokens" in value.usage && "completionTokens" in value.usage) {
            result.usage = {
                promptTokens: typeof value.usage.promptTokens === "number" ? value.usage.promptTokens : Number.NaN,
                completionTokens: typeof value.usage.completionTokens === "number" ? value.usage.completionTokens : Number.NaN
            };
        }
        if ("isContinued" in value && typeof value.isContinued === "boolean") {
            result.isContinued = value.isContinued;
        }
        return {
            type: "finish_step",
            value: result
        };
    }
};
var startStepStreamPart = {
    code: "f",
    name: "start_step",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("messageId" in value) || typeof value.messageId !== "string") {
            throw new Error('"start_step" parts expect an object with an "id" property.');
        }
        return {
            type: "start_step",
            value: {
                messageId: value.messageId
            }
        };
    }
};
var reasoningStreamPart = {
    code: "g",
    name: "reasoning",
    parse: (value)=>{
        if (typeof value !== "string") {
            throw new Error('"reasoning" parts expect a string value.');
        }
        return {
            type: "reasoning",
            value
        };
    }
};
var sourcePart = {
    code: "h",
    name: "source",
    parse: (value)=>{
        if (value == null || typeof value !== "object") {
            throw new Error('"source" parts expect a Source object.');
        }
        return {
            type: "source",
            value
        };
    }
};
var redactedReasoningStreamPart = {
    code: "i",
    name: "redacted_reasoning",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("data" in value) || typeof value.data !== "string") {
            throw new Error('"redacted_reasoning" parts expect an object with a "data" property.');
        }
        return {
            type: "redacted_reasoning",
            value: {
                data: value.data
            }
        };
    }
};
var reasoningSignatureStreamPart = {
    code: "j",
    name: "reasoning_signature",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("signature" in value) || typeof value.signature !== "string") {
            throw new Error('"reasoning_signature" parts expect an object with a "signature" property.');
        }
        return {
            type: "reasoning_signature",
            value: {
                signature: value.signature
            }
        };
    }
};
var fileStreamPart = {
    code: "k",
    name: "file",
    parse: (value)=>{
        if (value == null || typeof value !== "object" || !("data" in value) || typeof value.data !== "string" || !("mimeType" in value) || typeof value.mimeType !== "string") {
            throw new Error('"file" parts expect an object with a "data" and "mimeType" property.');
        }
        return {
            type: "file",
            value
        };
    }
};
var dataStreamParts = [
    textStreamPart2,
    dataStreamPart,
    errorStreamPart2,
    messageAnnotationsStreamPart,
    toolCallStreamPart,
    toolResultStreamPart,
    toolCallStreamingStartStreamPart,
    toolCallDeltaStreamPart,
    finishMessageStreamPart,
    finishStepStreamPart,
    startStepStreamPart,
    reasoningStreamPart,
    sourcePart,
    redactedReasoningStreamPart,
    reasoningSignatureStreamPart,
    fileStreamPart
];
var dataStreamPartsByCode = Object.fromEntries(dataStreamParts.map((part)=>[
        part.code,
        part
    ]));
var DataStreamStringPrefixes = Object.fromEntries(dataStreamParts.map((part)=>[
        part.name,
        part.code
    ]));
var validCodes2 = dataStreamParts.map((part)=>part.code);
var parseDataStreamPart = (line)=>{
    const firstSeparatorIndex = line.indexOf(":");
    if (firstSeparatorIndex === -1) {
        throw new Error("Failed to parse stream string. No separator found.");
    }
    const prefix = line.slice(0, firstSeparatorIndex);
    if (!validCodes2.includes(prefix)) {
        throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);
    }
    const code = prefix;
    const textValue = line.slice(firstSeparatorIndex + 1);
    const jsonValue = JSON.parse(textValue);
    return dataStreamPartsByCode[code].parse(jsonValue);
};
function formatDataStreamPart(type, value) {
    const streamPart = dataStreamParts.find((part)=>part.name === type);
    if (!streamPart) {
        throw new Error(`Invalid stream part type: ${type}`);
    }
    return `${streamPart.code}:${JSON.stringify(value)}
`;
}
// src/process-data-stream.ts
var NEWLINE = "\n".charCodeAt(0);
function concatChunks(chunks, totalLength) {
    const concatenatedChunks = new Uint8Array(totalLength);
    let offset = 0;
    for (const chunk of chunks){
        concatenatedChunks.set(chunk, offset);
        offset += chunk.length;
    }
    chunks.length = 0;
    return concatenatedChunks;
}
async function processDataStream({ stream, onTextPart, onReasoningPart, onReasoningSignaturePart, onRedactedReasoningPart, onSourcePart, onFilePart, onDataPart, onErrorPart, onToolCallStreamingStartPart, onToolCallDeltaPart, onToolCallPart, onToolResultPart, onMessageAnnotationsPart, onFinishMessagePart, onFinishStepPart, onStartStepPart }) {
    const reader = stream.getReader();
    const decoder = new TextDecoder();
    const chunks = [];
    let totalLength = 0;
    while(true){
        const { value } = await reader.read();
        if (value) {
            chunks.push(value);
            totalLength += value.length;
            if (value[value.length - 1] !== NEWLINE) {
                continue;
            }
        }
        if (chunks.length === 0) {
            break;
        }
        const concatenatedChunks = concatChunks(chunks, totalLength);
        totalLength = 0;
        const streamParts = decoder.decode(concatenatedChunks, {
            stream: true
        }).split("\n").filter((line)=>line !== "").map(parseDataStreamPart);
        for (const { type, value: value2 } of streamParts){
            switch(type){
                case "text":
                    await (onTextPart == null ? void 0 : onTextPart(value2));
                    break;
                case "reasoning":
                    await (onReasoningPart == null ? void 0 : onReasoningPart(value2));
                    break;
                case "reasoning_signature":
                    await (onReasoningSignaturePart == null ? void 0 : onReasoningSignaturePart(value2));
                    break;
                case "redacted_reasoning":
                    await (onRedactedReasoningPart == null ? void 0 : onRedactedReasoningPart(value2));
                    break;
                case "file":
                    await (onFilePart == null ? void 0 : onFilePart(value2));
                    break;
                case "source":
                    await (onSourcePart == null ? void 0 : onSourcePart(value2));
                    break;
                case "data":
                    await (onDataPart == null ? void 0 : onDataPart(value2));
                    break;
                case "error":
                    await (onErrorPart == null ? void 0 : onErrorPart(value2));
                    break;
                case "message_annotations":
                    await (onMessageAnnotationsPart == null ? void 0 : onMessageAnnotationsPart(value2));
                    break;
                case "tool_call_streaming_start":
                    await (onToolCallStreamingStartPart == null ? void 0 : onToolCallStreamingStartPart(value2));
                    break;
                case "tool_call_delta":
                    await (onToolCallDeltaPart == null ? void 0 : onToolCallDeltaPart(value2));
                    break;
                case "tool_call":
                    await (onToolCallPart == null ? void 0 : onToolCallPart(value2));
                    break;
                case "tool_result":
                    await (onToolResultPart == null ? void 0 : onToolResultPart(value2));
                    break;
                case "finish_message":
                    await (onFinishMessagePart == null ? void 0 : onFinishMessagePart(value2));
                    break;
                case "finish_step":
                    await (onFinishStepPart == null ? void 0 : onFinishStepPart(value2));
                    break;
                case "start_step":
                    await (onStartStepPart == null ? void 0 : onStartStepPart(value2));
                    break;
                default:
                    {
                        const exhaustiveCheck = type;
                        throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);
                    }
            }
        }
    }
}
// src/process-chat-response.ts
async function processChatResponse({ stream, update, onToolCall, onFinish, generateId: generateId2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"], getCurrentDate = ()=>/* @__PURE__ */ new Date(), lastMessage }) {
    var _a, _b;
    const replaceLastMessage = (lastMessage == null ? void 0 : lastMessage.role) === "assistant";
    let step = replaceLastMessage ? 1 + // find max step in existing tool invocations:
    ((_b = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.reduce((max, toolInvocation)=>{
        var _a2;
        return Math.max(max, (_a2 = toolInvocation.step) != null ? _a2 : 0);
    }, 0)) != null ? _b : 0) : 0;
    const message = replaceLastMessage ? structuredClone(lastMessage) : {
        id: generateId2(),
        createdAt: getCurrentDate(),
        role: "assistant",
        content: "",
        parts: []
    };
    let currentTextPart = void 0;
    let currentReasoningPart = void 0;
    let currentReasoningTextDetail = void 0;
    function updateToolInvocationPart(toolCallId, invocation) {
        const part = message.parts.find((part2)=>part2.type === "tool-invocation" && part2.toolInvocation.toolCallId === toolCallId);
        if (part != null) {
            part.toolInvocation = invocation;
        } else {
            message.parts.push({
                type: "tool-invocation",
                toolInvocation: invocation
            });
        }
    }
    const data = [];
    let messageAnnotations = replaceLastMessage ? lastMessage == null ? void 0 : lastMessage.annotations : void 0;
    const partialToolCalls = {};
    let usage = {
        completionTokens: NaN,
        promptTokens: NaN,
        totalTokens: NaN
    };
    let finishReason = "unknown";
    function execUpdate() {
        const copiedData = [
            ...data
        ];
        if (messageAnnotations == null ? void 0 : messageAnnotations.length) {
            message.annotations = messageAnnotations;
        }
        const copiedMessage = {
            // deep copy the message to ensure that deep changes (msg attachments) are updated
            // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.
            ...structuredClone(message),
            // add a revision id to ensure that the message is updated with SWR. SWR uses a
            // hashing approach by default to detect changes, but it only works for shallow
            // changes. This is why we need to add a revision id to ensure that the message
            // is updated with SWR (without it, the changes get stuck in SWR and are not
            // forwarded to rendering):
            revisionId: generateId2()
        };
        update({
            message: copiedMessage,
            data: copiedData,
            replaceLastMessage
        });
    }
    await processDataStream({
        stream,
        onTextPart (value) {
            if (currentTextPart == null) {
                currentTextPart = {
                    type: "text",
                    text: value
                };
                message.parts.push(currentTextPart);
            } else {
                currentTextPart.text += value;
            }
            message.content += value;
            execUpdate();
        },
        onReasoningPart (value) {
            var _a2;
            if (currentReasoningTextDetail == null) {
                currentReasoningTextDetail = {
                    type: "text",
                    text: value
                };
                if (currentReasoningPart != null) {
                    currentReasoningPart.details.push(currentReasoningTextDetail);
                }
            } else {
                currentReasoningTextDetail.text += value;
            }
            if (currentReasoningPart == null) {
                currentReasoningPart = {
                    type: "reasoning",
                    reasoning: value,
                    details: [
                        currentReasoningTextDetail
                    ]
                };
                message.parts.push(currentReasoningPart);
            } else {
                currentReasoningPart.reasoning += value;
            }
            message.reasoning = ((_a2 = message.reasoning) != null ? _a2 : "") + value;
            execUpdate();
        },
        onReasoningSignaturePart (value) {
            if (currentReasoningTextDetail != null) {
                currentReasoningTextDetail.signature = value.signature;
            }
        },
        onRedactedReasoningPart (value) {
            if (currentReasoningPart == null) {
                currentReasoningPart = {
                    type: "reasoning",
                    reasoning: "",
                    details: []
                };
                message.parts.push(currentReasoningPart);
            }
            currentReasoningPart.details.push({
                type: "redacted",
                data: value.data
            });
            currentReasoningTextDetail = void 0;
            execUpdate();
        },
        onFilePart (value) {
            message.parts.push({
                type: "file",
                mimeType: value.mimeType,
                data: value.data
            });
            execUpdate();
        },
        onSourcePart (value) {
            message.parts.push({
                type: "source",
                source: value
            });
            execUpdate();
        },
        onToolCallStreamingStartPart (value) {
            if (message.toolInvocations == null) {
                message.toolInvocations = [];
            }
            partialToolCalls[value.toolCallId] = {
                text: "",
                step,
                toolName: value.toolName,
                index: message.toolInvocations.length
            };
            const invocation = {
                state: "partial-call",
                step,
                toolCallId: value.toolCallId,
                toolName: value.toolName,
                args: void 0
            };
            message.toolInvocations.push(invocation);
            updateToolInvocationPart(value.toolCallId, invocation);
            execUpdate();
        },
        onToolCallDeltaPart (value) {
            const partialToolCall = partialToolCalls[value.toolCallId];
            partialToolCall.text += value.argsTextDelta;
            const { value: partialArgs } = parsePartialJson(partialToolCall.text);
            const invocation = {
                state: "partial-call",
                step: partialToolCall.step,
                toolCallId: value.toolCallId,
                toolName: partialToolCall.toolName,
                args: partialArgs
            };
            message.toolInvocations[partialToolCall.index] = invocation;
            updateToolInvocationPart(value.toolCallId, invocation);
            execUpdate();
        },
        async onToolCallPart (value) {
            const invocation = {
                state: "call",
                step,
                ...value
            };
            if (partialToolCalls[value.toolCallId] != null) {
                message.toolInvocations[partialToolCalls[value.toolCallId].index] = invocation;
            } else {
                if (message.toolInvocations == null) {
                    message.toolInvocations = [];
                }
                message.toolInvocations.push(invocation);
            }
            updateToolInvocationPart(value.toolCallId, invocation);
            execUpdate();
            if (onToolCall) {
                const result = await onToolCall({
                    toolCall: value
                });
                if (result != null) {
                    const invocation2 = {
                        state: "result",
                        step,
                        ...value,
                        result
                    };
                    message.toolInvocations[message.toolInvocations.length - 1] = invocation2;
                    updateToolInvocationPart(value.toolCallId, invocation2);
                    execUpdate();
                }
            }
        },
        onToolResultPart (value) {
            const toolInvocations = message.toolInvocations;
            if (toolInvocations == null) {
                throw new Error("tool_result must be preceded by a tool_call");
            }
            const toolInvocationIndex = toolInvocations.findIndex((invocation2)=>invocation2.toolCallId === value.toolCallId);
            if (toolInvocationIndex === -1) {
                throw new Error("tool_result must be preceded by a tool_call with the same toolCallId");
            }
            const invocation = {
                ...toolInvocations[toolInvocationIndex],
                state: "result",
                ...value
            };
            toolInvocations[toolInvocationIndex] = invocation;
            updateToolInvocationPart(value.toolCallId, invocation);
            execUpdate();
        },
        onDataPart (value) {
            data.push(...value);
            execUpdate();
        },
        onMessageAnnotationsPart (value) {
            if (messageAnnotations == null) {
                messageAnnotations = [
                    ...value
                ];
            } else {
                messageAnnotations.push(...value);
            }
            execUpdate();
        },
        onFinishStepPart (value) {
            step += 1;
            currentTextPart = value.isContinued ? currentTextPart : void 0;
            currentReasoningPart = void 0;
            currentReasoningTextDetail = void 0;
        },
        onStartStepPart (value) {
            if (!replaceLastMessage) {
                message.id = value.messageId;
            }
            message.parts.push({
                type: "step-start"
            });
            execUpdate();
        },
        onFinishMessagePart (value) {
            finishReason = value.finishReason;
            if (value.usage != null) {
                usage = calculateLanguageModelUsage(value.usage);
            }
        },
        onErrorPart (error) {
            throw new Error(error);
        }
    });
    onFinish == null ? void 0 : onFinish({
        message,
        finishReason,
        usage
    });
}
;
// src/process-text-stream.ts
async function processTextStream({ stream, onTextPart }) {
    const reader = stream.pipeThrough(new TextDecoderStream()).getReader();
    while(true){
        const { done, value } = await reader.read();
        if (done) {
            break;
        }
        await onTextPart(value);
    }
}
// src/process-chat-text-response.ts
async function processChatTextResponse({ stream, update, onFinish, getCurrentDate = ()=>/* @__PURE__ */ new Date(), generateId: generateId2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"] }) {
    const textPart = {
        type: "text",
        text: ""
    };
    const resultMessage = {
        id: generateId2(),
        createdAt: getCurrentDate(),
        role: "assistant",
        content: "",
        parts: [
            textPart
        ]
    };
    await processTextStream({
        stream,
        onTextPart: (chunk)=>{
            resultMessage.content += chunk;
            textPart.text += chunk;
            update({
                message: {
                    ...resultMessage
                },
                data: [],
                replaceLastMessage: false
            });
        }
    });
    onFinish == null ? void 0 : onFinish(resultMessage, {
        usage: {
            completionTokens: NaN,
            promptTokens: NaN,
            totalTokens: NaN
        },
        finishReason: "unknown"
    });
}
// src/call-chat-api.ts
var getOriginalFetch = ()=>fetch;
async function callChatApi({ api, body, streamProtocol = "data", credentials, headers, abortController, restoreMessagesOnFailure, onResponse, onUpdate, onFinish, onToolCall, generateId: generateId2, fetch: fetch2 = getOriginalFetch(), lastMessage, requestType = "generate" }) {
    var _a, _b, _c;
    const request = requestType === "resume" ? fetch2(`${api}?chatId=${body.id}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            ...headers
        },
        signal: (_a = abortController == null ? void 0 : abortController()) == null ? void 0 : _a.signal,
        credentials
    }) : fetch2(api, {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
            "Content-Type": "application/json",
            ...headers
        },
        signal: (_b = abortController == null ? void 0 : abortController()) == null ? void 0 : _b.signal,
        credentials
    });
    const response = await request.catch((err)=>{
        restoreMessagesOnFailure();
        throw err;
    });
    if (onResponse) {
        try {
            await onResponse(response);
        } catch (err) {
            throw err;
        }
    }
    if (!response.ok) {
        restoreMessagesOnFailure();
        throw new Error((_c = await response.text()) != null ? _c : "Failed to fetch the chat response.");
    }
    if (!response.body) {
        throw new Error("The response body is empty.");
    }
    switch(streamProtocol){
        case "text":
            {
                await processChatTextResponse({
                    stream: response.body,
                    update: onUpdate,
                    onFinish,
                    generateId: generateId2
                });
                return;
            }
        case "data":
            {
                await processChatResponse({
                    stream: response.body,
                    update: onUpdate,
                    lastMessage,
                    onToolCall,
                    onFinish ({ message, finishReason, usage }) {
                        if (onFinish && message != null) {
                            onFinish(message, {
                                usage,
                                finishReason
                            });
                        }
                    },
                    generateId: generateId2
                });
                return;
            }
        default:
            {
                const exhaustiveCheck = streamProtocol;
                throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);
            }
    }
}
// src/call-completion-api.ts
var getOriginalFetch2 = ()=>fetch;
async function callCompletionApi({ api, prompt, credentials, headers, body, streamProtocol = "data", setCompletion, setLoading, setError, setAbortController, onResponse, onFinish, onError, onData, fetch: fetch2 = getOriginalFetch2() }) {
    var _a;
    try {
        setLoading(true);
        setError(void 0);
        const abortController = new AbortController();
        setAbortController(abortController);
        setCompletion("");
        const response = await fetch2(api, {
            method: "POST",
            body: JSON.stringify({
                prompt,
                ...body
            }),
            credentials,
            headers: {
                "Content-Type": "application/json",
                ...headers
            },
            signal: abortController.signal
        }).catch((err)=>{
            throw err;
        });
        if (onResponse) {
            try {
                await onResponse(response);
            } catch (err) {
                throw err;
            }
        }
        if (!response.ok) {
            throw new Error((_a = await response.text()) != null ? _a : "Failed to fetch the chat response.");
        }
        if (!response.body) {
            throw new Error("The response body is empty.");
        }
        let result = "";
        switch(streamProtocol){
            case "text":
                {
                    await processTextStream({
                        stream: response.body,
                        onTextPart: (chunk)=>{
                            result += chunk;
                            setCompletion(result);
                        }
                    });
                    break;
                }
            case "data":
                {
                    await processDataStream({
                        stream: response.body,
                        onTextPart (value) {
                            result += value;
                            setCompletion(result);
                        },
                        onDataPart (value) {
                            onData == null ? void 0 : onData(value);
                        },
                        onErrorPart (value) {
                            throw new Error(value);
                        }
                    });
                    break;
                }
            default:
                {
                    const exhaustiveCheck = streamProtocol;
                    throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);
                }
        }
        if (onFinish) {
            onFinish(prompt, result);
        }
        setAbortController(null);
        return result;
    } catch (err) {
        if (err.name === "AbortError") {
            setAbortController(null);
            return null;
        }
        if (err instanceof Error) {
            if (onError) {
                onError(err);
            }
        }
        setError(err);
    } finally{
        setLoading(false);
    }
}
// src/data-url.ts
function getTextFromDataUrl(dataUrl) {
    const [header, base64Content] = dataUrl.split(",");
    const mimeType = header.split(";")[0].split(":")[1];
    if (mimeType == null || base64Content == null) {
        throw new Error("Invalid data URL format");
    }
    try {
        return window.atob(base64Content);
    } catch (error) {
        throw new Error(`Error decoding data URL`);
    }
}
// src/extract-max-tool-invocation-step.ts
function extractMaxToolInvocationStep(toolInvocations) {
    return toolInvocations == null ? void 0 : toolInvocations.reduce((max, toolInvocation)=>{
        var _a;
        return Math.max(max, (_a = toolInvocation.step) != null ? _a : 0);
    }, 0);
}
// src/get-message-parts.ts
function getMessageParts(message) {
    var _a;
    return (_a = message.parts) != null ? _a : [
        ...message.toolInvocations ? message.toolInvocations.map((toolInvocation)=>({
                type: "tool-invocation",
                toolInvocation
            })) : [],
        ...message.reasoning ? [
            {
                type: "reasoning",
                reasoning: message.reasoning,
                details: [
                    {
                        type: "text",
                        text: message.reasoning
                    }
                ]
            }
        ] : [],
        ...message.content ? [
            {
                type: "text",
                text: message.content
            }
        ] : []
    ];
}
// src/fill-message-parts.ts
function fillMessageParts(messages) {
    return messages.map((message)=>({
            ...message,
            parts: getMessageParts(message)
        }));
}
// src/is-deep-equal-data.ts
function isDeepEqualData(obj1, obj2) {
    if (obj1 === obj2) return true;
    if (obj1 == null || obj2 == null) return false;
    if (typeof obj1 !== "object" && typeof obj2 !== "object") return obj1 === obj2;
    if (obj1.constructor !== obj2.constructor) return false;
    if (obj1 instanceof Date && obj2 instanceof Date) {
        return obj1.getTime() === obj2.getTime();
    }
    if (Array.isArray(obj1)) {
        if (obj1.length !== obj2.length) return false;
        for(let i = 0; i < obj1.length; i++){
            if (!isDeepEqualData(obj1[i], obj2[i])) return false;
        }
        return true;
    }
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    if (keys1.length !== keys2.length) return false;
    for (const key of keys1){
        if (!keys2.includes(key)) return false;
        if (!isDeepEqualData(obj1[key], obj2[key])) return false;
    }
    return true;
}
// src/prepare-attachments-for-request.ts
async function prepareAttachmentsForRequest(attachmentsFromOptions) {
    if (!attachmentsFromOptions) {
        return [];
    }
    if (globalThis.FileList && attachmentsFromOptions instanceof globalThis.FileList) {
        return Promise.all(Array.from(attachmentsFromOptions).map(async (attachment)=>{
            const { name, type } = attachment;
            const dataUrl = await new Promise((resolve, reject)=>{
                const reader = new FileReader();
                reader.onload = (readerEvent)=>{
                    var _a;
                    resolve((_a = readerEvent.target) == null ? void 0 : _a.result);
                };
                reader.onerror = (error)=>reject(error);
                reader.readAsDataURL(attachment);
            });
            return {
                name,
                contentType: type,
                url: dataUrl
            };
        }));
    }
    if (Array.isArray(attachmentsFromOptions)) {
        return attachmentsFromOptions;
    }
    throw new Error("Invalid attachments type");
}
// src/process-assistant-stream.ts
var NEWLINE2 = "\n".charCodeAt(0);
function concatChunks2(chunks, totalLength) {
    const concatenatedChunks = new Uint8Array(totalLength);
    let offset = 0;
    for (const chunk of chunks){
        concatenatedChunks.set(chunk, offset);
        offset += chunk.length;
    }
    chunks.length = 0;
    return concatenatedChunks;
}
async function processAssistantStream({ stream, onTextPart, onErrorPart, onAssistantMessagePart, onAssistantControlDataPart, onDataMessagePart }) {
    const reader = stream.getReader();
    const decoder = new TextDecoder();
    const chunks = [];
    let totalLength = 0;
    while(true){
        const { value } = await reader.read();
        if (value) {
            chunks.push(value);
            totalLength += value.length;
            if (value[value.length - 1] !== NEWLINE2) {
                continue;
            }
        }
        if (chunks.length === 0) {
            break;
        }
        const concatenatedChunks = concatChunks2(chunks, totalLength);
        totalLength = 0;
        const streamParts = decoder.decode(concatenatedChunks, {
            stream: true
        }).split("\n").filter((line)=>line !== "").map(parseAssistantStreamPart);
        for (const { type, value: value2 } of streamParts){
            switch(type){
                case "text":
                    await (onTextPart == null ? void 0 : onTextPart(value2));
                    break;
                case "error":
                    await (onErrorPart == null ? void 0 : onErrorPart(value2));
                    break;
                case "assistant_message":
                    await (onAssistantMessagePart == null ? void 0 : onAssistantMessagePart(value2));
                    break;
                case "assistant_control_data":
                    await (onAssistantControlDataPart == null ? void 0 : onAssistantControlDataPart(value2));
                    break;
                case "data_message":
                    await (onDataMessagePart == null ? void 0 : onDataMessagePart(value2));
                    break;
                default:
                    {
                        const exhaustiveCheck = type;
                        throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);
                    }
            }
        }
    }
}
;
;
function zodSchema(zodSchema2, options) {
    var _a;
    const useReferences = (_a = options == null ? void 0 : options.useReferences) != null ? _a : false;
    return jsonSchema((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2d$to$2d$json$2d$schema$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])(zodSchema2, {
        $refStrategy: useReferences ? "root" : "none",
        target: "jsonSchema7"
    }), {
        validate: (value)=>{
            const result = zodSchema2.safeParse(value);
            return result.success ? {
                success: true,
                value: result.data
            } : {
                success: false,
                error: result.error
            };
        }
    });
}
// src/schema.ts
var schemaSymbol = Symbol.for("vercel.ai.schema");
function jsonSchema(jsonSchema2, { validate } = {}) {
    return {
        [schemaSymbol]: true,
        _type: void 0,
        // should never be used directly
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ai$2d$sdk$2f$provider$2d$utils$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validatorSymbol"]]: true,
        jsonSchema: jsonSchema2,
        validate
    };
}
function isSchema(value) {
    return typeof value === "object" && value !== null && schemaSymbol in value && value[schemaSymbol] === true && "jsonSchema" in value && "validate" in value;
}
function asSchema(schema) {
    return isSchema(schema) ? schema : zodSchema(schema);
}
// src/should-resubmit-messages.ts
function shouldResubmitMessages({ originalMaxToolInvocationStep, originalMessageCount, maxSteps, messages }) {
    var _a;
    const lastMessage = messages[messages.length - 1];
    return(// check if the feature is enabled:
    maxSteps > 1 && // ensure there is a last message:
    lastMessage != null && // ensure we actually have new steps (to prevent infinite loops in case of errors):
    (messages.length > originalMessageCount || extractMaxToolInvocationStep(lastMessage.toolInvocations) !== originalMaxToolInvocationStep) && // check that next step is possible:
    isAssistantMessageWithCompletedToolCalls(lastMessage) && // limit the number of automatic steps:
    ((_a = extractMaxToolInvocationStep(lastMessage.toolInvocations)) != null ? _a : 0) < maxSteps);
}
function isAssistantMessageWithCompletedToolCalls(message) {
    if (message.role !== "assistant") {
        return false;
    }
    const lastStepStartIndex = message.parts.reduce((lastIndex, part, index)=>{
        return part.type === "step-start" ? index : lastIndex;
    }, -1);
    const lastStepToolInvocations = message.parts.slice(lastStepStartIndex + 1).filter((part)=>part.type === "tool-invocation");
    return lastStepToolInvocations.length > 0 && lastStepToolInvocations.every((part)=>"result" in part.toolInvocation);
}
// src/update-tool-call-result.ts
function updateToolCallResult({ messages, toolCallId, toolResult: result }) {
    var _a;
    const lastMessage = messages[messages.length - 1];
    const invocationPart = lastMessage.parts.find((part)=>part.type === "tool-invocation" && part.toolInvocation.toolCallId === toolCallId);
    if (invocationPart == null) {
        return;
    }
    const toolResult = {
        ...invocationPart.toolInvocation,
        state: "result",
        result
    };
    invocationPart.toolInvocation = toolResult;
    lastMessage.toolInvocations = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.map((toolInvocation)=>toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation);
}
;
 //# sourceMappingURL=index.mjs.map
}}),

};

//# sourceMappingURL=node_modules_%40ai-sdk_7c751042._.js.map