---
title: Features
description: Overview of Onlook's features
---

# Onlook Features

Onlook provides a range of features to help designers and developers work together more efficiently.

![Features Overview](/images/features-overview.png)
*Onlook offers powerful features to streamline your design-to-code workflow*

## Key Features

<Cards>
  <Card title="Visual Editor" href="/docs/features/visual-editor" />
  <Card title="Code Integration" href="/docs/features/code-integration" />
  <Card title="AI Assistance" href="/docs/features/ai-assistance" />
  <Card title="Figma to Onlook" href="/docs/features/figma-to-onlook" />
  <Card title="Theme System" href="/docs/features/theme-system" />
  <Card title="Deployment" href="/docs/features/deployment" />
</Cards>

## Visual Editor

The Visual Editor allows you to edit React components visually, similar to design tools like Figma. You can:

- Drag and drop components
- Resize and position elements
- Edit text and images
- Apply styles using Tailwind CSS

![Visual Editor](/images/visual-editor.png)
*Onlook's Visual Editor provides a familiar design tool interface*

## Code Integration

Onlook integrates seamlessly with your code, allowing you to:

- See code changes in real-time
- Edit code directly
- Import and export components
- Integrate with your existing workflow

![Code Integration](/images/code-integration.png)
*Watch your design changes reflect immediately in the code*

## AI Assistance

Onlook's AI assistance helps you:

- Generate components from descriptions
- Get suggestions for improvements
- Convert designs to code
- Debug issues

![AI Assistance](/images/ai-assistance.png)
*Let AI help you create and improve your designs*

## Figma to Onlook

Import your Figma designs directly into Onlook to:

- Convert designs to working React components
- Maintain design fidelity
- Make designs interactive
- Connect to data sources

![Figma to Onlook](/images/figma-to-onlook.png)
*Seamlessly transition from Figma designs to working code*
