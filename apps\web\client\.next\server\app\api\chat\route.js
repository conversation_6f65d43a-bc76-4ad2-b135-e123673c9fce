const CHUNK_PUBLIC_PATH = "server/app/api/chat/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_formdata-node_lib_esm_fileFromPath_700fb9e9.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__41020097._.js");
runtime.loadChunk("server/chunks/node_modules_next_dist_b8dcab8d._.js");
runtime.loadChunk("server/chunks/node_modules_openai_a822ecb7._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_zod_dist_esm_54374b3b._.js");
runtime.loadChunk("server/chunks/node_modules_color-namer_16226826._.js");
runtime.loadChunk("server/chunks/node_modules_culori_src_046c50d0._.js");
runtime.loadChunk("server/chunks/node_modules_@babel_standalone_babel_7851784c.js");
runtime.loadChunk("server/chunks/node_modules_tldts_dist_es6_28064b83._.js");
runtime.loadChunk("server/chunks/node_modules_ai_dist_index_mjs_1ce7e475._.js");
runtime.loadChunk("server/chunks/node_modules_@ai-sdk_7c751042._.js");
runtime.loadChunk("server/chunks/node_modules_90dc6ea4._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/client/.next-internal/server/app/api/chat/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/web/client/src/app/api/chat/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/web/client/src/app/api/chat/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
