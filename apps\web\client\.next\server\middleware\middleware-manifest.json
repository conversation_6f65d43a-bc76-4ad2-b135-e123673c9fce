{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_133e7ea9._.js", "server/edge/chunks/node_modules_zod_dist_esm_56e6e3e4._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_587cc249._.js", "server/edge/chunks/[root-of-the-server]__1274d79d._.js", "server/edge/chunks/apps_web_client_edge-wrapper_0447e01a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KVeR4tqK3NZdEsroTwj9ZMkh+iVQvy58fbzb3fepOPw=", "__NEXT_PREVIEW_MODE_ID": "83b8b546b37b421baa18cb3206bce9e4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4288e683b117729660d05072db2493a2a5f5f84ee2677753693a64ec419b3d7e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cc21942991eb4a1db12c6372ecb9e8d83b0e9e4b59336cb85a35f80dfdb4058d"}}}, "instrumentation": null, "functions": {}}