{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_133e7ea9._.js", "server/edge/chunks/node_modules_zod_dist_esm_56e6e3e4._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_587cc249._.js", "server/edge/chunks/[root-of-the-server]__1274d79d._.js", "server/edge/chunks/apps_web_client_edge-wrapper_0447e01a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KVeR4tqK3NZdEsroTwj9ZMkh+iVQvy58fbzb3fepOPw=", "__NEXT_PREVIEW_MODE_ID": "65471ed0435662d7dee251f343478f8c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1b23b0e85fba6f9330830d7d0a78e177b5207487fec638590746f9906effc164", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "85afb47ddf389202dab123704d224fc5d17e8650416d6b013f5ff1c0aa3da1c0"}}}, "instrumentation": null, "functions": {}}